using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.Threading;

namespace SSIC.Infrastructure.Startups.Endpoints
{
    /// <summary>
    /// 固定端点数据源，解决热重载问题
    /// </summary>
    public class FixedEndpointDataSource : EndpointDataSource
    {
        private readonly List<Endpoint> _endpoints = new List<Endpoint>();
        private readonly object _lock = new object();
        private CancellationTokenSource _cts = new CancellationTokenSource();
        
        // 通过这个字段强制更新版本号，确保路由系统能感知变化
        private volatile int _version = 0;

        public override IReadOnlyList<Endpoint> Endpoints 
        { 
            get 
            {
                // 先获取一次_version的值，确保编译器不会优化
                var currentVersion = _version;
                return _endpoints.AsReadOnly(); 
            } 
        }

        public void AddEndpoint(Endpoint endpoint)
        {
            lock (_lock)
            {
                _endpoints.Add(endpoint);
                NotifyChange();
            }
        }

        public override IChangeToken GetChangeToken()
        {
            // 每次获取变更令牌时，返回可取消的令牌
            return new CancellationChangeToken(_cts.Token);
        }

        public void NotifyChange()
        {
            lock (_lock)
            {
                // 递增版本号
                _version++;
                
                // 取消旧令牌，触发变化通知
                var oldCts = Interlocked.Exchange(ref _cts, new CancellationTokenSource());
                try
                {
                    oldCts.Cancel();
                    oldCts.Dispose();
                }
                catch
                {
                    // 忽略取消过程中的错误
                }
            }
        }

        public void Clear()
        {
            lock (_lock)
            {
                _endpoints.Clear();
                NotifyChange();
            }
        }

        public void AddRange(IEnumerable<Endpoint> endpoints)
        {
            lock (_lock)
            {
                _endpoints.AddRange(endpoints);
                NotifyChange();
            }
        }
        
        /// <summary>
        /// 强制更新数据源并通知监听者
        /// </summary>
        public void ForceRefresh()
        {
            lock (_lock)
            {
                // 递增版本号
                _version++;
                
                // 取消旧令牌，创建新令牌
                var oldCts = Interlocked.Exchange(ref _cts, new CancellationTokenSource());
                try
                {
                    oldCts.Cancel();
                    oldCts.Dispose();
                }
                catch
                {
                    // 忽略取消过程中的错误
                }
            }
        }
    }
} 