﻿using Dapr.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Dapr
{
    public static class DaprServiceCollectionExtensions
    {
        public static void AddDaprClientProvider(this IServiceCollection services)
        {
            var configuration = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();

            services.AddDaprClient();
            services.AddSingleton<DaprClientProvider>();

        }

    }
}