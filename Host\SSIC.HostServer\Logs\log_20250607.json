{"@t":"2025-06-07T03:23:19.5699187Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:19.6580192Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:19.6584612Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:19.6923081Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:19.6926343Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:19.7512289Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:19.7662522Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:19.7663951Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:19.7684807Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:19.7707633Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:19.7804352Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:19.7809314Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:19.7810586Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:19.7810998Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:20.1274462Z","@mt":"Now listening on: {address}","address":"https://localhost:56299","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:20.1284931Z","@mt":"Now listening on: {address}","address":"http://localhost:56300","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:20.1697149Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:20.1699447Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:20.1700181Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:25.7260268Z","@mt":"首次请求触发端点刷新","@tr":"be376bbbff0a5f4dbcdc4c55d11f67ad","@sp":"94ac18de882a8844","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HND5EAKKNDO0:00000001","RequestPath":"/dapr/config","ConnectionId":"0HND5EAKKNDO0","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:23:25.7340728Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"be376bbbff0a5f4dbcdc4c55d11f67ad","@sp":"94ac18de882a8844","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HND5EAKKNDO0:00000001","RequestPath":"/dapr/config","ConnectionId":"0HND5EAKKNDO0","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:24.9552432Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.0115724Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.0324085Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.1569173Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.1572648Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.2210206Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.2376923Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.2379456Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.2413423Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.2554981Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.2577080Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.2582254Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.2583770Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.2584473Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.8559970Z","@mt":"Now listening on: {address}","address":"https://localhost:60154","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.8579668Z","@mt":"Now listening on: {address}","address":"http://localhost:60155","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.9251133Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.9254596Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:25.9257084Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:26.2933609Z","@mt":"首次请求触发端点刷新","@tr":"837d774f1de896e7af1e7a1e79ed360a","@sp":"aed9a174e8dd0cf9","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HND5EMCNPKK3:00000001","RequestPath":"/dapr/config","ConnectionId":"0HND5EMCNPKK3","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:44:26.2995506Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"837d774f1de896e7af1e7a1e79ed360a","@sp":"aed9a174e8dd0cf9","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HND5EMCNPKK3:00000001","RequestPath":"/dapr/config","ConnectionId":"0HND5EMCNPKK3","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:47:12.6897021Z","@mt":"手动API触发端点刷新完成，发现 {Count} 个模块","@tr":"1fce47e61eb559416687973a5a87fbf0","@sp":"8a8bc39b3e581358","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HND5EMCNPKK5:0000000F","RequestPath":"/api/endpoints/refresh","ConnectionId":"0HND5EMCNPKK5","MachineName":"DESKTOP-O211UJ1","ThreadId":32,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:47:15.0372394Z","@mt":"手动API触发端点刷新完成，发现 {Count} 个模块","@tr":"321196f455a1d316e06b625a9e002098","@sp":"77a73be659d7c63c","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HND5EMCNPKK5:00000011","RequestPath":"/api/endpoints/refresh","ConnectionId":"0HND5EMCNPKK5","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:47:15.5578408Z","@mt":"手动API触发端点刷新完成，发现 {Count} 个模块","@tr":"6df4c1ebd18f358c3c43ee44c0e6d1a3","@sp":"0e3d118826610f92","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HND5EMCNPKK5:00000013","RequestPath":"/api/endpoints/refresh","ConnectionId":"0HND5EMCNPKK5","MachineName":"DESKTOP-O211UJ1","ThreadId":32,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:47:15.7843470Z","@mt":"手动API触发端点刷新完成，发现 {Count} 个模块","@tr":"b337721b4082ed8fc9b3a810e14db704","@sp":"8c2431bf79da5c11","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HND5EMCNPKK5:00000015","RequestPath":"/api/endpoints/refresh","ConnectionId":"0HND5EMCNPKK5","MachineName":"DESKTOP-O211UJ1","ThreadId":32,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:47:20.2070495Z","@mt":"已手动添加/api/Hello路由","@tr":"9c4c22a9aad00ffb69e7204108acec6d","@sp":"2022df3d35dd0582","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HND5EMCNPKK5:00000017","RequestPath":"/api/endpoints/map-module-route","ConnectionId":"0HND5EMCNPKK5","MachineName":"DESKTOP-O211UJ1","ThreadId":32,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:47:20.9959443Z","@mt":"已手动添加/api/Hello路由","@tr":"ea03918b12d24fccda55d11bef119649","@sp":"3d2a81b1a898e382","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HND5EMCNPKK5:00000019","RequestPath":"/api/endpoints/map-module-route","ConnectionId":"0HND5EMCNPKK5","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:47:21.4993203Z","@mt":"已手动添加/api/Hello路由","@tr":"b084b69e74ca8fc42c4605d6112e528e","@sp":"9438b6e11d7ce419","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HND5EMCNPKK5:0000001B","RequestPath":"/api/endpoints/map-module-route","ConnectionId":"0HND5EMCNPKK5","MachineName":"DESKTOP-O211UJ1","ThreadId":30,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:47:21.8437278Z","@mt":"已手动添加/api/Hello路由","@tr":"68ff30e928b9ae7180fd550795f9b76c","@sp":"b3ad8055af3f4bbd","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HND5EMCNPKK5:0000001D","RequestPath":"/api/endpoints/map-module-route","ConnectionId":"0HND5EMCNPKK5","MachineName":"DESKTOP-O211UJ1","ThreadId":30,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:50:27.8879896Z","@mt":"已手动添加/api/Hello路由","@tr":"0359d14271a4c39e46aad9f26e5ca1dc","@sp":"4d83f9866ffed9cb","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HND5EMCNPKK5:00000039","RequestPath":"/api/endpoints/map-module-route","ConnectionId":"0HND5EMCNPKK5","MachineName":"DESKTOP-O211UJ1","ThreadId":36,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:50:28.7419119Z","@mt":"已手动添加/api/Hello路由","@tr":"37a2334136868c3bb8dda3bf8579abb1","@sp":"24150b9b3090a6ad","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HND5EMCNPKK5:0000003B","RequestPath":"/api/endpoints/map-module-route","ConnectionId":"0HND5EMCNPKK5","MachineName":"DESKTOP-O211UJ1","ThreadId":35,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:50:28.9755780Z","@mt":"已手动添加/api/Hello路由","@tr":"149f47306e212a5ddd302295ecfea8c2","@sp":"2d53b23e269d646a","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HND5EMCNPKK5:0000003D","RequestPath":"/api/endpoints/map-module-route","ConnectionId":"0HND5EMCNPKK5","MachineName":"DESKTOP-O211UJ1","ThreadId":35,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:50:32.1586949Z","@mt":"手动API触发端点刷新完成，发现 {Count} 个模块","@tr":"9c83b12e11c60016afa51214160baca9","@sp":"ed0f5e4c957d3cd4","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HND5EMCNPKK5:0000003F","RequestPath":"/api/endpoints/refresh","ConnectionId":"0HND5EMCNPKK5","MachineName":"DESKTOP-O211UJ1","ThreadId":34,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.3471765Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.3782860Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.3893564Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.4687941Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.4690337Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.5511208Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.5727994Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.5729251Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.5752260Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.5788840Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.5838733Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.5842052Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.5842845Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.5843149Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.9051088Z","@mt":"Now listening on: {address}","address":"https://localhost:62185","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.9059986Z","@mt":"Now listening on: {address}","address":"http://localhost:62186","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.9453227Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.9455348Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:36.9456442Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:37.8323123Z","@mt":"首次请求触发端点刷新","@tr":"34f94f69262c9ced951e1c704c3d456a","@sp":"9b518684634c7766","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HND5ERH2ROVH:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HND5ERH2ROVH","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-07T03:53:37.8375475Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"34f94f69262c9ced951e1c704c3d456a","@sp":"9b518684634c7766","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HND5ERH2ROVH:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HND5ERH2ROVH","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
