{"@t":"2025-07-24T17:39:28.0276629Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:39:28.1918985Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:39:28.2894700Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:39:28.2976970Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:39:28.7107032Z","@mt":"Now listening on: {address}","address":"https://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:39:28.7161220Z","@mt":"Now listening on: {address}","address":"http://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:39:28.8193907Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:39:28.8215730Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:39:28.8237267Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:39:29.9058840Z","@mt":"首次请求触发端点刷新","@tr":"b9f66a8dd0f86d6d2157975a73da8e96","@sp":"49197d13499ad3e6","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR457S5B5:00000001","RequestPath":"/","ConnectionId":"0HNEAR457S5B5","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:39:29.9166654Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"b9f66a8dd0f86d6d2157975a73da8e96","@sp":"49197d13499ad3e6","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR457S5B5:00000001","RequestPath":"/","ConnectionId":"0HNEAR457S5B5","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:39:45.6724026Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"33b6770339203a978693cb853da7f9f3","@sp":"0072a587d52862d3","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAR457S5B5:0000000D","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAR457S5B5","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:39:45.6791797Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"33b6770339203a978693cb853da7f9f3","@sp":"0072a587d52862d3","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAR457S5B5:0000000D","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAR457S5B5","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:39:45.6965152Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"33b6770339203a978693cb853da7f9f3","@sp":"0072a587d52862d3","PathCount":11,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAR457S5B5:0000000D","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAR457S5B5","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:46.3995805Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:46.4974555Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:46.6047596Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:46.6094687Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:46.9530484Z","@mt":"Now listening on: {address}","address":"https://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:46.9571851Z","@mt":"Now listening on: {address}","address":"http://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:47.0215230Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:47.0239787Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:47.0267833Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:49.5128274Z","@mt":"首次请求触发端点刷新","@tr":"51f99ce47785ed9f18f5283dbf0dbebb","@sp":"8c706a54fe7a8734","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR4SJK27D:00000001","RequestPath":"/","ConnectionId":"0HNEAR4SJK27D","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:49.5253777Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"51f99ce47785ed9f18f5283dbf0dbebb","@sp":"8c706a54fe7a8734","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR4SJK27D:00000001","RequestPath":"/","ConnectionId":"0HNEAR4SJK27D","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:07.8515806Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:07.9801159Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:08.1257769Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:08.1296846Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:08.5682412Z","@mt":"Now listening on: {address}","address":"https://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:08.5715876Z","@mt":"Now listening on: {address}","address":"http://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:08.6572770Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:08.6608684Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:08.6632305Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:10.4694957Z","@mt":"首次请求触发端点刷新","@tr":"f8ef8cc015d07d2ec2e16338174208fe","@sp":"4c9705a642228bcd","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR7AHBRLG:00000001","RequestPath":"/","ConnectionId":"0HNEAR7AHBRLG","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:10.4903123Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"f8ef8cc015d07d2ec2e16338174208fe","@sp":"4c9705a642228bcd","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR7AHBRLG:00000001","RequestPath":"/","ConnectionId":"0HNEAR7AHBRLG","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:16.8393182Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"10bdf1b1c6269f58978646c9c545bceb","@sp":"a57d826a4bb5fa7c","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAR7AHBRLG:0000000D","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAR7AHBRLG","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:16.8442702Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"10bdf1b1c6269f58978646c9c545bceb","@sp":"a57d826a4bb5fa7c","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAR7AHBRLG:0000000D","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAR7AHBRLG","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:16.8555434Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"10bdf1b1c6269f58978646c9c545bceb","@sp":"a57d826a4bb5fa7c","PathCount":11,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAR7AHBRLG:0000000D","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAR7AHBRLG","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:39.3255492Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:39.5953628Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:39.8603991Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:39.9567342Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:40.5556741Z","@mt":"Now listening on: {address}","address":"https://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:40.7167044Z","@mt":"Now listening on: {address}","address":"http://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:40.8537343Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:40.8582796Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:40.8603365Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:42.2626634Z","@mt":"首次请求触发端点刷新","@tr":"987072f36c043623c617fc435575c87a","@sp":"f853f4e2b5eb9933","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEBCALS35GM:00000001","RequestPath":"/","ConnectionId":"0HNEBCALS35GM","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:42.2750582Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"987072f36c043623c617fc435575c87a","@sp":"f853f4e2b5eb9933","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEBCALS35GM:00000001","RequestPath":"/","ConnectionId":"0HNEBCALS35GM","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:57.6726392Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"376d4f50a205ed4b4ee4def9a596df89","@sp":"42c1d00300e3c0a6","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEBCALS35GM:0000000D","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEBCALS35GM","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:58.0517774Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"376d4f50a205ed4b4ee4def9a596df89","@sp":"42c1d00300e3c0a6","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEBCALS35GM:0000000D","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEBCALS35GM","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:04:58.0787131Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"376d4f50a205ed4b4ee4def9a596df89","@sp":"42c1d00300e3c0a6","PathCount":12,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEBCALS35GM:0000000D","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEBCALS35GM","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:42.6968160Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:42.8335911Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:42.9534881Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:42.9584445Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:43.2960295Z","@mt":"Now listening on: {address}","address":"https://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:43.3001277Z","@mt":"Now listening on: {address}","address":"http://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:43.5117467Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:43.5140715Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:43.5155077Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:43.9806429Z","@mt":"首次请求触发端点刷新","@tr":"3859bb93749a981b88fcdc56dcbbe4a7","@sp":"6447c0b183ebcb24","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEBCLSAJ47F:00000001","RequestPath":"/","ConnectionId":"0HNEBCLSAJ47F","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:43.9970994Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"3859bb93749a981b88fcdc56dcbbe4a7","@sp":"6447c0b183ebcb24","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEBCLSAJ47F:00000001","RequestPath":"/","ConnectionId":"0HNEBCLSAJ47F","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:49.5770284Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"b58746af1725cfadd5b7f35fc43bc6cc","@sp":"378201e84a3418ce","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEBCLSAJ47F:0000000D","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEBCLSAJ47F","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:49.5829645Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"b58746af1725cfadd5b7f35fc43bc6cc","@sp":"378201e84a3418ce","Count":1,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEBCLSAJ47F:0000000D","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEBCLSAJ47F","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:49.5967356Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"b58746af1725cfadd5b7f35fc43bc6cc","@sp":"378201e84a3418ce","PathCount":12,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEBCLSAJ47F:0000000D","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEBCLSAJ47F","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:31:09.8985630Z","@mt":"热插拔服务未注册，跳过初始化","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:31:09.9519453Z","@mt":"在UseEndpoints中发现 {Count} 个模块程序集","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:31:10.0248502Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:31:10.0265481Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:31:10.2182958Z","@mt":"Now listening on: {address}","address":"https://localhost:57850","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:31:10.2203141Z","@mt":"Now listening on: {address}","address":"http://localhost:57851","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:31:10.2218627Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:31:10.2230472Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:31:10.2242161Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
