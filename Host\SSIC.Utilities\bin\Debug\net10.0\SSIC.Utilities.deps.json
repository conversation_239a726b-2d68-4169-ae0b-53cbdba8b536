{"runtimeTarget": {"name": ".NETCoreApp,Version=v10.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v10.0": {"SSIC.Utilities/1.0.0": {"dependencies": {"Masuit.Tools.Core": "2025.1.7", "Microsoft.DependencyValidation.Analyzers": "0.11.0", "QRCoder": "1.6.0"}, "runtime": {"SSIC.Utilities.dll": {}}}, "AngleSharp/1.3.0-beta.468": {"runtime": {"lib/net8.0/AngleSharp.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.3.0.0"}}}, "AngleSharp.Css/1.0.0-beta.151": {"dependencies": {"AngleSharp": "1.3.0-beta.468"}, "runtime": {"lib/net8.0/AngleSharp.Css.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Castle.Core/5.1.1": {"dependencies": {"System.Diagnostics.EventLog": "9.0.2"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.1.0"}}}, "DnsClient/1.8.0": {"runtime": {"lib/net8.0/DnsClient.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}}}, "Masuit.Tools.Abstractions/2025.1.7": {"dependencies": {"AngleSharp": "1.3.0-beta.468", "AngleSharp.Css": "1.0.0-beta.151", "Castle.Core": "5.1.1", "DnsClient": "1.8.0", "Microsoft.Extensions.Configuration.Json": "9.0.2", "Newtonsoft.Json": "13.0.3", "SharpCompress": "0.39.0", "SixLabors.ImageSharp": "3.1.7", "SixLabors.ImageSharp.Drawing": "2.1.5", "System.Diagnostics.PerformanceCounter": "9.0.2", "System.Management": "9.0.2"}, "runtime": {"lib/net9.0/Masuit.Tools.Abstractions.dll": {"assemblyVersion": "2.5.9.0", "fileVersion": "2.5.9.0"}}}, "Masuit.Tools.Core/2025.1.7": {"dependencies": {"Masuit.Tools.Abstractions": "2025.1.7", "Microsoft.EntityFrameworkCore": "9.0.2"}, "runtime": {"lib/net9.0/Masuit.Tools.Core.dll": {"assemblyVersion": "2.5.9.0", "fileVersion": "2.5.9.1"}}}, "Microsoft.DependencyValidation.Analyzers/0.11.0": {}, "Microsoft.EntityFrameworkCore/9.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.2", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.2.0", "fileVersion": "9.0.225.6701"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.2": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "9.0.2.0", "fileVersion": "9.0.225.6701"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.2": {}, "Microsoft.Extensions.Caching.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Caching.Memory/9.0.2": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Configuration/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.FileProviders.Physical": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Configuration.Json/9.0.2": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.DependencyInjection/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.2": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.2": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.FileSystemGlobbing": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.2": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Logging/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Options/9.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Microsoft.Extensions.Primitives/9.0.2": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "QRCoder/1.6.0": {"runtime": {"lib/net6.0/QRCoder.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}}}, "SharpCompress/0.39.0": {"dependencies": {"ZstdSharp.Port": "0.8.4"}, "runtime": {"lib/net8.0/SharpCompress.dll": {"assemblyVersion": "0.39.0.0", "fileVersion": "0.39.0.0"}}}, "SixLabors.Fonts/2.0.8": {"runtime": {"lib/net6.0/SixLabors.Fonts.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.8.0"}}}, "SixLabors.ImageSharp/3.1.7": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.1.7.0"}}}, "SixLabors.ImageSharp.Drawing/2.1.5": {"dependencies": {"SixLabors.Fonts": "2.0.8", "SixLabors.ImageSharp": "3.1.7"}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.Drawing.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.1.5.0"}}}, "System.CodeDom/9.0.2": {"runtime": {"lib/net9.0/System.CodeDom.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "System.Configuration.ConfigurationManager/9.0.2": {"dependencies": {"System.Diagnostics.EventLog": "9.0.2", "System.Security.Cryptography.ProtectedData": "9.0.2"}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "System.Diagnostics.EventLog/9.0.2": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.0", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "System.Diagnostics.PerformanceCounter/9.0.2": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.2"}, "runtime": {"lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "System.Management/9.0.2": {"dependencies": {"System.CodeDom": "9.0.2"}, "runtime": {"lib/net9.0/System.Management.dll": {"assemblyVersion": "9.0.0.2", "fileVersion": "9.0.225.6610"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.2", "fileVersion": "9.0.225.6610"}}}, "System.Security.Cryptography.ProtectedData/9.0.2": {"runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.225.6610"}}}, "ZstdSharp.Port/0.8.4": {"runtime": {"lib/net9.0/ZstdSharp.dll": {"assemblyVersion": "0.8.4.0", "fileVersion": "0.8.4.0"}}}}}, "libraries": {"SSIC.Utilities/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AngleSharp/1.3.0-beta.468": {"type": "package", "serviceable": true, "sha512": "sha512-3uIK3EUQbQ9Wl9ufL+ysh3jd5nx4NKZgjAZhjqKbWr8o3oPiBBGzEXSEdLYeh4dlt+PiuO1mjaM+wVtJQeVDTA==", "path": "anglesharp/1.3.0-beta.468", "hashPath": "anglesharp.1.3.0-beta.468.nupkg.sha512"}, "AngleSharp.Css/1.0.0-beta.151": {"type": "package", "serviceable": true, "sha512": "sha512-oEnqXQcwpc/kkUIi2rxWHfFrmKlcJFpZZZjhEIHVc+aEJFo3U+5cptOHByQh+FW0PCW6ssVJ+GGBgbgyN8YPiw==", "path": "anglesharp.css/1.0.0-beta.151", "hashPath": "anglesharp.css.1.0.0-beta.151.nupkg.sha512"}, "Castle.Core/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "path": "castle.core/5.1.1", "hashPath": "castle.core.5.1.1.nupkg.sha512"}, "DnsClient/1.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-RRwtaCXkXWsx0mmsReGDqCbRLtItfUbkRJlet1FpdciVhyMGKcPd57T1+8Jki9ojHlq9fntVhXQroOOgRak8DQ==", "path": "dnsclient/1.8.0", "hashPath": "dnsclient.1.8.0.nupkg.sha512"}, "Masuit.Tools.Abstractions/2025.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-KTvP4GXXHwRTRdrbOK8QxGi1EdyhKV3geUvPJr9L7TFevdxB1yoT66qtNOJlTtUdz7RChPYf6yTBoPX4+JcT4g==", "path": "masuit.tools.abstractions/2025.1.7", "hashPath": "masuit.tools.abstractions.2025.1.7.nupkg.sha512"}, "Masuit.Tools.Core/2025.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-azDIcg0e3ad3HQysim8/3Janiy57hB259G99qJuCc0rEj80cI3cb0SrbOCKeLTEE3opTSarK9+pj21hvr3AtMQ==", "path": "masuit.tools.core/2025.1.7", "hashPath": "masuit.tools.core.2025.1.7.nupkg.sha512"}, "Microsoft.DependencyValidation.Analyzers/0.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-3xBVhMsM4o9cDpsvmlv78T1FG+cTH8i6bjl1Irk6hoIwEc6Ekoz4mKAR6Hn5M/2G2ZsHNrYBNwGkgF7aiw74UA==", "path": "microsoft.dependencyvalidation.analyzers/0.11.0", "hashPath": "microsoft.dependencyvalidation.analyzers.0.11.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON><PERSON><PERSON>uybgcpW32y985eOYxSoZ9IiL0UTYQlY0y1Pt1iHAnpZj/dQHREpSpry1RNvk8YjAeoAkWFdem5conqB9zQ==", "path": "microsoft.entityframeworkcore/9.0.2", "hashPath": "microsoft.entityframeworkcore.9.0.2.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-oVSjNSIYHsk0N66eqAWgDcyo9etEFbUswbz7SmlYR6nGp05byHrJAYM5N8U2aGWJWJI6WvIC2e4TXJgH6GZ6HQ==", "path": "microsoft.entityframeworkcore.abstractions/9.0.2", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-w4jzX7XI+L3erVGzbHXpx64A3QaLXxqG3f1vPpGYYZGpxOIHkh7e4iLLD7cq4Ng1vjkwzWl5ZJp0Kj/nHsgFYg==", "path": "microsoft.entityframeworkcore.analyzers/9.0.2", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-a7QhA25n+BzSM5r5d7JznfyluMBGI7z3qyLlFviZ1Eiqv6DdiK27sLZdP/rpYirBM6UYAKxu5TbmfhIy13GN9A==", "path": "microsoft.extensions.caching.abstractions/9.0.2", "hashPath": "microsoft.extensions.caching.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-AlEfp0DMz8E1h1Exi8LBrUCNmCYcGDfSM4F/uK1D1cYx/R3w0LVvlmjICqxqXTsy7BEZaCf5leRZY2FuPEiFaw==", "path": "microsoft.extensions.caching.memory/9.0.2", "hashPath": "microsoft.extensions.caching.memory.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-EBZW+u96tApIvNtjymXEIS44tH0I/jNwABHo4c33AchWOiDWCq2rL3klpnIo+xGrxoVGJzPDISV6hZ+a9C9SzQ==", "path": "microsoft.extensions.configuration/9.0.2", "hashPath": "microsoft.extensions.configuration.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-I0O/270E/lUNqbBxlRVjxKOMZyYjP88dpEgQTveml+h2lTzAP4vbawLVwjS9SC7lKaU893bwyyNz0IVJYsm9EA==", "path": "microsoft.extensions.configuration.abstractions/9.0.2", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-tPgue19a0G+8Vb1ShCj4PmhjvVaEOfFb1L89WCr5aEpY1JUgIuYWsfELKf92Njwg53o4C+yWbE4UqbyQtLpKTg==", "path": "microsoft.extensions.configuration.fileextensions/9.0.2", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-u2eIYagO91VmRbKYQ5pmElWC6JWX7GPQbP57EX09zzFcI1ZMPDCykr07ikPB4ecgBZzG+UAhTcViTLe0gSF4WQ==", "path": "microsoft.extensions.configuration.json/9.0.2", "hashPath": "microsoft.extensions.configuration.json.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-ZffbJrskOZ40JTzcTyKwFHS5eACSWp2bUQBBApIgGV+es8RaTD4OxUG7XxFr3RIPLXtYQ1jQzF2DjKB5fZn7Qg==", "path": "microsoft.extensions.dependencyinjection/9.0.2", "hashPath": "microsoft.extensions.dependencyinjection.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-MNe7GSTBf3jQx5vYrXF0NZvn6l7hUKF6J54ENfAgCO8y6xjN1XUmKKWG464LP2ye6QqDiA1dkaWEZBYnhoZzjg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-IcOBmTlr2jySswU+3x8c3ql87FRwTVPQgVKaV5AXzPT5u0VItfNU8SMbESpdSp5STwxT/1R99WYszgHWsVkzhg==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.2", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-WcPkJx/OAaXG5xHvxYsoLY8qGsyCfHWsbDJtfMtHRWtceF/EmqAsqkHYsouh82gjxdZwfySvj3nGVi8AkwlYhA==", "path": "microsoft.extensions.fileproviders.physical/9.0.2", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-wAjk+6rvvU4WesskJ6rJX1FYL/S9zvnpqMai/pXb07+gtXpO7DhFfuKzYHwkKN3HAUq2W4CD+YLYenHwAS3DCA==", "path": "microsoft.extensions.filesystemglobbing/9.0.2", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-loV/0UNpt2bD+6kCDzFALVE63CDtqzPeC0LAetkdhiEr/tTNbvOlQ7CBResH7BQBd3cikrwiBfaHdyHMFUlc2g==", "path": "microsoft.extensions.logging/9.0.2", "hashPath": "microsoft.extensions.logging.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dV9s2Lamc8jSaqhl2BQSPn/AryDIH2sSbQUyLitLXV0ROmsb+SROnn2cH939JFbsNrnf3mIM3GNRKT7P0ldwLg==", "path": "microsoft.extensions.logging.abstractions/9.0.2", "hashPath": "microsoft.extensions.logging.abstractions.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-zr98z+AN8+isdmDmQRuEJ/DAKZGUTHmdv3t0ZzjHvNqvA44nAgkXE9kYtfoN6581iALChhVaSw2Owt+Z2lVbkQ==", "path": "microsoft.extensions.options/9.0.2", "hashPath": "microsoft.extensions.options.9.0.2.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-puBMtKe/wLuYa7H6docBkLlfec+h8L35DXqsDKKJgW0WY5oCwJ3cBJKcDaZchv6knAyqOMfsl6VUbaR++E5LXA==", "path": "microsoft.extensions.primitives/9.0.2", "hashPath": "microsoft.extensions.primitives.9.0.2.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "QRCoder/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-XmPA81eo+oRxBuyVdswsSkTGTE1d3thfF11Z1PdD7oB56A6HU4G4AAOdySmGRMb/cljwlFTMWUtosGEnwpS6GA==", "path": "qrcoder/1.6.0", "hashPath": "qrcoder.1.6.0.nupkg.sha512"}, "SharpCompress/0.39.0": {"type": "package", "serviceable": true, "sha512": "sha512-0esqIUDlg68Z7+Weuge4QzEvNtawUO4obTJFL7xuf4DBHMxVRr+wbNgiX9arMrj3kGXQSvLe0zbZG3oxpkwJOA==", "path": "sharpcompress/0.39.0", "hashPath": "sharpcompress.0.39.0.nupkg.sha512"}, "SixLabors.Fonts/2.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-psTLKJVKaD8PKm+Bz/mSSNo4m0VCTYoJ97TF/ynxEWkn4sFUipI0rzE2V/KBtfU0TOn/gF/+1XBOEvusGZZMhA==", "path": "sixlabors.fonts/2.0.8", "hashPath": "sixlabors.fonts.2.0.8.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-9fIOOAsyLFid6qKypM2Iy0Z3Q9yoanV8VoYAHtI2sYGMNKzhvRTjgFDHonIiVe+ANtxIxM6SuqUzj0r91nItpA==", "path": "sixlabors.imagesharp/3.1.7", "hashPath": "sixlabors.imagesharp.3.1.7.nupkg.sha512"}, "SixLabors.ImageSharp.Drawing/2.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-cER1JfvshYDmTxw+gUy/x5e1xoNWhrD6s3AFf8rRUx9hWSXYdOFreQfUrM/QooMj0rF7+hkVtvGnV3EdMx4dxA==", "path": "sixlabors.imagesharp.drawing/2.1.5", "hashPath": "sixlabors.imagesharp.drawing.2.1.5.nupkg.sha512"}, "System.CodeDom/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-VnlzS7afE8LaXXwKdHOe2D6FcDekqThzU1E67CONV7gp71q3zposqcSeXH+PxARMXC5j31efwXrxP8VGvD70Ug==", "path": "system.codedom/9.0.2", "hashPath": "system.codedom.9.0.2.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-pX5GhR4/mfYUbebRdINO6P4B8stIbVWaAWGgWzYO1eC30nUVpCQdzQjKwCPhtLdzjR3d/q+MQ0sRG3B1GLSyqw==", "path": "system.configuration.configurationmanager/9.0.2", "hashPath": "system.configuration.configurationmanager.9.0.2.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-i+Fe6Fpst/onydFLBGilCr/Eh9OFdlaTU/c3alPp6IbLZXQJOgpIu3l4MOnmsN8fDYq5nAyHSqNIJesc74Yw3Q==", "path": "system.diagnostics.eventlog/9.0.2", "hashPath": "system.diagnostics.eventlog.9.0.2.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-hRq8SBG2CYALIJqOYLsZW749wITem4JTXIL0YxYfovWSbCKF6Eu1AEMJ54Va1rg2I5o3aTD6QvBaRw4x/v7Sfw==", "path": "system.diagnostics.performancecounter/9.0.2", "hashPath": "system.diagnostics.performancecounter.9.0.2.nupkg.sha512"}, "System.Management/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-U6SXFe1kfJySAvcPPdbdb+fycaY+3c/KV0PwTjurvrALMnlSm37s5z8zcoI7qbkV2kYhxiLfsZRGiF0XeSsqSQ==", "path": "system.management/9.0.2", "hashPath": "system.management.9.0.2.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-+z9JWtU9di45tW/w6zpuJms4SnAAqKY2Usmmxs7MpOhxWoIeR2pKl1vEkaFbEx52ZfrMFcZXsFe+8WERMozzGg==", "path": "system.security.cryptography.protecteddata/9.0.2", "hashPath": "system.security.cryptography.protecteddata.9.0.2.nupkg.sha512"}, "ZstdSharp.Port/0.8.4": {"type": "package", "serviceable": true, "sha512": "sha512-eieSXq3kakCUXbgdxkKaRqWS6hF0KBJcqok9LlDCs60GOyrynLvPOcQ0pRw7shdPF7lh/VepJ9cP9n9HHc759g==", "path": "zstdsharp.port/0.8.4", "hashPath": "zstdsharp.port.0.8.4.nupkg.sha512"}}}