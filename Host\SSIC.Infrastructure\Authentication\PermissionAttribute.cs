﻿using Microsoft.AspNetCore.Authorization;
using SSIC.Infrastructure.Enums;
using System;

namespace SSIC.Infrastructure.Authentication
{
    public class PermissionAttribute : AuthorizeAttribute
    {
        public PermissionAttribute(ActionPermissionOption permission) => Permission = permission;

        public ActionPermissionOption Permission
        {
            get
            {
                if (Enum.TryParse<ActionPermissionOption>(Policy, out var permission))
                {
                    return permission;
                }
                return ActionPermissionOption.None;
            }
            set
            {
                Policy = value.ToString();
            }
        }

    }
}