﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using SSIC.Infrastructure.Enums;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Authentication
{
    public class PermissionHandler : AuthorizationHandler<PermissionRequirement>
    {
        private readonly IServiceProvider _services;

        public PermissionHandler(IServiceProvider services)
        {
            _services = services;
        }


        /// <summary>
        /// 授权策略
        /// </summary>
        /// <param name="context"></param>
        /// <param name="requirement"></param>
        /// <returns></returns>
        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context,
            PermissionRequirement requirement)
        {
            if (requirement.Permission != ActionPermissionOption.None)
            {
                //TODO：这些就需要判断user是否有requirement.Permissions的权限
                context.Succeed(requirement);
                //using (var scope = _services.CreateScope())
                //{
                //    var userService =
                //        scope.ServiceProvider
                //            .GetRequiredService<UserService>();
                //    var userName = context.User.FindFirstValue(ClaimTypes.Name);
                //    var permissions = await userService.GetPermissionsAsync(userName);
                //    if (permissions.Contains(requirement.Key))
                //    {
                //        context.Succeed(requirement);
                //    }
                //}

            }
            else {
                context.Succeed(requirement);
            }

                return Task.CompletedTask;
        }
    }
}
