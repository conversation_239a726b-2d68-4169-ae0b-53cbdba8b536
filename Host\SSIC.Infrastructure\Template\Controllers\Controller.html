﻿/*
 *Author：jxx
 *Contact：<EMAIL>
 *Date：2018-07-01
 * 此代码由框架生成，请勿随意更改
 */
using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using {Namespace}.IServices;
using {StartName}.Core.Controllers.Basic;
using Microsoft.AspNetCore.Mvc;
using VOL.Entity.AttributeManager;

namespace {Namespace}.Controllers
{   
    [ApiExplorerSettings(IgnoreApi = true)]
    [PermissionTable(Name = "{TableName}")]
    public partial class {TableName}Controller : BaseController<I{TableName}Service>
    {
        public {TableName}Controller(I{TableName}Service service)
        : base({BaseOptions}, service)
        {
        }
    }
}
