{"@t":"2025-04-12T01:26:43.5811998Z","@mt":"Now listening on: {address}","address":"https://localhost:64519","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:26:43.6063826Z","@mt":"Now listening on: {address}","address":"http://localhost:64520","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:26:43.6584405Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:26:43.6591490Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:26:43.6592402Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:26:52.6108257Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":0,"ExecutionTimeMs":4120.6005,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:26:52.6293937Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:26:58.9008470Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":1,"ExecutionTimeMs":4096.643,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:26:58.9028553Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:27:04.7434848Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":2,"ExecutionTimeMs":4092.0667,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:27:04.7444028Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:27:11.1111886Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Error","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":3,"ExecutionTimeMs":4084.0647,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:39:22.0084172Z","@mt":"Now listening on: {address}","address":"https://localhost:57951","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:39:22.0648737Z","@mt":"Now listening on: {address}","address":"http://localhost:57952","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:39:22.1842378Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:39:22.1857594Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:39:22.1860441Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:39:27.1253846Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":33.9981,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:41:33.4725063Z","@mt":"Now listening on: {address}","address":"https://localhost:59660","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:41:33.5057167Z","@mt":"Now listening on: {address}","address":"http://localhost:59661","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:41:33.5708036Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:41:33.5714260Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:41:33.5715674Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T01:41:38.2546058Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":33.368,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:25:34.4461512Z","@mt":"Now listening on: {address}","address":"https://localhost:52723","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:25:34.4820300Z","@mt":"Now listening on: {address}","address":"http://localhost:52724","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:25:34.5475130Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:25:34.5482561Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:25:34.5483805Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:25:39.2490463Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":34.0638,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:33:06.6368722Z","@mt":"Now listening on: {address}","address":"https://localhost:57539","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:33:06.6835387Z","@mt":"Now listening on: {address}","address":"http://localhost:57540","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:33:06.7503347Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:33:06.7514071Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:33:06.7515922Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:33:11.3480633Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":36.3433,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:45:06.9533499Z","@mt":"Now listening on: {address}","address":"https://localhost:64652","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:45:07.0008346Z","@mt":"Now listening on: {address}","address":"http://localhost:64653","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:45:07.1609811Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:45:07.1618185Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:45:07.1619547Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:45:11.7983475Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":53.0578,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:47:49.9918416Z","@mt":"Now listening on: {address}","address":"https://localhost:51182","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:47:50.0419871Z","@mt":"Now listening on: {address}","address":"http://localhost:51183","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:47:50.1280157Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:47:50.1295582Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:47:50.1297858Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:47:54.8562160Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":54.5713,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:57:11.8453052Z","@mt":"Now listening on: {address}","address":"https://localhost:56514","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:57:11.9113431Z","@mt":"Now listening on: {address}","address":"http://localhost:56515","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:57:12.0397616Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:57:12.0407160Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:57:12.0409026Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T02:57:16.5317163Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":32.3663,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:01:25.7473680Z","@mt":"Now listening on: {address}","address":"https://localhost:58836","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:01:25.7933457Z","@mt":"Now listening on: {address}","address":"http://localhost:58837","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:01:25.8618924Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:01:25.8627229Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:01:25.8628664Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:01:30.5492235Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":55.4533,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:09:52.2021815Z","@mt":"Now listening on: {address}","address":"https://localhost:63981","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:09:52.2339594Z","@mt":"Now listening on: {address}","address":"http://localhost:63982","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:09:52.2822097Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:09:52.2828478Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:09:52.2829509Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:09:57.1475409Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":52.1397,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:10:50.4996527Z","@mt":"Now listening on: {address}","address":"https://localhost:64763","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:10:50.5561314Z","@mt":"Now listening on: {address}","address":"http://localhost:64764","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:10:50.6590401Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:10:50.6599913Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:10:50.6602579Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:10:55.2703268Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":26.7827,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:11:44.6461073Z","@mt":"Now listening on: {address}","address":"https://localhost:65523","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:11:44.6958522Z","@mt":"Now listening on: {address}","address":"http://localhost:65524","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:11:44.7716763Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:11:44.7726012Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:11:44.7727756Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:11:49.5101653Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":50.6853,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:15:07.3972669Z","@mt":"Now listening on: {address}","address":"https://localhost:52666","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:15:07.4719001Z","@mt":"Now listening on: {address}","address":"http://localhost:52667","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:15:07.6667483Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:15:07.6677931Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:15:07.6680037Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:15:12.1145979Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":36.3894,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:27:10.4855776Z","@mt":"Now listening on: {address}","address":"https://localhost:59940","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:27:10.5097514Z","@mt":"Now listening on: {address}","address":"http://localhost:59941","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:27:10.5516876Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:27:10.5522495Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:27:10.5523417Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:27:15.4855934Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":46.0109,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:50:12.0999602Z","@mt":"Now listening on: {address}","address":"https://localhost:58305","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:50:12.1426941Z","@mt":"Now listening on: {address}","address":"http://localhost:58306","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:50:12.2293832Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:50:12.2303904Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:50:12.2306552Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:50:17.0233870Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":58.3329,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:55:40.8075408Z","@mt":"Now listening on: {address}","address":"https://localhost:61805","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:55:40.8416553Z","@mt":"Now listening on: {address}","address":"http://localhost:61806","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:55:40.8986501Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:55:40.9059462Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:55:40.9061010Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T03:55:45.5658788Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":26.855,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:06:33.5140770Z","@mt":"Now listening on: {address}","address":"https://localhost:53105","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:06:33.5575641Z","@mt":"Now listening on: {address}","address":"http://localhost:53106","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:06:33.6671230Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:06:33.6681234Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:06:33.6682966Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:06:38.3595060Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":55.1793,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:14:25.3668796Z","@mt":"Now listening on: {address}","address":"https://localhost:57885","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:14:25.4050073Z","@mt":"Now listening on: {address}","address":"http://localhost:57886","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:14:25.4750538Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:14:25.4759900Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:14:25.4761413Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:15:05.2867725Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":59.1057,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:56:31.6369422Z","@mt":"Now listening on: {address}","address":"https://localhost:54434","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:56:31.6854033Z","@mt":"Now listening on: {address}","address":"http://localhost:54435","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:56:31.7675969Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:56:31.7687453Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:56:31.7689691Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T04:56:36.4698828Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":59.1733,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:00:32.3194445Z","@mt":"Now listening on: {address}","address":"https://localhost:56962","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:00:32.3680188Z","@mt":"Now listening on: {address}","address":"http://localhost:56963","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:00:32.4468263Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:00:32.4478016Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:00:32.4480554Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:00:37.1584347Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":60.6936,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:02:51.3917454Z","@mt":"Now listening on: {address}","address":"https://localhost:58478","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:02:51.4488063Z","@mt":"Now listening on: {address}","address":"http://localhost:58479","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:02:51.5370542Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:02:51.5383120Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:02:51.5387645Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:02:56.1591680Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":49.4723,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:06:13.7881579Z","@mt":"Now listening on: {address}","address":"https://localhost:60751","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:06:13.8579956Z","@mt":"Now listening on: {address}","address":"http://localhost:60752","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:06:14.0058304Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:06:14.0069860Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:06:14.0073917Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-12T05:06:18.5575033Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":79.3871,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
