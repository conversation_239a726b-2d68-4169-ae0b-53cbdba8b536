﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.HotReload
{
    /// <summary>
    /// 模块扫描器
    /// </summary>
    public interface IModuleScanner
    {
        /// <summary>
        /// 扫描模块
        /// </summary>
        Task<List<string>> ScanModulesAsync();

        /// <summary>
        /// 检查文件是否为有效模块
        /// </summary>
        bool IsValidModuleFile(string filePath);
    }

    /// <summary>
    /// 模块扫描器实现
    /// </summary>
    public class ModuleScanner : IModuleScanner
    {
        private readonly HotReloadOptions _options;
        private readonly ILogger<ModuleScanner> _logger;

        public ModuleScanner(HotReloadOptions options, ILogger<ModuleScanner> logger)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 扫描所有监控路径下的模块
        /// </summary>
        public async Task<List<string>> ScanModulesAsync()
        {
            var moduleFiles = new List<string>();

            foreach (var basePath in _options.WatchPaths)
            {
                try
                {
                    if (!Directory.Exists(basePath))
                    {
                        if (_options.AutoCreateDirectories)
                        {
                            try
                            {
                                Directory.CreateDirectory(basePath);
                                _logger.LogInformation("创建监控目录: {Path}", basePath);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "创建监控目录失败: {Path}", basePath);
                                continue;
                            }
                        }
                        else
                        {
                            _logger.LogWarning("监控目录不存在: {Path}", basePath);
                            continue;
                        }
                    }

                    // 扫描目录
                    var files = await Task.Run(() => ScanDirectory(basePath, 0));
                    moduleFiles.AddRange(files);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "扫描目录失败: {Path}", basePath);
                }
            }

            return moduleFiles;
        }

        /// <summary>
        /// 扫描目录
        /// </summary>
        private List<string> ScanDirectory(string directory, int currentDepth)
        {
            var moduleFiles = new List<string>();

            try
            {
                // 扫描当前目录中的文件
                var files = Directory.GetFiles(directory, _options.ModuleSearchPattern);
                
                foreach (var file in files)
                {
                    if (IsValidModuleFile(file))
                    {
                        moduleFiles.Add(file);
                        _logger.LogDebug("找到模块文件: {File}", file);
                    }
                }

                // 如果需要递归扫描子目录，且未超过最大深度
                if (_options.RecursiveScan && (_options.MaxScanDepth == 0 || currentDepth < _options.MaxScanDepth))
                {
                    var subDirectories = Directory.GetDirectories(directory);
                    
                    foreach (var subDir in subDirectories)
                    {
                        var subDirFiles = ScanDirectory(subDir, currentDepth + 1);
                        moduleFiles.AddRange(subDirFiles);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扫描目录失败: {Path}", directory);
            }

            return moduleFiles;
        }

        /// <summary>
        /// 检查文件是否为有效模块
        /// </summary>
        public bool IsValidModuleFile(string filePath)
        {
            if (!File.Exists(filePath))
                return false;

            var fileName = Path.GetFileName(filePath);
            var extension = Path.GetExtension(filePath);
            
            // 检查文件扩展名是否匹配
            if (!_options.WatchExtensions.Contains(extension, StringComparer.OrdinalIgnoreCase))
                return false;

            // 根据不同的扫描模式判断是否为模块文件
            switch (_options.ScanMode)
            {
                case ModuleScanMode.ByPrefix:
                    // 按前缀匹配
                    return Path.GetFileNameWithoutExtension(fileName)
                        .StartsWith(_options.ModuleNamePrefix, StringComparison.OrdinalIgnoreCase);

                case ModuleScanMode.ByDirectory:
                    // 按目录结构匹配（检查是否位于Modules目录或其子目录中）
                    var directory = Path.GetDirectoryName(filePath);
                    return directory.Contains("Modules", StringComparison.OrdinalIgnoreCase);

                case ModuleScanMode.ByPattern:
                    // 按文件匹配模式匹配
                    return Path.GetFileNameWithoutExtension(fileName)
                        .Contains("Module", StringComparison.OrdinalIgnoreCase);
                
                case ModuleScanMode.ByWildcardPattern:
                    // 通配符模式匹配（类似"SSIC.MODULES.*.DLL"）
                    if (_options.WildcardPatterns == null || !_options.WildcardPatterns.Any())
                        return false;
                        
                    // 遍历所有通配符模式
                    foreach (var pattern in _options.WildcardPatterns)
                    {
                        if (string.IsNullOrEmpty(pattern))
                            continue;
                            
                        // 转换通配符模式为正则表达式
                        var regexPattern = WildcardToRegex(pattern);
                        
                        try
                        {
                            if (Regex.IsMatch(fileName, regexPattern, RegexOptions.IgnoreCase))
                            {
                                _logger.LogDebug("通配符匹配成功: {FileName} 匹配 {Pattern}", fileName, pattern);
                                return true;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "通配符匹配错误: {Pattern}", pattern);
                        }
                    }
                    
                    return false;

                case ModuleScanMode.AllDlls:
                    // 所有DLL文件都视为模块
                    return true;

                default:
                    return false;
            }
        }
        
        /// <summary>
        /// 将通配符模式转换为正则表达式
        /// </summary>
        private string WildcardToRegex(string pattern)
        {
            return "^" + Regex.Escape(pattern)
                      .Replace("\\*", ".*")
                      .Replace("\\?", ".")
                      + "$";
        }
    }
} 