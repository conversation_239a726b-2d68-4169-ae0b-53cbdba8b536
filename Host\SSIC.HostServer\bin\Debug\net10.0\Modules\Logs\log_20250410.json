{"@t":"2025-04-10T01:30:48.3229839Z","@mt":"Now listening on: {address}","address":"https://localhost:54189","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:30:48.3607351Z","@mt":"Now listening on: {address}","address":"http://localhost:54190","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:30:48.4374578Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:30:48.4379392Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:30:48.4381527Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:06.0848725Z","@mt":"Now listening on: {address}","address":"https://localhost:55107","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:06.1078354Z","@mt":"Now listening on: {address}","address":"http://localhost:55108","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:06.1415280Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:06.1416929Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:06.1417416Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:15.0600710Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":0,"ExecutionTimeMs":4083.0619,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:15.0771733Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:21.3896833Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":1,"ExecutionTimeMs":4073.6653,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:21.3909638Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:27.1444709Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":2,"ExecutionTimeMs":4074.7637,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:27.1456647Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:33.2071805Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Error","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":3,"ExecutionTimeMs":4122.6629,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:38:55.2429443Z","@mt":"Now listening on: {address}","address":"https://localhost:61289","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:38:55.2671761Z","@mt":"Now listening on: {address}","address":"http://localhost:61290","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:38:55.3007924Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:38:55.3009618Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:38:55.3010221Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:04.3011932Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":0,"ExecutionTimeMs":4090.7187,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:04.3274741Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:10.0164994Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":1,"ExecutionTimeMs":4097.8943,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:10.0188025Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:16.3648370Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":2,"ExecutionTimeMs":4080.1789,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:16.3676338Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:23.9949745Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Error","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":3,"ExecutionTimeMs":4104.2154,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:53:55.1637580Z","@mt":"Now listening on: {address}","address":"https://localhost:55907","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:53:55.1906687Z","@mt":"Now listening on: {address}","address":"http://localhost:55908","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:53:55.2317383Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:53:55.2319930Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:53:55.2320827Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:04.0782069Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":0,"ExecutionTimeMs":4091.5141,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:04.0949896Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:10.2746755Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":1,"ExecutionTimeMs":4092.3059,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:10.2758703Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:16.2247395Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":2,"ExecutionTimeMs":4065.5929,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:16.2254914Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:22.7378724Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Error","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":3,"ExecutionTimeMs":4079.4409,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:10.7181032Z","@mt":"Now listening on: {address}","address":"https://localhost:56687","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:10.7751192Z","@mt":"Now listening on: {address}","address":"http://localhost:56688","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:10.8585350Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:10.8590329Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:10.8592841Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:24.4246234Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":0,"ExecutionTimeMs":4123.2321,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:24.4597121Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:30.3904157Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":1,"ExecutionTimeMs":4095.6001,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:30.3934735Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:38.0478910Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":2,"ExecutionTimeMs":4087.6902,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:38.0494963Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:45.0203245Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Error","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":3,"ExecutionTimeMs":4061.9034,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:39:02.6384498Z","@mt":"Now listening on: {address}","address":"https://localhost:63126","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:39:02.6768955Z","@mt":"Now listening on: {address}","address":"http://localhost:63127","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:39:02.7378058Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:39:02.7381111Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:39:02.7382856Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:41:47.6563178Z","@mt":"Now listening on: {address}","address":"https://localhost:49380","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:41:47.6962673Z","@mt":"Now listening on: {address}","address":"http://localhost:49381","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:41:47.7655174Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:41:47.7658582Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:41:47.7660699Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:43:56.3836438Z","@mt":"Now listening on: {address}","address":"https://localhost:51792","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:43:56.4354565Z","@mt":"Now listening on: {address}","address":"http://localhost:51793","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:43:56.5119179Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:43:56.5122260Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:43:56.5123309Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:44:01.1364315Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":54.1544,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:25.8719019Z","@mt":"Now listening on: {address}","address":"https://localhost:56894","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:25.9132886Z","@mt":"Now listening on: {address}","address":"http://localhost:56895","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:25.9948887Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:25.9952475Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:25.9954045Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:30.7133578Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":52.2578,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:50:44.0175104Z","@mt":"Now listening on: {address}","address":"https://localhost:58057","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:50:44.0645714Z","@mt":"Now listening on: {address}","address":"http://localhost:58058","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:50:44.1395828Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:50:44.1399440Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:50:44.1402182Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:50:48.7403747Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":38.4776,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:04.9508651Z","@mt":"Now listening on: {address}","address":"https://localhost:56895","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:05.0136955Z","@mt":"Now listening on: {address}","address":"http://localhost:56896","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:05.1234129Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:05.1238388Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:05.1239955Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:19.7110882Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":47.7206,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:41:58.8354272Z","@mt":"Now listening on: {address}","address":"https://localhost:59242","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:41:58.8941454Z","@mt":"Now listening on: {address}","address":"http://localhost:59243","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:41:58.9639586Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:41:58.9642729Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:41:58.9643894Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:42:03.6061793Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":48.4119,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:42:08.6835964Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":46.8803,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:42:13.7429006Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":51.9407,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:42:28.7801052Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":8.8811,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:42:38.8629966Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":49.0917,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:42:48.9330832Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":49.2662,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:42:53.9817906Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":42.9921,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:44:51.8150200Z","@mt":"Now listening on: {address}","address":"https://localhost:61712","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:44:51.8648906Z","@mt":"Now listening on: {address}","address":"http://localhost:61713","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:44:51.9409124Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:44:51.9412804Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:44:51.9414595Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:44:56.5460146Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":50.3918,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:47.7401455Z","@mt":"Now listening on: {address}","address":"https://localhost:62403","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:47.7875137Z","@mt":"Now listening on: {address}","address":"http://localhost:62404","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:47.8692385Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:47.8695706Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:47.8697548Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:52.5273512Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":23.6617,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:28:10.9640133Z","@mt":"Now listening on: {address}","address":"https://localhost:65402","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:28:10.9999689Z","@mt":"Now listening on: {address}","address":"http://localhost:65403","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:28:11.0576202Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:28:11.0580566Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:28:11.0581696Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:28:15.7890018Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":43.2923,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:55:51.7052789Z","@mt":"Now listening on: {address}","address":"https://localhost:56801","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:55:51.7390030Z","@mt":"Now listening on: {address}","address":"http://localhost:56802","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:55:51.7976290Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:55:51.7979061Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:55:51.7979656Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:55:56.6202351Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":62.3875,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:58:36.5642693Z","@mt":"Now listening on: {address}","address":"https://localhost:59069","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:58:36.6211393Z","@mt":"Now listening on: {address}","address":"http://localhost:59070","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:58:36.7004701Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:58:36.7008477Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:58:36.7009841Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:58:41.3466077Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":56.847,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T10:14:21.9160569Z","@mt":"Now listening on: {address}","address":"https://localhost:56271","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T10:14:21.9530882Z","@mt":"Now listening on: {address}","address":"http://localhost:56272","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T10:14:22.0071406Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T10:14:22.0074587Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T10:14:22.0075735Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.Modules\\SSIC.Modules.Auth","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T10:14:26.8666596Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":61.0152,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
