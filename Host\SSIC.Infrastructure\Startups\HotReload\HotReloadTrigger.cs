using System;

namespace SSIC.Infrastructure.Startups.HotReload
{
    /// <summary>
    /// 热重载触发器，用于手动触发热重载
    /// 通过修改Version属性的值并保存文件来触发热重载
    /// </summary>
    public static class HotReloadTrigger
    {
        /// <summary>
        /// 版本号，修改此值并保存文件可触发热重载
        /// </summary>
        public static readonly string Version = "1.0.0";
        
        /// <summary>
        /// 获取当前时间戳，防止编译优化
        /// </summary>
        public static readonly DateTime LastModified = DateTime.Now;
        
        /// <summary>
        /// 更新说明，可选填
        /// </summary>
        public static readonly string UpdateNote = "Initial Version";
    }
} 