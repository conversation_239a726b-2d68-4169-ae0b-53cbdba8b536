﻿++解决方案 'SSIC' ‎ (5 个项目，共 5 个)
i:{00000000-0000-0000-0000-000000000000}:SSIC.sln
++SSIC.HostServer
i:{00000000-0000-0000-0000-000000000000}:SSIC.HostServer
++Connected Services 
i:{************************************}:>1635
i:{************************************}:>1636
++Properties
i:{************************************}:d:\saisi\ssic\ssic.host\properties\
i:{************************************}:d:\saisi\ssic\ssic.devtools\properties\
++依赖项
i:{************************************}:>1639
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1072
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:>1077
i:{************************************}:>1640
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:>1071
++Controllers
i:{************************************}:d:\saisi\ssic\ssic.host\controllers\
i:{************************************}:d:\saisi\ssic\ssic.devtools\controllers\
++WeatherForecastController.cs
i:{************************************}:d:\saisi\ssic\ssic.host\controllers\weatherforecastcontroller.cs
i:{************************************}:d:\saisi\ssic\ssic.devtools\controllers\weatherforecastcontroller.cs
++Logs
i:{************************************}:d:\saisi\ssic\ssic.host\logs\
++_20240605.log
i:{************************************}:d:\saisi\ssic\ssic.host\logs\_20240605.log
++appsettings.json
i:{************************************}:d:\saisi\ssic\ssic.host\appsettings.json
i:{************************************}:d:\saisi\ssic\ssic.devtools\appsettings.json
++Dockerfile
i:{************************************}:d:\saisi\ssic\ssic.host\dockerfile
i:{************************************}:d:\saisi\ssic\ssic.devtools\dockerfile
++Program.cs
i:{************************************}:d:\saisi\ssic\ssic.host\program.cs
i:{************************************}:d:\saisi\ssic\ssic.devtools\program.cs
++SSIC.Host.http
i:{************************************}:d:\saisi\ssic\ssic.host\ssic.host.http
++Startup.cs
i:{************************************}:d:\saisi\ssic\ssic.host\startup.cs
++WeatherForecast.cs
i:{************************************}:d:\saisi\ssic\ssic.host\weatherforecast.cs
i:{************************************}:d:\saisi\ssic\ssic.devtools\weatherforecast.cs
++未发现任何服务依赖项
i:{************************************}:>1637
i:{************************************}:>1638
++launchSettings.json
i:{************************************}:d:\saisi\ssic\ssic.host\properties\launchsettings.json
i:{************************************}:d:\saisi\ssic\ssic.devtools\properties\launchsettings.json
++包
i:{************************************}:>1697
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1747
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:>1682
i:{************************************}:>1725
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:>1675
++分析器
i:{************************************}:>1647
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1730
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:>1641
i:{************************************}:>1701
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:>1643
++框架
i:{************************************}:>1694
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1745
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:>1679
i:{************************************}:>1722
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:>1671
++项目
i:{************************************}:>1642
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1727
i:{************************************}:>1699
++appsettings.Development.json
i:{************************************}:d:\saisi\ssic\ssic.host\appsettings.development.json
i:{************************************}:d:\saisi\ssic\ssic.devtools\appsettings.development.json
++.dockerignore
i:{************************************}:d:\saisi\ssic\.dockerignore
i:{************************************}:d:\saisi\ssic\.dockerignore
++Microsoft.VisualStudio.Azure.Containers.Tools.Targets (1.19.5)
i:{************************************}:>1698
i:{************************************}:>1726
++Microsoft.AspNetCore.Analyzers
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
++Microsoft.AspNetCore.App.Analyzers
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
++Microsoft.AspNetCore.App.CodeFixes
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
++Microsoft.AspNetCore.Components.Analyzers
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
++Microsoft.AspNetCore.Mvc.Analyzers
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
++Microsoft.AspNetCore.Razor.Utilities.Shared
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.CodeAnalysis.Razor.Compiler
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
++Microsoft.EntityFrameworkCore.Analyzers
i:{************************************}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\8.0.4\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\8.0.4\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\8.0.4\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{************************************}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\8.0.4\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
++Microsoft.Extensions.Logging.Generators
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.ObjectPool
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Collections.Immutable
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
i:{************************************}:c:\program files\dotnet\sdk\8.0.301\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
++System.Text.Json.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.6\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.AspNetCore.App
i:{************************************}:>1695
i:{************************************}:>1723
++Microsoft.NETCore.App
i:{************************************}:>1696
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1746
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:>1681
i:{************************************}:>1724
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:>1674
++SSIC.Infrastructure
i:{************************************}:>1645
i:{00000000-0000-0000-0000-000000000000}:SSIC.Infrastructure
i:{************************************}:>1700
++Dapr.AspNetCore (1.13.1)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1770
++FreeRedis (1.2.15)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1762
++FreeScheduler (2.0.30)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1754
++FreeSql.All (3.2.825)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1755
++FreeSql.Cloud (1.8.3)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1753
++Mapster (7.4.0)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1758
++Microsoft.AspNetCore.Authentication.JwtBearer (8.0.6)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1764
++Microsoft.Data.SqlClient (5.2.1)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1748
++Microsoft.Extensions.Configuration (8.0.0)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1765
++Microsoft.Extensions.Configuration.Json (8.0.0)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1756
++Microsoft.Extensions.DependencyInjection (8.0.0)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1761
++Microsoft.Extensions.Hosting (8.0.0)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1749
++MiniProfiler.AspNetCore (4.3.8)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1759
++MiniProfiler.AspNetCore.Mvc (4.3.8)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1757
++MiniProfiler.Shared (4.3.8)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1763
++Npgsql (8.0.3)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1769
++Serilog (4.0.0)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1766
++Serilog.AspNetCore (8.0.1)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1752
++Serilog.Extensions.Logging (8.0.0)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1750
++Serilog.Settings.Configuration (8.0.0)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1767
++Serilog.Sinks.Console (5.0.1)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1760
++Swashbuckle.AspNetCore (6.6.2)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1751
++System.Text.RegularExpressions (4.3.1)
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1768
++Authentication
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\authentication\
++BaseProvider
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\baseprovider\
++Caching
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\caching\
++Configuration
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\configuration\
++Dapr
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\dapr\
++Enums
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\enums\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\enums\
++Framework
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\
++App
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\
++Attributes
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\attributes\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\configurableoptions\attributes\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\attributes\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dynamicapicontroller\attributes\
++AppStartupAttribute.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\attributes\appstartupattribute.cs
++Extensions
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\extensions\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\extensions\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\configurableoptions\extensions\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\extensions\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\utilities\extensions\
++Extras
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\extras\
++Filters
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\filters\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\filters\
++Internal
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\internal\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\internal\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\internal\
++InternalApp.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\internal\internalapp.cs
++Options
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\options\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\options\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\configurableoptions\options\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\options\
++AppSettingsOptions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\options\appsettingsoptions.cs
++Proxies
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\proxies\
++IDispatchProxy.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\proxies\idispatchproxy.cs
++IGlobalDispatchProxy.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\proxies\iglobaldispatchproxy.cs
++Startups
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\startups\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\startups\
++AppStartup.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\startups\appstartup.cs
++HostingStartup.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\startups\hostingstartup.cs
++Web
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\web\
++HttpContextLocal.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\web\httpcontextlocal.cs
++App.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\app.cs
++ConfigurableOptions
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\configurableoptions\
++DependencyInjection
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\
++DynamicApiController
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dynamicapicontroller\
++JsonConverters
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\jsonconverters\
++Reflection
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\reflection\
++SpecificationDocument
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\
++Assets
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\assets\
++index.html
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\assets\index.html
++index-mini-profiler.html
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\assets\index-mini-profiler.html
++Builders
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\builders\
++EnumSchemaFilter.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\filters\enumschemafilter.cs
++TagsOrderDocumentFilter.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\filters\tagsorderdocumentfilter.cs
++GroupExtraInfo.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\internal\groupextrainfo.cs
++SpecificationOpenApiInfo.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\internal\specificationopenapiinfo.cs
++SpecificationOpenApiSecurityRequirementItem.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\internal\specificationopenapisecurityrequirementitem.cs
++SpecificationOpenApiSecurityScheme.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\internal\specificationopenapisecurityscheme.cs
++Utilities
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\utilities\
++Logging
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\logging\
++Monitoring
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\monitoring\
++OptionsEntity
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\optionsentity\
++CorsOption.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\optionsentity\corsoption.cs
++DbSettings.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\optionsentity\dbsettings.cs
++PageData.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\optionsentity\pagedata.cs
++TenantChange.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\optionsentity\tenantchange.cs
++TenantList.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\optionsentity\tenantlist.cs
++ORM
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\orm\
++HostBuilder.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\startups\hostbuilder.cs
++CorsSetting.json
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\corssetting.json
++Dbsettings.json
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\dbsettings.json
++SSIC.Entity
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1729
i:{00000000-0000-0000-0000-000000000000}:SSIC.Entity
++SSIC.Utilities
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:>1728
i:{00000000-0000-0000-0000-000000000000}:SSIC.Utilities
++AuthenticationExtension.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\authentication\authenticationextension.cs
++JwtGenerator.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\authentication\jwtgenerator.cs
++PermissionAttribute.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\authentication\permissionattribute.cs
++PermissionHandler.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\authentication\permissionhandler.cs
++PermissionPolicyProvider.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\authentication\permissionpolicyprovider.cs
++PermissionRequirement.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\authentication\permissionrequirement.cs
++DaprClientProvider.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\dapr\daprclientprovider.cs
++ActionPermissionOption.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\enums\actionpermissionoption.cs
++CheckOption.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\enums\checkoption.cs
++OutCode.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\enums\outcode.cs
++TagType.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\enums\tagtype.cs
++TenatType.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\enums\tenattype.cs
++AppApplicationBuilderExtensions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\extensions\appapplicationbuilderextensions.cs
++AppServiceCollectionExtensions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\extensions\appservicecollectionextensions.cs
++HostBuilderExtensions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\extensions\hostbuilderextensions.cs
++AppExtra.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\extras\appextra.cs
++StartupFilter.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\app\filters\startupfilter.cs
++Dependencies
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\dependencies\
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dynamicapicontroller\dependencies\
++Penetrates.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dynamicapicontroller\penetrates.cs
++DateTimeJsonConverter.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\jsonconverters\datetimejsonconverter.cs
++DateTimeOffsetJsonConverter.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\jsonconverters\datetimeoffsetjsonconverter.cs
++AspectDispatchProxy.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\reflection\aspectdispatchproxy.cs
++AspectDispatchProxyGenerator.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\reflection\aspectdispatchproxygenerator.cs
++SpecificationDocumentBuilder.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\builders\specificationdocumentbuilder.cs
++SpecificationDocumentApplicationBuilderExtensions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\extensions\specificationdocumentapplicationbuilderextensions.cs
++SpecificationDocumentServiceCollectionExtensions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\extensions\specificationdocumentservicecollectionextensions.cs
++SpecificationDocumentSettingsOptions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\specificationdocument\options\specificationdocumentsettingsoptions.cs
++Snowflake
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\utilities\snowflake\
++JsonSerializerUtility.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\utilities\jsonserializerutility.cs
++SerilogConfig.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\logging\serilogconfig.cs
++ContextMiddleware
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\orm\contextmiddleware\
++DbContexts
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\orm\dbcontexts\
++Fillter
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\orm\fillter\
++OptionsSettingsAttribute.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\configurableoptions\attributes\optionssettingsattribute.cs
++ConfigurableOptionsServiceCollectionExtensions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\configurableoptions\extensions\configurableoptionsservicecollectionextensions.cs
++IConfigurableOptions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\configurableoptions\options\iconfigurableoptions.cs
++InjectionAttribute.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\attributes\injectionattribute.cs
++SkipProxyAttribute.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\attributes\skipproxyattribute.cs
++SkipScanAttribute.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\attributes\skipscanattribute.cs
++IPrivateDependency.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\dependencies\iprivatedependency.cs
++IScoped.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\dependencies\iscoped.cs
++ISingleton.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\dependencies\isingleton.cs
++ITransient.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\dependencies\itransient.cs
++InjectionActions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\enums\injectionactions.cs
++InjectionPatterns.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\enums\injectionpatterns.cs
++RegisterType.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\enums\registertype.cs
++DependencyInjectionExtensions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\extensions\dependencyinjectionextensions.cs
++DependencyInjectionServiceCollectionExtensions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\extensions\dependencyinjectionservicecollectionextensions.cs
++ExternalService.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\internal\externalservice.cs
++DependencyInjectionSettingsOptions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dependencyinjection\options\dependencyinjectionsettingsoptions.cs
++ApiDescriptionSettingsAttribute.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dynamicapicontroller\attributes\apidescriptionsettingsattribute.cs
++DynamicApiControllerAttribute.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dynamicapicontroller\attributes\dynamicapicontrollerattribute.cs
++IDynamicApiController.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\dynamicapicontroller\dependencies\idynamicapicontroller.cs
++HttpContextExtensions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\utilities\extensions\httpcontextextensions.cs
++MvcBuilderServiceCollectionExtensions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\utilities\extensions\mvcbuilderservicecollectionextensions.cs
++ObjectExtensions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\utilities\extensions\objectextensions.cs
++AtomicLongArray.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\utilities\snowflake\atomiclongarray.cs
++Snowflake.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\framework\utilities\snowflake\snowflake.cs
++ContextMiddleware.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\orm\contextmiddleware\contextmiddleware.cs
++ContextMiddlewareExtensions.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\orm\contextmiddleware\contextmiddlewareextensions.cs
++FreeSqlContexts.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\orm\dbcontexts\freesqlcontexts.cs
++MultiFreeSql.cs
i:{1cf1037d-28c8-4006-988c-ed0925b95207}:d:\saisi\ssic\ssic.infrastructure\orm\dbcontexts\multifreesql.cs
++Masuit.Tools.Core (2024.3.4)
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:>1684
++QRCoder (1.5.1)
i:{4d638b87-715b-4f3c-a53e-a011655ab9b5}:>1686
++SSIC.DevTools
i:{00000000-0000-0000-0000-000000000000}:SSIC.DevTools
++SSIC.DevTools.http
i:{************************************}:d:\saisi\ssic\ssic.devtools\ssic.devtools.http
++EntityBase
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\
++TenantEntity
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\tenantentity\
++Composition
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\composition\
++EntityApprove.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\entityapprove.cs
++EntityBasic.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\entitybasic.cs
++EntityDoc.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\entitydoc.cs
++EntityRate.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\entityrate.cs
++EntityTableBase.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\entitytablebase.cs
++IEntityApprove.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\ientityapprove.cs
++IEntityDoc.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\ientitydoc.cs
++IEntityRate.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\ientityrate.cs
++TenantInfo.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\tenantentity\tenantinfo.cs
++FreeSql (3.2.825)
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:>1678
++ApproveBasic.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\composition\approvebasic.cs
++DocBase.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\composition\docbase.cs
++DocBaseApprove.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\composition\docbaseapprove.cs
++DocRateBasic.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\composition\docratebasic.cs
++RateBasic.cs
i:{648d8d64-f32c-415b-ae5d-f7789de12be2}:d:\saisi\ssic\ssic.entity\entitybase\composition\ratebasic.cs
