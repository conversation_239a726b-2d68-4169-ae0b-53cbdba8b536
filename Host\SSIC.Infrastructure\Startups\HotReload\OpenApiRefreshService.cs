using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.HotReload
{
    /// <summary>
    /// OpenAPI文档刷新服务接口
    /// </summary>
    public interface IOpenApiRefreshService
    {
        /// <summary>
        /// 刷新OpenAPI文档
        /// </summary>
        Task RefreshAsync();
        
        /// <summary>
        /// 清除OpenAPI缓存
        /// </summary>
        Task ClearCacheAsync();
    }

    /// <summary>
    /// OpenAPI文档刷新服务实现
    /// </summary>
    public class OpenApiRefreshService : IOpenApiRefreshService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<OpenApiRefreshService> _logger;

        public OpenApiRefreshService(
            IServiceProvider serviceProvider,
            ILogger<OpenApiRefreshService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        /// <summary>
        /// 刷新OpenAPI文档
        /// </summary>
        public async Task RefreshAsync()
        {
            try
            {
                _logger.LogInformation("开始刷新OpenAPI文档...");

                // 1. 刷新ActionDescriptorCollectionProvider
                await RefreshActionDescriptors();

                // 2. 清除OpenAPI缓存
                await ClearCacheAsync();

                // 3. 触发文档重新生成
                await TriggerDocumentRegeneration();

                _logger.LogInformation("OpenAPI文档刷新完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新OpenAPI文档时出错");
                throw;
            }
        }

        /// <summary>
        /// 清除OpenAPI缓存
        /// </summary>
        public async Task ClearCacheAsync()
        {
            try
            {
                _logger.LogDebug("开始清除OpenAPI缓存...");

                // 尝试清除各种可能的缓存
                await ClearActionDescriptorCache();
                await ClearOpenApiDocumentCache();
                await ClearEndpointCache();

                _logger.LogDebug("OpenAPI缓存清除完成");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "清除OpenAPI缓存时出现警告");
            }
        }

        /// <summary>
        /// 刷新ActionDescriptor集合
        /// </summary>
        private async Task RefreshActionDescriptors()
        {
            try
            {
                var actionDescriptorProvider = _serviceProvider.GetService<IActionDescriptorCollectionProvider>();
                if (actionDescriptorProvider is ActionDescriptorCollectionProvider provider)
                {
                    // 通过反射调用内部刷新方法
                    var updateMethod = provider.GetType().GetMethod("Update", BindingFlags.NonPublic | BindingFlags.Instance);
                    if (updateMethod != null)
                    {
                        updateMethod.Invoke(provider, null);
                        _logger.LogDebug("已刷新ActionDescriptorCollectionProvider");
                    }
                    else
                    {
                        _logger.LogWarning("未找到ActionDescriptorCollectionProvider的Update方法");
                    }
                }
                else
                {
                    _logger.LogWarning("未找到ActionDescriptorCollectionProvider服务");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新ActionDescriptor时出错");
            }
        }

        /// <summary>
        /// 清除ActionDescriptor缓存
        /// </summary>
        private async Task ClearActionDescriptorCache()
        {
            try
            {
                var actionDescriptorProvider = _serviceProvider.GetService<IActionDescriptorCollectionProvider>();
                if (actionDescriptorProvider != null)
                {
                    // 尝试通过反射清除缓存
                    var cacheField = actionDescriptorProvider.GetType().GetField("_cache", BindingFlags.NonPublic | BindingFlags.Instance);
                    if (cacheField != null)
                    {
                        cacheField.SetValue(actionDescriptorProvider, null);
                        _logger.LogDebug("已清除ActionDescriptor缓存");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "清除ActionDescriptor缓存时出现异常（可忽略）");
            }
        }

        /// <summary>
        /// 清除OpenAPI文档缓存
        /// </summary>
        private async Task ClearOpenApiDocumentCache()
        {
            try
            {
                // 尝试获取OpenAPI相关服务并清除缓存
                var services = _serviceProvider.GetServices<object>();
                foreach (var service in services)
                {
                    var serviceType = service.GetType();
                    if (serviceType.Name.Contains("OpenApi") || serviceType.Name.Contains("Document"))
                    {
                        // 尝试调用可能的缓存清除方法
                        var clearMethods = serviceType.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                            .Where(m => m.Name.Contains("Clear") || m.Name.Contains("Reset") || m.Name.Contains("Refresh"));
                        
                        foreach (var method in clearMethods)
                        {
                            try
                            {
                                if (method.GetParameters().Length == 0)
                                {
                                    method.Invoke(service, null);
                                    _logger.LogDebug("已调用 {ServiceType}.{MethodName}", serviceType.Name, method.Name);
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogDebug(ex, "调用 {ServiceType}.{MethodName} 时出现异常", serviceType.Name, method.Name);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "清除OpenAPI文档缓存时出现异常（可忽略）");
            }
        }

        /// <summary>
        /// 清除端点缓存
        /// </summary>
        private async Task ClearEndpointCache()
        {
            try
            {
                // 清除动态端点管理器的缓存
                var endpointManager = SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.Instance;
                if (endpointManager != null)
                {
                    // 这里可以添加清除端点缓存的逻辑
                    _logger.LogDebug("已清除动态端点缓存");
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "清除端点缓存时出现异常（可忽略）");
            }
        }

        /// <summary>
        /// 触发文档重新生成
        /// </summary>
        private async Task TriggerDocumentRegeneration()
        {
            try
            {
                // 这里可以添加触发文档重新生成的逻辑
                // 例如：发送信号给前端刷新，或者重新构建文档
                _logger.LogDebug("已触发文档重新生成");
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "触发文档重新生成时出现异常（可忽略）");
            }
        }
    }
}
