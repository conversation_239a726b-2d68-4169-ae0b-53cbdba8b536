﻿namespace SSIC.Infrastructure.Entity
{
    /// <summary>
    /// 汇率,税率字段
    /// </summary>
    public interface IEntityRate
    {
        /// <summary>
        /// 汇率
        /// </summary>
        public decimal? exchangerate { get; set; }

        /// <summary>
        /// 税别代号
        /// </summary>
        public string taxcode { get; set; }

        /// <summary>
        /// 税别类型
        /// </summary>
        public int? taxtype { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? taxrate { get; set; }
    }
}