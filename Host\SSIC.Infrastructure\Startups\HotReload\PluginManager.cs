﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.Startups.Endpoints;
using SSIC.Infrastructure.Startups.HotReload;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.IO.MemoryMappedFiles;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;
using System.Threading;
using System.Threading.Tasks;
using SSIC.Infrastructure.Startups.Extensions;

namespace SSIC.Infrastructure.Startups.HotReload
{
    /// <summary>
    /// 插件管理器接口
    /// </summary>
    public interface IPluginManager
    {
        /// <summary>
        /// 初始化插件管理器
        /// </summary>
        Task InitializeAsync();

        /// <summary>
        /// 加载插件
        /// </summary>
        Task<Assembly> LoadPluginAsync(string pluginPath);

        /// <summary>
        /// 卸载插件
        /// </summary>
        Task UnloadPluginAsync(string pluginPath);

        /// <summary>
        /// 重新加载插件
        /// </summary>
        Task<Assembly> ReloadPluginAsync(string pluginPath);

        /// <summary>
        /// 获取已加载的插件列表
        /// </summary>
        IEnumerable<Assembly> GetLoadedPlugins();
    }

    /// <summary>
    /// 插件管理器实现
    /// </summary>
    public class PluginManager : IPluginManager, IDisposable
    {
        private readonly ILogger<PluginManager> _logger;
        private readonly HotReloadOptions _options;
        private readonly IServiceProvider _serviceProvider;
        private readonly ApplicationPartManager _partManager;
        private readonly IModuleScanner _moduleScanner;
        private readonly ConcurrentDictionary<string, AssemblyLoadContext> _loadContexts;
        private readonly ConcurrentDictionary<string, Assembly> _loadedPlugins;
        private readonly ConcurrentDictionary<string, MemoryMappedFile> _mappedFiles;
        private readonly ConcurrentDictionary<string, string> _tempFilePaths;
        private readonly string _tempDirectory;
        private readonly ConcurrentBag<string> _pendingDeleteFiles;
        private readonly Timer _cleanupTimer;
        private bool _isDisposed;

        public PluginManager(
            ILogger<PluginManager> logger,
            HotReloadOptions options,
            IServiceProvider serviceProvider,
            ApplicationPartManager partManager,
            IModuleScanner moduleScanner)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _partManager = partManager ?? throw new ArgumentNullException(nameof(partManager));
            _moduleScanner = moduleScanner ?? throw new ArgumentNullException(nameof(moduleScanner));
            _loadContexts = new ConcurrentDictionary<string, AssemblyLoadContext>(StringComparer.OrdinalIgnoreCase);
            _loadedPlugins = new ConcurrentDictionary<string, Assembly>(StringComparer.OrdinalIgnoreCase);
            _mappedFiles = new ConcurrentDictionary<string, MemoryMappedFile>(StringComparer.OrdinalIgnoreCase);
            _tempFilePaths = new ConcurrentDictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            _pendingDeleteFiles = new ConcurrentBag<string>();
            
            // 创建临时目录
            _tempDirectory = Path.Combine(Path.GetTempPath(), "SSIC_Modules_" + Guid.NewGuid().ToString("N"));
            if (!Directory.Exists(_tempDirectory))
            {
                Directory.CreateDirectory(_tempDirectory);
            }
            
            // 创建定时清理任务
            _cleanupTimer = new Timer(CleanupTempFiles, null, TimeSpan.FromMinutes(2), TimeSpan.FromMinutes(5));
        }

        /// <summary>
        /// 初始化插件管理器
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                // 搜索并加载所有模块
                var moduleFiles = await _moduleScanner.ScanModulesAsync();
                _logger.LogInformation("发现 {Count} 个模块文件", moduleFiles.Count);

                foreach (var modulePath in moduleFiles)
                {
                    try
                    {
                        await LoadPluginAsync(modulePath);
                        }
                        catch (Exception ex)
                        {
                        _logger.LogError(ex, "加载模块失败: {Path}", modulePath);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化插件管理器失败");
            }
        }

        /// <summary>
        /// 加载插件
        /// </summary>
        public async Task<Assembly> LoadPluginAsync(string pluginPath)
        {
            try
            {
                // 获取插件名称
                var pluginName = Path.GetFileNameWithoutExtension(pluginPath);
                
                // 检查插件是否已加载
                if (_loadedPlugins.TryGetValue(pluginName, out var existingAssembly))
                {
                    _logger.LogWarning("插件已经加载: {Name}", pluginName);
                    return existingAssembly;
                }

                // 使用内存映射文件加载插件
                var assembly = await LoadAssemblyFromMemoryMappedFileAsync(pluginPath);
                
                // 注册程序集部件
                _partManager.ApplicationParts.Add(new AssemblyPart(assembly));

                // 配置模块服务
                await ConfigureModuleServicesAsync(assembly);

                // 添加到已加载插件字典
                _loadedPlugins[pluginName] = assembly;

                _logger.LogInformation("成功加载插件: {Name}", pluginName);
                
                // 获取程序集名称
                var assemblyName = assembly.GetName().Name;
                
                // 尝试重建路由
                try
                {
                    var dynamicEndpointManager = SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.Instance;
                    if (dynamicEndpointManager != null)
                    {
                        // 从已卸载记录中移除当前模块
                        dynamicEndpointManager.RecordModuleLoaded(assemblyName);
                        
                        // 使用RebuildEndpoints方法
                        dynamicEndpointManager.RebuildEndpoints(HotReloadExtensions.GlobalServiceProvider, GetLoadedPlugins());
                        
                        // 使用RefreshEndpoints方法刷新路由
                        dynamicEndpointManager.RefreshEndpoints(HotReloadExtensions.GlobalServiceProvider, GetLoadedPlugins());
                        
                        _logger.LogInformation("已重建路由端点，模块 {AssemblyName} 已加载", assemblyName);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "重建路由端点失败，请稍后手动触发更新");
                }

                // 尝试刷新OpenAPI文档
                try
                {
                    var openApiRefreshService = HotReloadExtensions.GlobalServiceProvider?.GetService<OpenApiRefreshService>();
                    if (openApiRefreshService != null)
                    {
                        await openApiRefreshService.RefreshAsync();
                        _logger.LogInformation("已刷新OpenAPI文档");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "刷新OpenAPI文档失败: {Error}", ex.Message);
                }

                return assembly;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载插件失败: {Path}", pluginPath);
                throw;
            }
        }

        /// <summary>
        /// 使用内存映射文件加载程序集
        /// </summary>
        private async Task<Assembly> LoadAssemblyFromMemoryMappedFileAsync(string filePath)
        {
            // 读取文件内容
            byte[] fileContent;
            using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
            {
                fileContent = new byte[fileStream.Length];
                await fileStream.ReadAsync(fileContent, 0, fileContent.Length);
            }
            
            // 创建内存映射文件
            var mappedFileName = Guid.NewGuid().ToString("N");
            var mappedFile = MemoryMappedFile.CreateNew(mappedFileName, fileContent.Length);
            
            // 将文件内容写入内存映射文件
            using (var accessor = mappedFile.CreateViewAccessor(0, fileContent.Length, MemoryMappedFileAccess.ReadWrite))
            {
                accessor.WriteArray(0, fileContent, 0, fileContent.Length);
            }
            
            // 保存内存映射文件引用
            _mappedFiles[Path.GetFileNameWithoutExtension(filePath)] = mappedFile;
            
            // 创建自定义加载上下文
            var loadContext = new MemoryMappedAssemblyLoadContext(mappedFile, fileContent.Length);
            
            // 从内存映射文件加载程序集
            var assembly = loadContext.LoadFromMemoryMappedFile();
            
            // 保存加载上下文引用
            _loadContexts[Path.GetFileNameWithoutExtension(filePath)] = loadContext;
            
            return assembly;
        }
  
        /// <summary>
        /// 卸载插件
        /// </summary>
        public async Task UnloadPluginAsync(string pluginPath)
        {
            try
            {
                var pluginName = Path.GetFileNameWithoutExtension(pluginPath);
                
                // 检查插件是否已加载
                if (!_loadedPlugins.TryGetValue(pluginName, out var assembly))
                {
                    _logger.LogWarning("插件未加载: {Name}", pluginName);
                    return;
                }

                // 移除程序集部件
                var part = _partManager.ApplicationParts.FirstOrDefault(p => 
                    p is AssemblyPart assemblyPart && assemblyPart.Assembly == assembly);
                
                if (part != null)
                {
                    _partManager.ApplicationParts.Remove(part);
                }

                // 卸载程序集
                if (_loadContexts.TryRemove(pluginName, out var loadContext))
                {
                    loadContext.Unload();
                }

                _loadedPlugins.TryRemove(pluginName, out _);
                
                // 释放内存映射文件
                if (_mappedFiles.TryRemove(pluginName, out var mappedFile))
        {
            try
            {
                        mappedFile.Dispose();
                        _logger.LogDebug("已释放内存映射文件: {Name}", pluginName);
            }
            catch (Exception ex)
            {
                        _logger.LogWarning(ex, "释放内存映射文件失败: {Name}", pluginName);
                    }
                }

                _logger.LogInformation("成功卸载插件: {Name}", pluginName);

                // 尝试重建路由
                try
                {
                    var dynamicEndpointManager = SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.Instance;
                    if (dynamicEndpointManager != null)
                    {
                        // 记录已卸载的模块
                        dynamicEndpointManager.RecordUnloadedModule(pluginName);

                        // 使用RebuildEndpoints方法
                        dynamicEndpointManager.RebuildEndpoints(HotReloadExtensions.GlobalServiceProvider, GetLoadedPlugins());

                        // 使用RefreshEndpoints方法刷新路由
                        dynamicEndpointManager.RefreshEndpoints(HotReloadExtensions.GlobalServiceProvider, GetLoadedPlugins());

                        _logger.LogInformation("已重建路由端点");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "重建路由端点失败，请稍后手动触发更新");
                }

                // 尝试刷新OpenAPI文档
                try
                {
                    var openApiRefreshService = HotReloadExtensions.GlobalServiceProvider?.GetService<OpenApiRefreshService>();
                    if (openApiRefreshService != null)
                    {
                        await openApiRefreshService.RefreshAsync();
                        _logger.LogInformation("已刷新OpenAPI文档");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "刷新OpenAPI文档失败: {Error}", ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "卸载插件失败: {Path}", pluginPath);
                throw;
            }
        }
        
        /// <summary>
        /// 重新加载插件
        /// </summary>
        public async Task<Assembly> ReloadPluginAsync(string pluginPath)
        {
            await UnloadPluginAsync(pluginPath);
            return await LoadPluginAsync(pluginPath);
        }

        /// <summary>
        /// 获取已加载的插件列表
        /// </summary>
        public IEnumerable<Assembly> GetLoadedPlugins()
        {
            return _loadedPlugins.Values;
        }
        
        /// <summary>
        /// 配置模块服务
        /// </summary>
        private async Task ConfigureModuleServicesAsync(Assembly assembly)
        {
            try
            {
                // 不再尝试从GlobalServiceProvider获取IServiceCollection
                // 而是直接创建一个新的ServiceCollection实例
                var services = new ServiceCollection();
                
                // 注册当前ServiceProvider，以便插件可以访问现有服务
                services.AddSingleton(HotReloadExtensions.GlobalServiceProvider);
                
                // 使用自定义方法配置模块服务，避免命名冲突
                services.AddModuleServices(assembly);

                // 构建临时ServiceProvider并使用它
                using (var tempProvider = services.BuildServiceProvider())
                {
                    // 这里可以执行一些使用tempProvider的操作
                    // 例如获取并调用模块的初始化方法等
                }

                _logger.LogInformation("已配置模块服务: {Name}", assembly.GetName().Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "配置模块服务失败: {Name}", assembly.GetName().Name);
            }
        }
        
        
        /// <summary>
        /// 定期清理临时文件
        /// </summary>
        private void CleanupTempFiles(object state)
        {
            // 收集所有需要删除的文件
            var filesToDelete = new List<string>();
            while (_pendingDeleteFiles.TryTake(out var filePath))
            {
                filesToDelete.Add(filePath);
            }
            
            if (filesToDelete.Count == 0)
                return;
                
            _logger.LogInformation("开始清理临时文件，共 {Count} 个文件", filesToDelete.Count);
            
            // 尝试删除文件
            foreach (var filePath in filesToDelete)
            {
                try
                {
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                        _logger.LogDebug("已删除临时文件: {Path}", filePath);
                    }
            }
            catch (Exception ex)
            {
                    // 如果失败，重新添加到待删除队列
                    _pendingDeleteFiles.Add(filePath);
                    _logger.LogWarning(ex, "删除临时文件失败，已重新标记: {Path}", filePath);
                }
            }
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (_isDisposed)
                return;
                
            if (disposing)
            {
                // 停止定时器
                _cleanupTimer?.Dispose();
                
                // 执行最后一次清理
                CleanupTempFiles(null);
                
                // 释放所有内存映射文件
                foreach (var mappedFile in _mappedFiles.Values)
                {
                    try
                    {
                        mappedFile.Dispose();
                                }
                                catch { }
                            }
                _mappedFiles.Clear();
                
                // 尝试删除临时目录
                try
                {
                    if (Directory.Exists(_tempDirectory))
                    {
                        Directory.Delete(_tempDirectory, true);
                        _logger.LogInformation("已删除临时目录: {Path}", _tempDirectory);
                    }
            }
            catch (Exception ex)
            {
                    _logger.LogWarning(ex, "删除临时目录失败: {Path}", _tempDirectory);
                }
            }
            
            _isDisposed = true;
            }
        }
        
        /// <summary>
    /// 内存映射文件程序集加载上下文
        /// </summary>
    public class MemoryMappedAssemblyLoadContext : AssemblyLoadContext
    {
        private readonly MemoryMappedFile _mappedFile;
        private readonly long _fileSize;

        public MemoryMappedAssemblyLoadContext(MemoryMappedFile mappedFile, long fileSize) 
            : base(isCollectible: true)
        {
            _mappedFile = mappedFile ?? throw new ArgumentNullException(nameof(mappedFile));
            _fileSize = fileSize;
        }
        
        /// <summary>
        /// 从内存映射文件加载程序集
        /// </summary>
        public Assembly LoadFromMemoryMappedFile()
        {
            // 创建内存映射视图
            using (var accessor = _mappedFile.CreateViewAccessor(0, _fileSize, MemoryMappedFileAccess.Read))
            {
                // 创建字节数组
                byte[] assemblyBytes = new byte[_fileSize];
                
                // 读取内存映射文件内容
                accessor.ReadArray(0, assemblyBytes, 0, assemblyBytes.Length);
                
                // 从字节数组加载程序集
                return LoadFromStream(new MemoryStream(assemblyBytes));
            }
        }

        protected override Assembly Load(AssemblyName assemblyName)
        {
            // 尝试从当前上下文解析程序集
            var assembly = Default.LoadFromAssemblyName(assemblyName);
            return assembly;
        }

        protected override IntPtr LoadUnmanagedDll(string unmanagedDllName)
        {
            // 尝试从当前上下文解析非托管DLL
            return IntPtr.Zero;
        }
    }
}