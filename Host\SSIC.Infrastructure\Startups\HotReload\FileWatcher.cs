using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.HotReload
{
    /// <summary>
    /// 文件变化事件处理委托
    /// </summary>
    /// <param name="filePath">文件路径</param>
    public delegate Task FileChangedEventHandler(string filePath);

    /// <summary>
    /// 文件监控器接口
    /// </summary>
    public interface IFileWatcher : IDisposable
    {
        /// <summary>
        /// 文件创建或修改事件
        /// </summary>
        event FileChangedEventHandler FileChanged;

        /// <summary>
        /// 文件删除事件
        /// </summary>
        event FileChangedEventHandler FileDeleted;

        /// <summary>
        /// 文件重命名事件
        /// </summary>
        event FileChangedEventHandler FileRenamed;

        /// <summary>
        /// 开始监控
        /// </summary>
        void StartWatching();

        /// <summary>
        /// 停止监控
        /// </summary>
        void StopWatching();
    }

    /// <summary>
    /// 文件监控器实现
    /// </summary>
    public class FileWatcher : IFileWatcher
    {
        private readonly HotReloadOptions _options;
        private readonly ILogger<FileWatcher> _logger;
        private readonly List<FileSystemWatcher> _watchers = new List<FileSystemWatcher>();
        private readonly SemaphoreSlim _fileEventSemaphore = new SemaphoreSlim(1, 1);
        private readonly Dictionary<string, DateTime> _lastEventTimes = new Dictionary<string, DateTime>();
        private readonly TimeSpan _debounceTime = TimeSpan.FromMilliseconds(500); // 去抖动时间
        private bool _isDisposed;

        public event FileChangedEventHandler FileChanged;
        public event FileChangedEventHandler FileDeleted;
        public event FileChangedEventHandler FileRenamed;

        public FileWatcher(
            HotReloadOptions options,
            ILogger<FileWatcher> logger)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 开始监控文件
        /// </summary>
        public void StartWatching()
        {
            if (!_options.Enabled)
            {
                _logger.LogInformation("热插拔功能已禁用，不启动文件监控");
                return;
            }

            if (_options.WatchPaths.Count == 0)
            {
                _logger.LogWarning("未指定监控路径，不启动文件监控");
                return;
            }

            // 为每个监控路径创建监控器
            foreach (var path in _options.WatchPaths)
            {
                if (!Directory.Exists(path))
                {
                    if (_options.AutoCreateDirectories)
                    {
                        try
                        {
                            Directory.CreateDirectory(path);
                            _logger.LogInformation("创建监控目录: {Path}", path);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "创建监控目录失败: {Path}", path);
                            continue;
                        }
                    }
                    else
                    {
                        _logger.LogWarning("监控目录不存在: {Path}", path);
                        continue;
                    }
                }

                try
                {
                    var watcher = new FileSystemWatcher(path)
                    {
                        NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.FileName | NotifyFilters.DirectoryName,
                        IncludeSubdirectories = _options.RecursiveScan,
                        EnableRaisingEvents = true
                    };

                    // 设置文件过滤器
                    watcher.Filters.Clear();
                    foreach (var extension in _options.WatchExtensions)
                    {
                        watcher.Filters.Add($"*{extension}");
                    }

                    // 注册事件处理程序
                    watcher.Created += OnFileChanged;
                    watcher.Changed += OnFileChanged;
                    watcher.Deleted += OnFileDeleted;
                    watcher.Renamed += OnFileRenamed;

                    _watchers.Add(watcher);
                    _logger.LogInformation("开始监控目录: {Path}", path);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "创建目录监控器失败: {Path}", path);
                }
            }

            _logger.LogInformation("文件监控已启动，共监控 {Count} 个目录", _watchers.Count);
        }

        /// <summary>
        /// 停止监控文件
        /// </summary>
        public void StopWatching()
        {
            foreach (var watcher in _watchers)
            {
                watcher.EnableRaisingEvents = false;
                watcher.Created -= OnFileChanged;
                watcher.Changed -= OnFileChanged;
                watcher.Deleted -= OnFileDeleted;
                watcher.Renamed -= OnFileRenamed;
            }

            _watchers.Clear();
            _logger.LogInformation("文件监控已停止");
        }

        private async void OnFileChanged(object sender, FileSystemEventArgs e)
        {
            // 避免重复处理同一文件的多个事件
            await _fileEventSemaphore.WaitAsync();
            try
            {
                var fullPath = e.FullPath;
                var now = DateTime.UtcNow;
                
                // 检查是否已经最近处理过该文件事件
                if (_lastEventTimes.TryGetValue(fullPath, out var lastTime) && 
                    (now - lastTime) < _debounceTime)
                {
                    _logger.LogDebug("跳过重复事件: {Path}", fullPath);
                    return;
                }
                
                // 更新最后处理时间
                _lastEventTimes[fullPath] = now;
                
                // 确保文件不再被锁定
                await WaitForFileToBeUnlocked(fullPath);
                
                _logger.LogInformation("检测到文件变更: {Path}", fullPath);
                FileChanged?.Invoke(fullPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理文件变更事件失败: {Path}", e.FullPath);
            }
            finally
            {
                _fileEventSemaphore.Release();
            }
        }

        private async void OnFileDeleted(object sender, FileSystemEventArgs e)
        {
            await _fileEventSemaphore.WaitAsync();
            try
            {
                var fullPath = e.FullPath;
                
                // 从事件字典中移除
                _lastEventTimes.Remove(fullPath);
                
                _logger.LogInformation("检测到文件删除: {Path}", fullPath);
                FileDeleted?.Invoke(fullPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理文件删除事件失败: {Path}", e.FullPath);
            }
            finally
            {
                _fileEventSemaphore.Release();
            }
        }

        private async void OnFileRenamed(object sender, RenamedEventArgs e)
        {
            await _fileEventSemaphore.WaitAsync();
            try
            {
                // 先处理旧文件删除事件
                _lastEventTimes.Remove(e.OldFullPath);
                FileDeleted?.Invoke(e.OldFullPath);
                
                // 确保新文件不再被锁定
                await WaitForFileToBeUnlocked(e.FullPath);
                
                // 更新最后处理时间
                _lastEventTimes[e.FullPath] = DateTime.UtcNow;
                
                _logger.LogInformation("检测到文件重命名: {OldPath} -> {NewPath}", e.OldFullPath, e.FullPath);
                FileChanged?.Invoke(e.FullPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理文件重命名事件失败: {OldPath} -> {NewPath}", e.OldFullPath, e.FullPath);
            }
            finally
            {
                _fileEventSemaphore.Release();
            }
        }
        
        /// <summary>
        /// 等待文件解锁（确保可以访问文件）
        /// </summary>
        private async Task WaitForFileToBeUnlocked(string filePath)
        {
            // 如果文件不存在，直接返回
            if (!File.Exists(filePath))
                return;
                
            // 尝试次数
            int attempts = 0;
            int maxAttempts = _options.MaxRetryCount;
            int delayMs = _options.RetryDelayMilliseconds;
            
            while (attempts < maxAttempts)
            {
                try
                {
                    // 尝试以共享读取方式打开文件
                    using (var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                    {
                        // 如果能成功打开，则文件没有被锁定
                        _logger.LogDebug("文件可以访问: {Path}", filePath);
                        return;
                    }
                }
                catch (IOException)
                {
                    // 文件被锁定，等待后重试
                    attempts++;
                    
                    if (attempts < maxAttempts)
                    {
                        _logger.LogDebug("文件被锁定，等待解锁: {Path}，尝试 {Attempt}/{MaxAttempt}", 
                            filePath, attempts, maxAttempts);
                        await Task.Delay(delayMs);
                    }
                    else
                    {
                        _logger.LogWarning("等待文件解锁超时: {Path}", filePath);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "检查文件锁定状态时出错: {Path}", filePath);
                    break;
                }
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (_isDisposed)
                return;

            if (disposing)
            {
                StopWatching();
                foreach (var watcher in _watchers)
                {
                    watcher.Dispose();
                }
                _watchers.Clear();
                _fileEventSemaphore.Dispose();
            }

            _isDisposed = true;
        }
    }
} 