﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups
{
    /// <summary>
    /// 启动项接口
    /// </summary>
    public interface IStartups
    {
        void ConfigureServices(IServiceCollection services){ }
        void Configure(WebApplication app) { }
    }
}

