// <auto-generated/>

namespace Projects;

[global::System.CodeDom.Compiler.GeneratedCode("Aspire.Hosting", null)]
[global::System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage(Justification = "Generated code.")]
[global::System.Diagnostics.DebuggerDisplay("Type = {GetType().Name,nq}, ProjectPath = {ProjectPath}")]
public class SSIC_Infrastructure : global::Aspire.Hosting.IProjectMetadata
{
    public string ProjectPath => """D:\Saisi\SSIC_Core\SSIC.Infrastructure\SSIC.Infrastructure.csproj""";
}
