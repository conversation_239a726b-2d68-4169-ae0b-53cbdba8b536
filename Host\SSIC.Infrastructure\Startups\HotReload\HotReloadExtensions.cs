﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.OpenApi;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.OptionsEntity;
using SSIC.Infrastructure.ConfigurableOptions.Realization;
using SSIC.Infrastructure.Startups.Endpoints;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;

namespace SSIC.Infrastructure.Startups.HotReload
{
    /// <summary>
    /// 热插拔扩展方法
    /// </summary>
    public static class HotReloadExtensions
    {
        // 内部非静态类，用于日志类别
        private class HotReloadLogger { }

        /// <summary>
        /// 全局服务提供程序
        /// </summary>
        public static IServiceProvider GlobalServiceProvider { get; private set; }

        private static readonly ILogger _logger = 
            LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger("HotReload");
        private static IPluginManager _pluginManager;
        private static WebApplication _app;

        /// <summary>
        /// 在WebApplicationBuilder上添加热插拔支持，支持链式调用
        /// </summary>
        /// <param name="builder">Web应用程序构建器</param>
        /// <param name="configureOptions">配置选项</param>
        /// <returns>Web应用程序构建器</returns>
        public static WebApplicationBuilder AddHotReload(this WebApplicationBuilder builder, Action<HotReloadOptions> configureOptions)
        {
            //Console.WriteLine("123");
            var options = new HotReloadOptions();
            configureOptions?.Invoke(options);

            return builder.AddHotReload(options);
        }

        /// <summary>
        /// 在WebApplicationBuilder上添加热插拔支持，使用模块路径配置
        /// </summary>
        /// <param name="builder">Web应用程序构建器</param>
        /// <param name="enabled">是否启用热插拔功能，默认为false</param>
        /// <returns>Web应用程序构建器</returns>
        public static WebApplicationBuilder AddHotReload(this WebApplicationBuilder builder, bool enabled = false)
        {
            if (!enabled)
            {
                // 如果未启用，返回默认的空配置
                return builder.AddHotReload(new HotReloadOptions { Enabled = false });
            }

            // 先注册配置选项以便获取模块路径配置
            IConfiguration _Configuration = builder.Configuration;
            builder.Services.AddAllConfigurableOptions(ref _Configuration);

            // 获取模块路径配置
            var modulePathOptions = builder.Services.GetOptions<ModulePathOptions>();

            // 获取模块路径
            var isDevelopment = builder.Environment.IsDevelopment();
            var modulePath = modulePathOptions.GetModuleDirectory(isDevelopment);

            // 确保模块目录存在
            if (!Directory.Exists(modulePath))
            {
                Directory.CreateDirectory(modulePath);
            }

            // 获取模块子文件夹（使用配置中的设置）
            var subFolders = modulePathOptions.GetModuleSubFolders(isDevelopment);

            var hotReloadOptions = new HotReloadOptions
            {
                Enabled = modulePathOptions.Enabled,
                WatchPaths = subFolders,
                ScanMode = modulePathOptions.GetScanMode(),
                WildcardPatterns = modulePathOptions.WildcardPatterns
            };

            return builder.AddHotReload(hotReloadOptions);
        }

        /// <summary>
        /// 在WebApplicationBuilder上添加热插拔支持，支持链式调用（重载版本，直接接收选项对象）
        /// </summary>
        /// <param name="builder">Web应用程序构建器</param>
        /// <param name="options">热插拔选项</param>
        /// <returns>Web应用程序构建器</returns>
        public static WebApplicationBuilder AddHotReload(this WebApplicationBuilder builder, HotReloadOptions options)
        {
            if (options == null)
            {
                options = new HotReloadOptions();
            }

            // 注册HotReloadOptions
            builder.Services.AddSingleton(options);

            // 注册模块扫描器
            builder.Services.AddSingleton<IModuleScanner, ModuleScanner>();

            // 注册文件监控器
            builder.Services.AddSingleton<IFileWatcher, FileWatcher>();

            // 注册插件管理器
            builder.Services.AddSingleton<IPluginManager, PluginManager>();
            builder.Services.AddSingleton<PluginManager>(sp => (PluginManager)sp.GetRequiredService<IPluginManager>());

            // 注册OpenAPI刷新服务
            builder.Services.AddSingleton<IOpenApiRefreshService, OpenApiRefreshService>();
            builder.Services.AddSingleton<ISimpleOpenApiRefreshService, SimpleOpenApiRefreshService>();

            // 注册动态端点源
            builder.Services.AddSingleton<IFileProvider>(sp => 
            {
                var basePath = Directory.GetCurrentDirectory();
                return new PhysicalFileProvider(basePath);
            });

            // 注册动态端点数据源
            builder.Services.AddSingleton<DynamicEndpointDataSource>(sp => 
            {
                // 获取DynamicEndpointManager实例
                var dataSource = DynamicEndpointManager.Instance.GetDynamicEndpoints() as DynamicEndpointDataSource;
                if (dataSource == null)
                {
                    dataSource = new DynamicEndpointDataSource();
                }
                return dataSource;
            });

            // 注册动态端点源
            builder.Services.AddSingleton<IDynamicEndpointSource>(sp =>
            {
                var fileProvider = sp.GetRequiredService<IFileProvider>();
                // 端点定义JSON文件所在目录，默认为"Endpoints"
                var endpointsPath = "Endpoints";
                return new DynamicEndpointSource(fileProvider, endpointsPath);
            });

            return builder;
        }

        /// <summary>
        /// 使用热插拔功能，初始化并启动热插拔模块
        /// </summary>
        /// <param name="app">Web应用程序</param>
        /// <returns>Web应用程序</returns>
        public static WebApplication UseHotReload(this WebApplication app)
        {
            GlobalServiceProvider = app.Services;

            // 获取插件管理器和文件监控器
            var pluginManager = app.Services.GetService<IPluginManager>();
            var fileWatcher = app.Services.GetService<IFileWatcher>();
            var endpointSource = app.Services.GetService<IDynamicEndpointSource>();
            // 使用非静态类作为日志类别
            var logger = app.Services.GetService<ILogger<HotReloadLogger>>();

            if (pluginManager == null || fileWatcher == null)
            {
                logger?.LogWarning("热插拔服务未注册，跳过初始化");
                return app;
            }

            // 初始化DynamicEndpointManager
            DynamicEndpointManager.Initialize(app.Services);

            // 初始化插件管理器
            Task.Run(async () =>
            {
                try
                {
                    // 初始化插件管理器
                    await pluginManager.InitializeAsync();

                    // 等待一小段时间确保所有模块完全加载
                    await Task.Delay(1000);

                    // 初始化完成后刷新OpenAPI文档
                    await UpdateRoutes(app, pluginManager);

                    // 再次等待确保路由更新完成
                    await Task.Delay(500);

                    var simpleRefreshService = app.Services.GetService<ISimpleOpenApiRefreshService>();
                    if (simpleRefreshService != null)
                    {
                        await simpleRefreshService.RefreshAsync();
                        logger?.LogInformation("初始化完成，已刷新OpenAPI组件");
                    }
                    else
                    {
                        await RefreshOpenApiDocumentation(app);
                        logger?.LogInformation("初始化完成，已使用备用方法刷新OpenAPI组件");
                    }

                    // 最后再次刷新确保Scalar能看到更新
                    await Task.Delay(500);
                    logger?.LogInformation("模块热重载初始化完成，请刷新Scalar页面查看API文档");

                    // 注册文件监控事件
                    fileWatcher.FileChanged += async (filePath) =>
                    {
                        try
                        {
                            logger?.LogInformation("检测到文件变更: {FilePath}", filePath);

                            // 重新加载插件
                            await pluginManager.ReloadPluginAsync(filePath);

                            // 更新路由
                            await UpdateRoutes(app, pluginManager);

                            // 刷新OpenAPI文档和Scalar
                            var simpleRefreshService = app.Services.GetService<ISimpleOpenApiRefreshService>();
                            if (simpleRefreshService != null)
                            {
                                await simpleRefreshService.RefreshAsync();
                                logger?.LogInformation("已使用简化服务刷新OpenAPI组件");
                            }
                            else
                            {
                                await RefreshOpenApiDocumentation(app);
                                logger?.LogInformation("已使用备用方法刷新OpenAPI组件");
                            }
                        }
                        catch (Exception ex)
                        {
                            logger?.LogError(ex, "处理文件变更事件失败: {FilePath}", filePath);
                        }
                    };

                    fileWatcher.FileDeleted += async (filePath) =>
                    {
                        try
                        {
                            logger?.LogInformation("检测到文件删除: {FilePath}", filePath);
                            
                            // 卸载插件
                            await pluginManager.UnloadPluginAsync(filePath);
                            
                            // 更新路由
                            await UpdateRoutes(app, pluginManager);
                        }
                        catch (Exception ex)
                        {
                            logger?.LogError(ex, "处理文件删除事件失败: {FilePath}", filePath);
                        }
                    };

                    // 如果有动态端点源，监控端点定义变化
                    if (endpointSource != null)
                    {
                        // 获取初始端点
                        var endpoints = await endpointSource.GetEndpointsAsync();
                        logger?.LogInformation("加载了 {Count} 个动态端点定义", endpoints?.Count() ?? 0);

                        // 监控端点定义变化
                        endpointSource.Watch(newEndpoints =>
                        {
                            logger?.LogInformation("检测到端点定义变化，共 {Count} 个端点", newEndpoints?.Count() ?? 0);
                            // 这里可以添加处理端点变化的逻辑
                        });
                    }

                    // 启动文件监控
                    fileWatcher.StartWatching();
                    logger?.LogInformation("热插拔服务已启动");
                }
                catch (Exception ex)
                {
                    logger?.LogError(ex, "初始化热插拔服务失败");
                }
            });

            _pluginManager = pluginManager;
            _app = app;

            return app;
        }

        /// <summary>
        /// 更新路由
        /// </summary>
        private static async Task UpdateRoutes(WebApplication app, IPluginManager pluginManager)
        {
            try
            {
                // 获取所有已加载的插件
                var plugins = pluginManager.GetLoadedPlugins();

                // 使用DynamicEndpointManager更新路由
                var dynamicEndpointManager = SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.Instance;
                if (dynamicEndpointManager != null)
                {
                    // 使用RebuildEndpoints方法
                    dynamicEndpointManager.RebuildEndpoints(app.Services, plugins);

                    // 使用RefreshEndpoints方法刷新路由
                    dynamicEndpointManager.RefreshEndpoints(app.Services, plugins);
                }
                else
                {
                    _logger.LogWarning("DynamicEndpointManager实例为空，无法更新路由");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新路由时出错");
            }
        }

        /// <summary>
        /// 刷新OpenAPI文档和Scalar
        /// </summary>
        private static async Task RefreshOpenApiDocumentation(WebApplication app)
        {
            try
            {
                _logger.LogInformation("开始刷新OpenAPI文档...");

                // 强制刷新ActionDescriptorCollectionProvider
                var actionDescriptorProvider = app.Services.GetService<IActionDescriptorCollectionProvider>();
                if (actionDescriptorProvider is ActionDescriptorCollectionProvider provider)
                {
                    // 通过反射调用内部刷新方法
                    var updateMethod = provider.GetType().GetMethod("Update", BindingFlags.NonPublic | BindingFlags.Instance);
                    updateMethod?.Invoke(provider, null);
                    _logger.LogInformation("已刷新ActionDescriptorCollectionProvider");
                }

                // 尝试清除可能的OpenAPI相关缓存
                try
                {
                    // 获取所有已注册的服务，查找OpenAPI相关服务
                    var serviceProvider = app.Services;
                    var serviceDescriptors = serviceProvider.GetService<IServiceCollection>();

                    // 尝试通过反射清除可能的OpenAPI缓存
                    var openApiServices = serviceProvider.GetServices<object>()
                        .Where(s => s.GetType().FullName?.Contains("OpenApi") == true);

                    foreach (var service in openApiServices)
                    {
                        var serviceType = service.GetType();
                        var clearMethods = serviceType.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                            .Where(m => m.Name.Contains("Clear") || m.Name.Contains("Reset"));

                        foreach (var method in clearMethods)
                        {
                            if (method.GetParameters().Length == 0)
                            {
                                try
                                {
                                    method.Invoke(service, null);
                                    _logger.LogDebug("已调用 {ServiceType}.{MethodName}", serviceType.Name, method.Name);
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogDebug(ex, "调用清除方法时出现异常: {ServiceType}.{MethodName}", serviceType.Name, method.Name);
                                }
                            }
                        }
                    }

                    _logger.LogInformation("已尝试清除OpenAPI相关缓存");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "清除OpenAPI缓存时出现警告，但不影响功能");
                }

                _logger.LogInformation("OpenAPI文档刷新完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新OpenAPI文档时出错");
            }
        }

        private static void OnFileDeleted(string filePath)
        {
            try
            {
                _logger.LogInformation("检测到文件删除: {FilePath}", filePath);
                
                // 获取文件名（不含路径）
                var fileName = Path.GetFileName(filePath);
                
                // 检查是否是模块文件
                if (fileName.StartsWith("SSIC.Modules.", StringComparison.OrdinalIgnoreCase) && 
                    fileName.EndsWith(".dll", StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogInformation("检测到模块文件删除: {FileName}", fileName);
                    
                    // 获取模块名称（不含扩展名）
                    var moduleName = Path.GetFileNameWithoutExtension(fileName);
                    
                    // 记录已卸载的模块名称
                    SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.Instance.RecordUnloadedModule(moduleName);
                    
                    // 卸载插件
                    Task.Run(async () => 
                    {
                        await _pluginManager.UnloadPluginAsync(filePath);
                        await UpdateRoutes(_app, _pluginManager);
                    }).GetAwaiter().GetResult();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理文件删除事件时出错: {FilePath}", filePath);
            }
        }
    }
} 