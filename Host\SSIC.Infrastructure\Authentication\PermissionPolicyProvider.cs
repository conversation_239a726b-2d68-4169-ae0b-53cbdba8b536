﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;
using SSIC.Infrastructure.Enums;
using System;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Authentication
{
    public class PermissionPolicyProvider : IAuthorizationPolicyProvider
    {
        private readonly AuthorizationOptions _options;
        private Task<AuthorizationPolicy> _cachedDefaultPolicy;
        private Task<AuthorizationPolicy> _cachedFallbackPolicy;

        public PermissionPolicyProvider(IOptions<AuthorizationOptions> options)
        {
            if (options == null)
            {
                throw new ArgumentNullException(nameof(options));
            }

            _options = options.Value;
        }

        public Task<AuthorizationPolicy> GetDefaultPolicyAsync()
        {
            return GetCachedPolicy(ref _cachedDefaultPolicy, _options.DefaultPolicy);
        }

        public Task<AuthorizationPolicy> GetFallbackPolicyAsync()
        {
            return GetCachedPolicy(ref _cachedFallbackPolicy, _options.FallbackPolicy);
        }

        private Task<AuthorizationPolicy> GetCachedPolicy(ref Task<AuthorizationPolicy> cachedPolicy, AuthorizationPolicy currentPolicy)
        {
            var local = cachedPolicy;
            if (local == null || local.Result != currentPolicy)
            {
                cachedPolicy = local = Task.FromResult(currentPolicy);
            }
            return local;
        }

        public Task<AuthorizationPolicy> GetPolicyAsync(string policyName)
        {
            if (Enum.TryParse<ActionPermissionOption>(policyName, out var permission))
            {
                var policy = new AuthorizationPolicyBuilder(JwtBearerDefaults.AuthenticationScheme);
                policy.AddRequirements(new PermissionRequirement(permission));
                return Task.FromResult(policy.Build());
            }

            return Task.FromResult<AuthorizationPolicy>(null);
        }
    }
}
