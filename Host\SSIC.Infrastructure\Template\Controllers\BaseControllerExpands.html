﻿/*
 *接口编写处...
*如果接口需要做Action的权限验证，请在Action上使用属性
*如: [Permission(ActionPermissionOption.Add)]
 */
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SSFB.Core.Authentication;
using SSFB.Core.BaseController;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using SSFB.Entity.{BusinessName};
using SSFB.Business.{BusinessName}.IService;
namespace SSFB.WebApi.Controllers.{BusinessName}
{
public partial class {EntityName}Controller
{
public {EntityName}Controller(I{EntityName}Service service,
ILogger<{EntityName}Controller>
    logger) : base(service, logger)
    {
    }
    }
    }