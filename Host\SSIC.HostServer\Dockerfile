#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER app
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Host/SSIC.HostServer/SSIC.HostServer.csproj", "Host/SSIC.HostServer/"]
COPY ["Host/SSIC.Infrastructure/SSIC.Infrastructure.csproj", "Host/SSIC.Infrastructure/"]
COPY ["Host/SSIC.Entity/SSIC.Entity.csproj", "Host/SSIC.Entity/"]
COPY ["Host/SSIC.Utilities/SSIC.Utilities.csproj", "Host/SSIC.Utilities/"]
RUN dotnet restore "./Host/SSIC.HostServer/./SSIC.HostServer.csproj"
COPY . .
WORKDIR "/src/Host/SSIC.HostServer"
RUN dotnet build "./SSIC.HostServer.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./SSIC.HostServer.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "SSIC.HostServer.dll"]