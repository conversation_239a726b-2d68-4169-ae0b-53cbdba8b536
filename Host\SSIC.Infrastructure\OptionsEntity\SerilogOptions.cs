﻿using SSIC.Infrastructure.ConfigurableOptions;
using System;
using System.Collections.Generic;
using System.Text;

namespace SSIC.Infrastructure.OptionsEntity
{
    /// <summary>
    /// Serilog 配置选项类，用于映射配置文件（如 appsettings.json）
    /// </summary>
    public class SerilogOptions : IConfigurableOptions
    {
        /// <summary>
        /// Serilog 配置根对象
        /// </summary>
      
            /// <summary>
            /// 使用的 Serilog 插件列表（如 Serilog.Sinks.Console 等）
            /// </summary>
            public string[] Using { get; set; }

            /// <summary>
            /// 日志写入配置列表（支持写入多个目的地，如 Console、File、Seq 等）
            /// </summary>
            public Writeto[] WriteTo { get; set; }

            /// <summary>
            /// 日志增强器配置（如 FromLogContext，添加更多上下文信息）
            /// </summary>
            public string[] Enrich { get; set; }

            /// <summary>
            /// 附加到日志中的通用属性（如应用名、环境）
            /// </summary>
            public Properties Properties { get; set; }

            /// <summary>
            /// 最小日志级别配置（如 Debug、Information、Warning 等）
            /// </summary>
            public Minimumlevel MinimumLevel { get; set; }
        }

        /// <summary>
        /// 日志附加属性，如应用名和环境
        /// </summary>
        public class Properties
        {
            /// <summary>
            /// 应用程序名称
            /// </summary>
            public string Application { get; set; }

            /// <summary>
            /// 当前运行环境（如 Development、Production）
            /// </summary>
            public string Environment { get; set; }
        }

        /// <summary>
        /// 最小日志级别配置
        /// </summary>
        public class Minimumlevel
        {
            /// <summary>
            /// 默认的最小日志级别
            /// </summary>
            public string Default { get; set; }
        }

        /// <summary>
        /// 日志写入配置
        /// </summary>
        public class Writeto
        {
            /// <summary>
            /// Sink 名称（例如 Console、File、Seq 等）
            /// </summary>
            public string Name { get; set; }

            /// <summary>
            /// Sink 参数配置
            /// </summary>
            public Args Args { get; set; }
        }

        /// <summary>
        /// 日志写入参数
        /// </summary>
        public class Args
        {
            /// <summary>
            /// 日志服务器地址（用于 Seq）
            /// </summary>
            public string serverUrl { get; set; }

            /// <summary>
            /// 访问日志服务器所需的 API 密钥（可选）
            /// </summary>
            public string apiKey { get; set; }

            /// <summary>
            /// 写入日志的最小级别
            /// </summary>
            public string restrictedToMinimumLevel { get; set; }

            /// <summary>
            /// 批量发送日志的最大条数
            /// </summary>
            public int batchPostingLimit { get; set; }

            /// <summary>
            /// 批量发送的时间间隔（格式如 "00:00:02"）
            /// </summary>
            public string period { get; set; }

            /// <summary>
            /// 日志输出模板（控制日志内容格式）
            /// </summary>
            public string outputTemplate { get; set; }

            /// <summary>
            /// 控制台日志主题（如 SystemConsoleTheme.Literate）
            /// </summary>
            public string theme { get; set; }

            /// <summary>
            /// 文件日志保存路径
            /// </summary>
            public string path { get; set; }

            /// <summary>
            /// 文件滚动间隔（如 "Day"、"Hour"）
            /// </summary>
            public string rollingInterval { get; set; }

            /// <summary>
            /// 自定义日志格式器（如 CompactJsonFormatter）
            /// </summary>
            public string formatter { get; set; }

            /// <summary>
            /// 保留的最大日志文件数量
            /// </summary>
            public int retainedFileCountLimit { get; set; }

            /// <summary>
            /// 单个日志文件的最大大小（字节数）
            /// </summary>
            public int fileSizeLimitBytes { get; set; }

            /// <summary>
            /// 超过最大文件大小时是否滚动生成新文件
            /// </summary>
            public bool rollOnFileSizeLimit { get; set; }
        }
    }
