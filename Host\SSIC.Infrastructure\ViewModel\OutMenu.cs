﻿using SSIC.Infrastructure.Enums;
using System.Collections.Generic;

namespace SSIC.Infrastructure.ViewModel
{
    /// <summary>
    /// 菜单实体
    /// </summary>
    public class OutMenu
    {
        /// <summary>
        /// 菜单名
        /// </summary>
        public string name { get; set; }

        /// <summary>
        /// 菜单图标,如果没有，则会尝试使用route.meta.icon
        /// </summary>
        public string icon { get; set; }

        /// <summary>
        /// 菜单路径
        /// </summary>
        public string path { get; set; }

        /// <summary>
        /// 是否禁用
        /// </summary>
        public bool disabled { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string component { get; set; }

        /// <summary>
        /// 重定向
        /// </summary>
        public string redirect { get; set; }

        /// <summary>
        /// 菜单属性
        /// </summary>
        public meta meta { get; set; }

        public List<OutMenu> children { get; set; }
    }

    /// <summary>
    /// 菜单属性
    /// </summary>
    public class meta
    {
        /// <summary>
        /// 路由title  一般必填
        /// </summary>
        public string title { get; set; }

        /// <summary>
        /// 是否忽略权限，只在权限模式为Role的时候有效
        /// </summary>
        public bool? ignoreAuth { get; set; } = true;

        // 可以访问的角色，只在权限模式为Role的时候有效
        /// roles?: RoleEnum[];

        /// <summary>
        ///是否忽略KeepAlive缓存
        /// </summary>
        public bool? ignoreKeepAlive { get; set; } = false;

        /// <summary>
        ///是否固定标签
        /// </summary>
        public bool? affix { get; set; } = false;

        /// <summary>
        ///  图标，也是菜单图标
        /// </summary>
        public string icon { get; set; }

        /// <summary>
        /// 内嵌iframe的地址
        /// </summary>
        public string frameSrc { get; set; }

        /// <summary>
        /// 指定该路由切换的动画名
        /// </summary>
        public string transitionName { get; set; }

        /// <summary>
        /// 隐藏该路由在面包屑上面的显示
        /// </summary>
        public bool? hideBreadcrumb { get; set; } = false;

        /// <summary>
        /// 如果该路由会携带参数，且需要在tab页上面显示。则需要设置为true
        /// </summary>
        public bool? carryParam { get; set; } = false;

        /// <summary>
        /// 当前激活的菜单。用于配置详情页时左侧激活的菜单路径
        /// </summary>
        public string currentActiveMenu { get; set; }

        /// <summary>
        /// 当前路由不再标签页显示
        /// </summary>
        public bool? hideTab { get; set; } = false;

        /// <summary>
        /// 当前路由不再菜单显示
        /// </summary>
        public bool? hideMenu { get; set; } = false;
    }

    /// <summary>
    /// 菜单标签设置
    /// </summary>
    public class tag
    {
        /// <summary>
        /// 为true则显示小圆点
        /// </summary>
        public bool? dot { get; set; } = false;

        /// <summary>
        /// 内容
        /// </summary>
        public string content { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public TagType type { get; set; }
    }
}