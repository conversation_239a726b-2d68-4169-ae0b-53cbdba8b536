﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>32997513-307a-490a-83c7-e1571114e507</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <DockerfileContext>..\..</DockerfileContext>
    <DockerComposeProjectPath>..\..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.DependencyValidation.Analyzers" Version="0.11.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1-Preview.1" />
  </ItemGroup>
	<ItemGroup>
		<Content Include="$(NuGetPackageRoot)\ssic.infrastructure\2.0.0\contentFiles\any\any\appsettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="$(NuGetPackageRoot)\ssic.infrastructure\2.0.0\contentFiles\any\any\Corssettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="$(NuGetPackageRoot)\ssic.infrastructure\2.0.0\contentFiles\any\any\DbInfosettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\SSIC.Infrastructure\SSIC.Infrastructure.csproj" />
	  <ProjectReference Include="..\SSIC.Utilities\SSIC.Utilities.csproj" />
	</ItemGroup>


</Project>
