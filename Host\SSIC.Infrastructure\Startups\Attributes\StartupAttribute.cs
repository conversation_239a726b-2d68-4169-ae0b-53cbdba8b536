﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups
{
    /// <summary>
    /// 项目启动项特性
    /// </summary>
    [AttributeUsage(AttributeTargets.Class, Inherited = false, AllowMultiple = false)]
    public sealed class StartupAttribute : Attribute
    {
        /// <summary>
        /// 排序，执行顺序由小到大
        /// </summary>
        public int Sort { get; }

        public StartupAttribute(int sort=0)
        {
            Sort = sort;
        }
    }
}