<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SSIC.DevTools</name>
    </assembly>
    <members>
        <member name="M:SSIC.DevTools.BuilderTools.CreateProject.LoadPathFile(System.String,System.String,System.String,System.String,System.String,System.Boolean)">
             <summary>
            读取Html文件生成文件
             </summary>
             <param name="path">文件地址</param>
             <param name="Templatepath">模板路径</param>
             <param name="BusinessName">业务模块</param>
             <param name="Filename">文件名</param>
             <param name="IsRepalce">是否替换</param>
        </member>
        <member name="M:SSIC.DevTools.Controllers.WeatherForecastController.CreateFiles(System.String)">
            <summary>
            生成后台模板
            </summary>
            <returns></returns>
        </member>
    </members>
</doc>
