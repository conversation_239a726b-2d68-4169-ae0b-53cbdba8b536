﻿using FreeSql.DataAnnotations;
using System;

namespace SSIC.Infrastructure.Entity.EntityBase.Composition
{
    public class DocBase : EntityBasic, IEntityDoc
    {
        /// <summary>
        /// 单据号
        /// </summary>
        [Column(StringLength = 50)]
        public string docno { get; set; } //50

        /// <summary>
        /// 单据类型
        /// </summary>
        public int? doctype { get; set; } = 0;//3

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? docdate { get; set; } = DateTime.Now;

        /// <summary>
        /// 岗位id
        /// </summary>
        public Guid? pid { get; set; }

        /// <summary>
        /// 员工ID
        /// </summary>
        public Guid? staffid { get; set; }


    }
}