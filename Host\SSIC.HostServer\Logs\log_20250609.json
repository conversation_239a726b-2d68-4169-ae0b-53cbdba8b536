{"@t":"2025-06-09T02:20:15.4662797Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:15.5709098Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:15.5717030Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:15.6116537Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:15.6120563Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:15.7701710Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:15.8031191Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:15.8033851Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:15.8073386Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:15.8113135Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:15.8155827Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:15.8160648Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:15.8162218Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:15.8162943Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:16.3946369Z","@mt":"Now listening on: {address}","address":"https://localhost:54741","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:16.3982160Z","@mt":"Now listening on: {address}","address":"http://localhost:54742","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:16.4701039Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:16.4705388Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:16.4707027Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:16.9595608Z","@mt":"首次请求触发端点刷新","@tr":"32063845a067fc1972378642d51413e5","@sp":"f7f9cdbc906125b5","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HND6VGMA9K47:00000001","RequestPath":"/dapr/config","ConnectionId":"0HND6VGMA9K47","MachineName":"DESKTOP-O211UJ1","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-06-09T02:20:16.9667149Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"32063845a067fc1972378642d51413e5","@sp":"f7f9cdbc906125b5","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HND6VGMA9K47:00000001","RequestPath":"/dapr/config","ConnectionId":"0HND6VGMA9K47","MachineName":"DESKTOP-O211UJ1","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
