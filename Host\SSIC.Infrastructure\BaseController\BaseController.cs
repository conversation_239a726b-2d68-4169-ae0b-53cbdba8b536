﻿using FreeSql.Internal.Model;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SSIC.Infrastructure.BaseProvider;
using SSIC.Infrastructure.Entity;
using SSIC.Infrastructure.Entity.EntityBase;
using SSIC.Infrastructure.Entity.EntityBase.Composition;
using SSIC.Infrastructure.Enums;
using SSIC.Infrastructure.OptionsEntity;
using SSIC.Infrastructure.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.BaseController
{
    public class BaseController<TEntity, TService> : ControllerBase where TEntity : class
        where TService : IServiceBase<TEntity>
    {
        protected readonly TService Service;

        protected readonly ILogger<BaseController<TEntity, TService>> Logger;

        public BaseController(TService service, ILogger<BaseController<TEntity, TService>> logger)
        {
            Service = service;
            Logger = logger;
        }

        /// <summary>
        /// 新增一条数据
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        [HttpPost, Route("post/[action]")]
        public async Task<ActionResult> Insert(TEntity entities)
        {
            var InsertInfo = await Service.InsertAsync(entities);
            return Ok(new OutputView { c = OutCode.Success, d = InsertInfo, m = "新增数据成功!" });
        }

        /// <summary>
        /// 新增多条数据
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        //[Permission(ActionPermissionOption.Add)]
        [HttpPost, Route("post/[action]")]
        public async Task<ActionResult> InsertList(List<TEntity> entities)
        {
            var InsertInfo = await Service.InsertAsyncList(entities);
            return Ok(new OutputView { c = OutCode.Success, d = null, m = $"新增数据{InsertInfo.Count}条!" });
        }

        /// <summary>
        /// 修改一条数据
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        //[Permission(ActionPermissionOption.Update)]
        [HttpPost, Route("post/[action]")]
        public async Task<ActionResult> Update(TEntity entities)
        {
            if (entities is EntityBasic e)
            {
                if (e.id == Guid.Empty)
                {
                    return Ok(new OutputView { c = OutCode.Fail, d = null, m = "请勿提交关键ID为空的数据!" });
                }
                if (entities is IEntityApprove k)
                    if (k.check == 1)
                    {
                        return Ok(new OutputView { c = OutCode.Fail, d = null, m = "改数据已经审核不能再修改!" });
                    }

                e.modifytime = DateTime.Now;
                var Entity = await Service.UpdateAsync(entities);
                return Ok(new OutputView { c = OutCode.Success, d = null, m = "数据修改完成!" });
            }

            return Ok(new OutputView { c = OutCode.Fail, d = null, m = "实体类型有误!" });
        }

        /// <summary>
        /// 批量修改数据
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        //[Permission(ActionPermissionOption.Update)]
        [HttpPost, Route("post/[action]")]
        public async Task<ActionResult> UpdateList(List<TEntity> entities)
        {
            var FindList = entities.OfType<ApproveBasic>().Where(n => n.id != Guid.Empty && n.check != 1).ToList();
            if (entities == null || entities.Count == 0)
            {
                return Ok(new OutputView { c = OutCode.Fail, d = null, m = "请勿提交空的数据!" });
            }
            if (FindList.Count > 0)
            {
                FindList.ForEach(n => n.modifytime = DateTime.Now);
                var Entity = await Service.UpdateAsync(FindList.OfType<TEntity>().ToList());
            }
            return Ok(new OutputView { c = OutCode.Success, d = null, m = $"批量接收数据{entities.Count}条;修改数据{FindList.Count}条;!无效数据{entities.Count - FindList.Count}" });
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        //[Permission(ActionPermissionOption.Delete)]
        [HttpPost, Route("post/[action]")]
        public async Task<ActionResult> Delete(TEntity entities)
        {
            if (entities is ApproveBasic e)
            {
                if (e.id == Guid.Empty)
                {
                    return Ok(new OutputView { c = OutCode.Fail, d = null, m = "请勿提交关键ID为空的数据!" });
                }

                if (e.check == 1)
                {
                    return Ok(new OutputView { c = OutCode.Success, d = null, m = "该数据已审核无法删除!" });
                }

                if (e.isdelete == false)
                {
                    e.isdelete = true;
                    var Entity = await Service.UpdateAsync(entities);
                    return Ok(new OutputView { c = OutCode.Success, d = null, m = "数据删除成功!" });
                }

                return Ok(new OutputView { c = OutCode.Fail, d = null, m = "该数据已经删除，无法再删除，请尝试刷新页面!" });
            }
            return Ok(new OutputView { c = OutCode.Fail, d = null, m = "实体类型有误!" });
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        //[Permission(ActionPermissionOption.Delete)]
        [HttpPost, Route("post/[action]")]
        public async Task<ActionResult> DeleteList(List<TEntity> entities)
        {
            if (entities == null || entities.Count == 0)
            {
                return Ok(new OutputView { c = OutCode.Fail, d = null, m = "请勿提交空的数据!" });
            }

            var FindList = entities.OfType<ApproveBasic>().Where(n => n.isdelete == false && n.check != 1).ToList();
            if (FindList.Count > 0)
            {
                FindList.ForEach(n => n.modifytime = DateTime.Now);
                FindList.ForEach(n => n.isdelete = true);
                var Entity = await Service.UpdateAsync(FindList.OfType<TEntity>().ToList());
            }
            return Ok(new OutputView { c = OutCode.Success, d = null, m = $"批量接收数据{entities.Count}条;删除数据{FindList.Count}条;!无效数据{entities.Count - FindList.Count}!" });
        }

        /// <summary>
        /// 数据单条审核
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        //[Permission(ActionPermissionOption.Check)]
        [HttpPost, Route("post/[action]")]
        public async Task<ActionResult> Check(TEntity entities)
        {
            if (entities is ApproveBasic e)
            {
                if (e.id == Guid.Empty)
                {
                    return Ok(new OutputView { c = OutCode.Fail, d = null, m = "请勿提交空的数据!" });
                }

                if (e.check == 1)
                {
                    return Ok(new OutputView { c = OutCode.Fail, d = null, m = "该数据已审核，请勿重复审核!" });
                }
                e.check = 1;
                e.checktime = DateTime.Now;
                var Entity = await Service.UpdateAsync(entities);
                return Ok(new OutputView { c = OutCode.Success, d = null, m = "数据审核成功!" });
            }
            return Ok(new OutputView { c = OutCode.Fail, d = null, m = "实体类型有误!" });
        }

        /// <summary>
        /// 数据批量审核
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        //[Permission(ActionPermissionOption.Check)]
        [HttpPost, Route("post/[action]")]
        public async Task<ActionResult> CheckList(List<TEntity> entities)
        {
            if (entities == null || entities.Count == 0)
            {
                return Ok(new OutputView { c = OutCode.Fail, d = null, m = "请勿提交空的数据!" });
            }

            var FindList = entities.OfType<ApproveBasic>().Where(n => n.check != 1).ToList();
            if (FindList.Count > 0)
            {
                FindList.ForEach(n => n.check = 1);
                FindList.ForEach(n => n.checktime = DateTime.Now);
                var Entity = await Service.UpdateAsync(FindList.OfType<TEntity>().ToList());
            }
            return Ok(new OutputView { c = OutCode.Success, d = null, m = $"批量接收数据{entities.Count}条;删除数据{FindList.Count}条;!无效数据{entities.Count - FindList.Count}!" });
        }

        /// <summary>
        /// 获取分页数据
        /// </summary>
        /// <param name="loadData"></param>
        /// <returns></returns>
        //[Permission(ActionPermissionOption.Search)]
        [HttpPost, Route("post/[action]"), AllowAnonymous]
        public async Task<ActionResult> PageData(PageData loadData)
        {
            DynamicFilterInfo dyfilter = new DynamicFilterInfo();
            //if (loadData.Wheres.Any()) 
            var pageData = await Service.QueryPageDynStr(loadData.Wheres, loadData.Page, loadData.Rows);
            return Ok(new OutputView
            {
                c = OutCode.Success,
                d = new OutTable
                {
                    items = pageData.Item2,
                    total = pageData.Item1
                },
                m = $"共查询数据{pageData.Item1}条,返回{(pageData.Item1 > loadData.Rows ? loadData.Rows : pageData.Item1)}条"
            });
        }
    }
}