﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <PackageOutputPath>../SSIC.Packages</PackageOutputPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="HotReload\**" />
    <EmbeddedResource Remove="HotReload\**" />
    <None Remove="HotReload\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Swagger\UI\index-mini-profiler.html" />
    <None Remove="Swagger\UI\index.html" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Swagger\UI\index-mini-profiler.html" />
    <EmbeddedResource Include="Swagger\UI\index.html" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Configuration\" />
    <Folder Include="Orm\Fillter\" />
    <Folder Include="Caching\" />
    <Folder Include="Monitoring\" />
    <Folder Include="Startups\Endpoints\" />
    <Folder Include="Startups\HotReload\" />
    <Folder Include="Startups\ModuleManager\" />
    <Folder Include="Template\Sln\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Dapr.AspNetCore" Version="1.15.3" />
    <PackageReference Include="FreeRedis" Version="1.3.6" />
    <PackageReference Include="FreeScheduler" Version="2.0.33" />
    <PackageReference Include="FreeSql.All" Version="3.5.202" />
    <PackageReference Include="FreeSql.Cloud" Version="2.0.1" />
    <PackageReference Include="Mapster" Version="7.4.2-pre02" />
    <PackageReference Include="Masa.Contrib.Development.DaprStarter.AspNetCore" Version="1.2.0-preview.6" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="10.0.0-preview.3.25172.1" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
    <PackageReference Include="Microsoft.DependencyValidation.Analyzers" Version="0.11.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="10.0.0-preview.3.25171.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="10.0.0-preview.3.25171.5" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="10.0.0-preview.3.25171.5" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="10.0.0-preview.3.25171.5" />
    <PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="9.4.0" />
    <PackageReference Include="Microsoft.Extensions.ServiceDiscovery" Version="9.2.0" />
    <PackageReference Include="MiniProfiler.AspNetCore" Version="4.5.4" />
    <PackageReference Include="MiniProfiler.AspNetCore.Mvc" Version="4.5.4" />

    <PackageReference Include="Npgsql" Version="9.0.3" />

    <PackageReference Include="OpenTelemetry" Version="1.11.2" />

    <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.11.2" />

    <PackageReference Include="OpenTelemetry.Exporter.Zipkin" Version="1.11.2" />

    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.11.2" />

    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.11.1" />

    <PackageReference Include="OpenTelemetry.Instrumentation.GrpcCore" Version="1.0.0-beta.6" />

    <PackageReference Include="OpenTelemetry.Instrumentation.GrpcNetClient" Version="1.11.0-beta.2" />

    <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.11.1" />

    <PackageReference Include="OpenTelemetry.Instrumentation.Process" Version="1.11.0-beta.2" />

    <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.11.1" />

    <PackageReference Include="Scalar.AspNetCore" Version="2.1.16" />
    <PackageReference Include="Serilog" Version="4.2.1-dev-02352" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageReference Include="Serilog.Expressions" Version="5.1.0-dev-02301" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.1-dev-02307" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.1" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.1-dev-02317" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.1-dev-00953" />
    <PackageReference Include="Serilog.Sinks.OpenTelemetry" Version="4.2.0-dev-02302" />
    <PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
  </ItemGroup>

  <ItemGroup>
	  <Content Include="appsettings.json" Pack="true" PackagePath="contentFiles\any\any\">
	    <CopyToOutputDirectory>Never</CopyToOutputDirectory>
	  </Content>
	  <Content Include="Corssettings.json" Pack="true" PackagePath="contentFiles\any\any\">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Include="DbInfosettings.json" Pack="true" PackagePath="contentFiles\any\any\">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	  <Content Include="modulesettings.json" Pack="true" PackagePath="contentFiles\any\any\">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
  </ItemGroup>

</Project>
