﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.DependencyInjection;
using SSIC.Infrastructure.Startups.Endpoints;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace SSIC.Infrastructure.Startups.Extensions
{
    /// <summary>
    /// 注册模块服务
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddModuleServices(this IServiceCollection services, Assembly moduleAssembly)
        {
            // 查找模块中的 Startup 类或其他配置类
            var startupType = moduleAssembly.GetTypes()
                .FirstOrDefault(t => t.Name == "Startup" || t.Name.EndsWith("Module"));

            if (startupType != null)
            {
                // 尝试调用 ConfigureServices 方法
                var configureServicesMethod = startupType.GetMethod("ConfigureServices");
                if (configureServicesMethod != null)
                {
                    var startup = Activator.CreateInstance(startupType);
                    configureServicesMethod.Invoke(startup, new object[] { services });
                }
            }

            // 获取模块名称
            var assemblyName = moduleAssembly.GetName().Name;
            if (assemblyName != null && assemblyName.StartsWith("SSIC.Modules."))
            {
                try
                {
                    // 1. 提取模块名称
                    var moduleName = assemblyName.Split('.')[2].ToLower();
                    var routePrefix = $"api/{moduleName}";

                    // 2. 找出模块中的所有控制器
                    var controllers = moduleAssembly.GetTypes()
                        .Where(t =>
                            !t.IsAbstract &&
                            t.IsPublic &&
                            (
                                t.Name.EndsWith("Controller") ||
                                t.GetCustomAttributes<ControllerAttribute>().Any()
                            ))
                        .ToList();

                    // 3. 为所有控制器添加路由前缀
                    services.Configure<MvcOptions>(options =>
                    {
                        options.Conventions.Add(new ModuleRoutePrefixConvention(moduleAssembly, routePrefix));
                    });

                    // 4. 直接将控制器添加到DynamicEndpointManager
                    var dynamicEndpointManager = DynamicEndpointManager.Instance;
                    if (dynamicEndpointManager != null)
                    {
                        // 从已卸载记录中移除当前模块
                        dynamicEndpointManager.RecordModuleLoaded(assemblyName);

                        Console.WriteLine($"模块 {assemblyName} 已加载，包含 {controllers.Count} 个控制器");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"处理模块 {assemblyName} 时出错: {ex.Message}");
                }
            }
            Console.WriteLine($"处理模块23333{moduleAssembly}");

            // 只添加应用程序部件，不重复调用AddControllers
            // 获取现有的MVC构建器并添加应用程序部件
            var serviceProvider = services.BuildServiceProvider();
            var partManager = serviceProvider.GetService<ApplicationPartManager>();
            if (partManager != null)
            {
                partManager.ApplicationParts.Add(new AssemblyPart(moduleAssembly));
                Console.WriteLine($"已将模块 {moduleAssembly.GetName().Name} 添加到ApplicationPartManager");
            }
            else
            {
                // 如果没有找到ApplicationPartManager，则配置MVC选项
                services.Configure<MvcOptions>(options =>
                {
                    // 这里可以添加其他MVC配置
                });
                Console.WriteLine($"未找到ApplicationPartManager，已配置MVC选项");
            }

            return services;
        }
    }

    /// <summary>
    /// 模块路由前缀约定类
    /// </summary>
    public class ModuleRoutePrefixConvention : IApplicationModelConvention
    {
        private readonly Assembly _moduleAssembly;
        private readonly string _routePrefix;

        public ModuleRoutePrefixConvention(Assembly moduleAssembly, string routePrefix)
        {
            _moduleAssembly = moduleAssembly;
            _routePrefix = routePrefix;
        }

        public void Apply(ApplicationModel application)
        {
            foreach (var controller in application.Controllers)
            {
                // 只处理特定模块中的控制器
                if (controller.ControllerType.Assembly == _moduleAssembly)
                {
                    // 获取控制器名称（去掉Controller后缀并转为小写）
                    var controllerName = controller.ControllerName.ToLowerInvariant();
                    var controllerRoute = $"{_routePrefix}/{controllerName}";

                    // 为控制器设置路由前缀
                    if (controller.Selectors.Count == 0)
                    {
                        // 如果没有选择器，创建一个默认的
                        controller.Selectors.Add(new SelectorModel());
                    }

                    foreach (var selector in controller.Selectors)
                    {
                        if (selector.AttributeRouteModel != null)
                        {
                            // 修改现有的路由模板
                            var template = selector.AttributeRouteModel.Template;
                            if (string.IsNullOrEmpty(template) || template == "[controller]")
                            {
                                selector.AttributeRouteModel.Template = controllerRoute;
                            }
                            else
                            {
                                selector.AttributeRouteModel.Template = $"{controllerRoute}/{template}";
                            }
                        }
                        else
                        {
                            // 创建新的路由模板
                            selector.AttributeRouteModel = new AttributeRouteModel
                            {
                                Template = controllerRoute
                            };
                        }
                    }
                }
            }
        }
    }
}