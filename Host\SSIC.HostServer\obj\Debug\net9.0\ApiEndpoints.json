[{"ContainingType": "SSIC.Modules.Sys.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "api/sys/WeatherForecast/api/Hello", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SSIC.Modules.Sys.WeatherForecast, SSIC.Modules.Sys, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SSIC.HostServer.Controllers.WeatherForecast2Controller", "Method": "Get", "RelativePath": "WeatherForecast2", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast33333"}]