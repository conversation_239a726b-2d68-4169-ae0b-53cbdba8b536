﻿using SSIC.Infrastructure.ConfigurableOptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.OptionsEntity
{
    public class ZipkinOptions : IConfigurableOptions
    {
        /// <summary>
        ///端点
        /// </summary>
        public string Endpoint
        {
            get; set;

        }
    }
}
