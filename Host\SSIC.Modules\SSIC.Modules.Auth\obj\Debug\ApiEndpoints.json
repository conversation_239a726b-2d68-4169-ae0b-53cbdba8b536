[{"ContainingType": "SSIC.Infrastructure.Startups.HostBuilder+<>c__DisplayClass4_0", "Method": "<Configure>b__3", "RelativePath": "api/endpoints/map-module-route", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "SSIC.Infrastructure.Startups.HostBuilder+<>c__DisplayClass4_0", "Method": "<Configure>b__2", "RelativePath": "api/endpoints/refresh", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "SSIC.Infrastructure.Startups.HostBuilder+<>c__DisplayClass4_0", "Method": "<Configure>b__4", "RelativePath": "api/openapi/refresh", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "Tags": ["System"], "Summary": "手动刷新OpenAPI文档"}]