﻿using Google.Api;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Reflection;
using Microsoft.OpenApi.Models;
using Scalar.AspNetCore;
using SSIC.Infrastructure.Authentication;
using SSIC.Infrastructure.OpenApi;
using SSIC.Infrastructure.ConfigurableOptions.Realization;
using SSIC.Infrastructure.Dapr;
using SSIC.Infrastructure.DependencyInjection.Extensions;
using SSIC.Infrastructure.Logging;
using SSIC.Infrastructure.OptionsEntity;
using SSIC.Infrastructure.Orm;
using SSIC.Infrastructure.Startups.Endpoints;
using SSIC.Infrastructure.Startups.HotReload;
using StackExchange.Profiling;
using StackExchange.Profiling.Storage;
using System.IO;
using System.Reflection;
namespace SSIC.Infrastructure.Startups
{
    /// <summary>
    /// 配置项目启动项
    /// </summary>
    [Startup(1000)]
    public sealed class HostBuilder : IStartups
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<HostBuilder> _logger;
        public HostBuilder(IConfiguration configuration, ILogger<HostBuilder> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }
     

        public void ConfigureServices(IServiceCollection services)
        {
            // 注册动态路由支持
           
            // 获取当前环境
            var env = services.BuildServiceProvider().GetRequiredService<IWebHostEnvironment>();
            // 配置 Dapr 客户端
            services.AddDaprClientProvider();

            //services.AddConfigurableOptions<CorsOptions>();
            services.AddControllers();
            //扫描项目中继承作用域接口的类，并注册
            services.AddDependencyInjection();

            // 注册动态模块OpenAPI转换器
            services.AddScoped<DynamicModuleDocumentTransformer>();
            // 添加健康检查服务
            //services.AddHealthChecks();

            // 配置Swagger
            //services.AddSwaggerGen(c =>
            //{
            //    c.SwaggerDoc("v1", new OpenApiInfo { Title = "SSIC.WebApi", Version = "v1" });

            //    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            //    {
            //        Name = "Authorization",
            //        Type = SecuritySchemeType.ApiKey,
            //        Scheme = "Bearer",
            //        BearerFormat = "JWT",
            //        In = ParameterLocation.Header,
            //        Description = "在下方填入 Bearer 空格 + token，例如: \"Bearer {token}\"",
            //    });
            //    c.AddSecurityRequirement(new OpenApiSecurityRequirement
            //    {
            //        {
            //            new OpenApiSecurityScheme
            //            {
            //                Reference = new OpenApiReference
            //                {
            //                    Type = ReferenceType.SecurityScheme,
            //                    Id = "Bearer"
            //                }
            //            },
            //            new string[] {}
            //        }
            //    });

            //    // 包含XML注释
            //    var xmlFile = AppContext.BaseDirectory;
            //    var xmlDocumentationFiles = Directory.GetFiles(xmlFile, "*.xml");
            //    foreach (var xmlFilePath in xmlDocumentationFiles)
            //    {
            //        c.IncludeXmlComments(xmlFilePath);
            //    }
            //});
            // 配置 Scalar
            // 添加OpenApi服务，这是Scalar所需的
            services.AddOpenApi(options =>
            {
                options.AddDocumentTransformer((document, context, cancellationToken) =>
                {
                    document.Info = new()
                    {
                        Title = "SSIC 模块化API",
                        Version = "V1",
                        Description = "SSIC 模块化架构API文档，支持动态模块加载"
                    };

                    return Task.CompletedTask;
                });

                // 添加动态模块文档转换器
                options.AddDocumentTransformer<DynamicModuleDocumentTransformer>();

                // 添加操作转换器来处理动态路由
                options.AddOperationTransformer((operation, context, cancellationToken) =>
                {
                    // 为动态路由添加标签
                    if (context.Description.ActionDescriptor is ControllerActionDescriptor controllerAction)
                    {
                        var assemblyName = controllerAction.ControllerTypeInfo.Assembly.GetName().Name;
                        if (assemblyName?.StartsWith("SSIC.Modules.") == true)
                        {
                            var moduleName = assemblyName.Replace("SSIC.Modules.", "");
                            operation.Tags = new List<OpenApiTag> { new OpenApiTag { Name = moduleName } };
                        }
                    }

                    return Task.CompletedTask;
                });

                // 如果不是开发模式才添加 BearerSecuritySchemeTransformer
                if (!env.IsDevelopment())
                {
                    options.AddDocumentTransformer<BearerSecuritySchemeTransformer>();
                }
            });
        

            // 添加MiniProfiler服务
            services.AddMiniProfiler(options =>
            {
                options.RouteBasePath = "/profiler"; // 设置MiniProfiler的路径
                (options.Storage as MemoryCacheStorage).CacheDuration = TimeSpan.FromMinutes(10);
            }); // 如果你使用的是EF Core
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            services.AddSingleton<IAuthorizationHandler, PermissionHandler>();
            services.AddSingleton<IAuthorizationPolicyProvider, PermissionPolicyProvider>();
            services.AddTokenAuthentication(_configuration);
            services.AddHttpContextAccessor();

            var corsOption = services.GetOptions<CorsOptions>();
            services.AddCors(c =>
            {
                c.AddPolicy(corsOption.PolicyName, policy =>
                {
                    policy.AllowAnyHeader()
                          .AllowAnyMethod();
                         
                    if (corsOption.IsUse == true)
                    {
                        policy.WithOrigins(corsOption.WithOrigins).AllowCredentials();
                    }
                    else { 
                        policy.AllowAnyOrigin();
                    }
                });
            });
            
            // 注册新的FixedEndpointDataSource
            services.AddSingleton<FixedEndpointDataSource>();
            
            // 仍然注册DynamicEndpointDataSource以保持兼容性
            services.AddSingleton<DynamicEndpointDataSource>(sp => 
            {
                return DynamicEndpointManager.Instance.GetDynamicEndpoints() as DynamicEndpointDataSource;
            });
        }

        public void Configure(WebApplication app)
        {
            // 配置顺序很重要
            app.UseDeveloperExceptionPage();
           
            //// 使用Swagger中间件
            //app.UseSwagger();

            //app.UseSwaggerUI(c =>
            //{
            //    c.IndexStream = () => GetType().GetTypeInfo().Assembly.GetManifestResourceStream("SSIC.Infrastructure.Swagger.UI.index-mini-profiler.html");
            //    c.SwaggerEndpoint("/swagger/v1/swagger.json", "SSIC.WebApi v1");
            //});

            app.UseAuthentication();
            app.UseContextMiddleware(); // 引入自定义的 HttpContextMiddleware 中间件
            
            // 在路由之前使用中间件方式触发路由刷新
            ((IApplicationBuilder)app).UseMiddleware<EndpointRefreshMiddleware>();
            
            app.UseHttpsRedirection(); // 取消 https 验证
            app.UseRouting();
            var corsOption = app.Services.GetOptions<CorsOptions>();

            // 确保使用正确的 CORS 策略名称
            app.UseCors(corsOption.PolicyName);

            app.UseAuthorization();
            // 使用MiniProfiler中间件
            app.UseMiniProfiler();

            app.UseEndpoints(endpoints =>
            {
                // 获取FixedEndpointDataSource
                var fixedDataSource = app.Services.GetRequiredService<FixedEndpointDataSource>();
                
                // 添加到端点构建器
                endpoints.DataSources.Add(fixedDataSource);
                
                // 添加一个"测试"模块路由，检验路由是否正常工作
                endpoints.MapGet("/api/test-module-route", async context =>
                {
                    await context.Response.WriteAsync("测试模块路由正常工作!");
                });
                
                // 直接添加一个手动路由到api/Hello
                endpoints.MapGet("/api/Hello", async context =>
                {
                    await context.Response.WriteAsync("手动添加的Hello路由正常工作!");
                });
                
                // 获取已加载的模块程序集
                var loadedAssemblies = AppDomain.CurrentDomain.GetAssemblies()
                    .Where(a => a.GetName().Name?.StartsWith("SSIC.Modules.") == true)
                    .ToList();
                
                if (loadedAssemblies.Any())
                {
                    _logger.LogInformation("在UseEndpoints中发现 {Count} 个模块程序集", loadedAssemblies.Count);
                    
                    // 使用端点管理器刷新路由
                    var endpointManager = DynamicEndpointManager.Instance;
                    endpointManager.RefreshEndpoints(app.Services, loadedAssemblies);
                }
            });

            // 在开发环境中启用Scalar
            if (app.Environment.IsDevelopment())
            {
                app.MapScalarApiReference(opt =>
                {
                    opt.Title = "Scalar Example2";
                    opt.Theme = ScalarTheme.Kepler;
                    opt.Servers = [];
                    opt.DefaultHttpClient = new(ScalarTarget.Http, ScalarClient.Http11);
                });


                app.MapOpenApi();//映射OpenApi文档路径
            }
            
            // 手动触发一次动态端点刷新，确保正常启动时也能正确加载控制器路由
            try
            {
                var endpointManager = SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.Instance;
                if (endpointManager != null)
                {
                    // 获取已加载的插件程序集
                    var loadedAssemblies = AppDomain.CurrentDomain.GetAssemblies()
                        .Where(a => a.GetName().Name?.StartsWith("SSIC.Modules.") == true)
                        .ToList();

                    // 刷新路由端点
                    endpointManager.RefreshEndpoints(app.Services, loadedAssemblies);
                    _logger.LogInformation("应用启动时已手动刷新路由端点，共 {Count} 个模块", loadedAssemblies.Count);
                    
                    // 手动触发固定端点数据源的刷新
                    var fixedDataSource = app.Services.GetService<FixedEndpointDataSource>();
                    if (fixedDataSource != null)
                    {
                        fixedDataSource.ForceRefresh();
                        _logger.LogInformation("已强制刷新FixedEndpointDataSource");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用启动时手动刷新路由端点失败");
            }
            
            // 添加一个手动刷新路由的API端点
            app.MapGet("/api/endpoints/refresh", (HttpContext httpContext) => {
                try {
                    // 获取已加载的模块程序集
                    var loadedAssemblies = AppDomain.CurrentDomain.GetAssemblies()
                        .Where(a => a.GetName().Name?.StartsWith("SSIC.Modules.") == true)
                        .ToList();
                    
                    // 获取端点管理器
                    var endpointManager = SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.Instance;
                    
                    // 先重建端点
                    endpointManager.RebuildEndpoints(app.Services, loadedAssemblies);
                    
                    // 再刷新端点
                    endpointManager.RefreshEndpoints(app.Services, loadedAssemblies);
                    
                    // 强制刷新FixedEndpointDataSource
                    var fixedDataSource = app.Services.GetService<FixedEndpointDataSource>();
                    if (fixedDataSource != null)
                    {
                        fixedDataSource.ForceRefresh();
                    }
                    
                    _logger.LogInformation("手动API触发端点刷新完成，发现 {Count} 个模块", loadedAssemblies.Count);
                    
                    return Results.Ok(new { 
                        message = "端点刷新成功", 
                        modules = loadedAssemblies.Select(a => a.GetName().Name).ToList(),
                        dynamicEndpointCount = endpointManager.GetDynamicEndpoints().Endpoints.Count,
                        fixedEndpointCount = fixedDataSource?.Endpoints.Count ?? 0
                    });
                }
                catch (Exception ex) {
                    _logger.LogError(ex, "API刷新端点失败");
                    return Results.Problem("端点刷新失败: " + ex.Message);
                }
            });
            
            // 添加一个直接映射模块路由的API端点
            app.MapGet("/api/endpoints/map-module-route", (HttpContext httpContext) => {
                try {
                    // 强制注册已知的模块路由
                    app.MapGet("/api/Hello", async context =>
                    {
                        await context.Response.WriteAsync("手动添加的Hello路由正常工作!");
                    });
                    
                    // 获取FixedEndpointDataSource
                    var fixedDataSource = app.Services.GetService<FixedEndpointDataSource>();
                    if (fixedDataSource != null)
                    {
                        // 创建端点
                        var routePattern = Microsoft.AspNetCore.Routing.Patterns.RoutePatternFactory.Parse("/api/Hello");
                        var endpoint = new RouteEndpoint(
                            async context => await context.Response.WriteAsync("手动创建的Hello端点"),
                            routePattern,
                            0,
                            new EndpointMetadataCollection(),
                            "Manual: /api/Hello"
                        );
                        
                        // 添加到数据源
                        fixedDataSource.AddEndpoint(endpoint);
                        fixedDataSource.ForceRefresh();
                        
                        _logger.LogInformation("已手动添加/api/Hello路由");
                    }
                    
                    return Results.Ok(new { 
                        message = "已手动添加/api/Hello路由" 
                    });
                }
                catch (Exception ex) {
                    _logger.LogError(ex, "手动添加模块路由失败");
                    return Results.Problem("手动添加模块路由失败: " + ex.Message);
                }
            });

            // 添加OpenAPI文档刷新端点
            app.MapPost("/api/openapi/refresh", async (HttpContext httpContext) => {
                try {
                    _logger.LogInformation("收到OpenAPI文档刷新请求");

                    // 获取已加载的模块程序集
                    var loadedAssemblies = AppDomain.CurrentDomain.GetAssemblies()
                        .Where(a => a.GetName().Name?.StartsWith("SSIC.Modules.") == true)
                        .ToList();

                    // 1. 先刷新路由端点
                    var endpointManager = SSIC.Infrastructure.Startups.Endpoints.DynamicEndpointManager.Instance;
                    endpointManager.RebuildEndpoints(app.Services, loadedAssemblies);
                    endpointManager.RefreshEndpoints(app.Services, loadedAssemblies);

                    // 2. 使用简化的OpenAPI刷新服务
                    var simpleRefreshService = app.Services.GetService<SSIC.Infrastructure.Startups.HotReload.ISimpleOpenApiRefreshService>();
                    if (simpleRefreshService != null)
                    {
                        await simpleRefreshService.RefreshAsync();
                        _logger.LogInformation("已使用简化服务刷新OpenAPI组件");
                    }
                    else
                    {
                        // 备用方法：直接刷新ActionDescriptorCollectionProvider
                        var actionDescriptorProvider = app.Services.GetService<IActionDescriptorCollectionProvider>();
                        if (actionDescriptorProvider is ActionDescriptorCollectionProvider provider)
                        {
                            var updateMethod = provider.GetType().GetMethod("Update", BindingFlags.NonPublic | BindingFlags.Instance);
                            updateMethod?.Invoke(provider, null);
                            _logger.LogInformation("已使用备用方法刷新ActionDescriptorCollectionProvider");
                        }
                    }

                    // 3. 强制刷新FixedEndpointDataSource
                    var fixedDataSource = app.Services.GetService<FixedEndpointDataSource>();
                    if (fixedDataSource != null)
                    {
                        fixedDataSource.ForceRefresh();
                    }

                    return Results.Ok(new {
                        message = "OpenAPI文档刷新成功",
                        moduleCount = loadedAssemblies.Count,
                        timestamp = DateTime.Now,
                        note = "请手动刷新Scalar页面以查看更新的API文档",
                        modules = loadedAssemblies.Select(a => a.GetName().Name).ToArray()
                    });
                } catch (Exception ex) {
                    _logger.LogError(ex, "刷新OpenAPI文档失败");
                    return Results.Problem($"刷新失败: {ex.Message}");
                }
            }).WithTags("System").WithSummary("手动刷新OpenAPI文档");
        }


        /// <summary>
        /// 模块路由移除请求模型
        /// </summary>
        private class ModuleRouteRemovalRequest
        {
            public string ModuleName { get; set; }
        }
    }
}
