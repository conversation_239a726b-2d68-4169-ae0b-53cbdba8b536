﻿using Dapr.Client;
using System;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Dapr
{
    public class DaprClientProvider
    {
        private readonly DaprClient _daprClient;

        // 使用构造函数注入 DaprClient
        public DaprClientProvider(DaprClient daprClient)
        {
            _daprClient = daprClient ?? throw new ArgumentNullException(nameof(daprClient));
        }

        /// <summary>
        /// 调用指定服务的指定方法
        /// </summary>
        /// <typeparam name="TRequest">请求数据的类型</typeparam>
        /// <typeparam name="TResponse">响应数据的类型</typeparam>
        /// <param name="appId">目标服务的 App ID</param>
        /// <param name="methodName">要调用的方法名称</param>
        /// <param name="data">请求数据</param>
        /// <returns>目标服务返回的数据</returns>
        public async Task<TResponse> InvokeServiceAsync<TRequest, TResponse>(string appId, string methodName, TRequest data)
        {
            var response = await _daprClient.InvokeMethodAsync<TRequest, TResponse>(appId, methodName, data);
            return response;
        }

        public async Task<TResponse> InvokeServiceWithLoadBalancingAsync<TRequest, TResponse>(HttpMethod httpMethod, string appId, string methodName, TRequest data)
        {
            var response = await _daprClient.InvokeMethodAsync<TRequest, TResponse>(httpMethod, appId, methodName, data);
            return response;
        }

        /// <summary>
        /// 发布事件(向指定的 pub/sub 组件发布事件)
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="pubsubName">Pub/Sub 组件的名称</param>
        /// <param name="topicName">要发布的主题名称</param>
        /// <param name="data">要发布的事件数据</param>
        /// <returns></returns>
        public async Task PublishEventAsync<T>(string pubsubName, string topicName, T data)
        {
            await _daprClient.PublishEventAsync(pubsubName, topicName, data);
        }

        /// <summary>
        /// 获取状态(从指定的状态存储中获取状态)
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storeName">状态存储的名称</param>
        /// <param name="key">要获取的状态的键</param>
        /// <returns></returns>
        public async Task<T> GetStateAsync<T>(string storeName, string key)
        {
            return await _daprClient.GetStateAsync<T>(storeName, key);
        }

        /// <summary>
        /// 保存状态(将状态保存到指定的状态存储中)
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storeName">状态存储的名称</param>
        /// <param name="key">要保存的状态的键</param>
        /// <param name="value">要保存的状态数据</param>
        /// <returns></returns>
        public async Task SaveStateAsync<T>(string storeName, string key, T value)
        {
            await _daprClient.SaveStateAsync(storeName, key, value);
        }
    }
}
