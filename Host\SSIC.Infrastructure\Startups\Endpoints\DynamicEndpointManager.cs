﻿using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Primitives;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Routing.Patterns;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Routing.Matching;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.Endpoints
{
    /// <summary>
    /// 动态端点数据源，用于管理插件的路由端点
    /// </summary>
    public class DynamicEndpointDataSource : EndpointDataSource
    {
        private readonly List<Endpoint> _endpoints = new List<Endpoint>();
        private readonly object _lock = new object();
        private readonly ILogger<DynamicEndpointDataSource> _logger;

        public DynamicEndpointDataSource() {
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            _logger = loggerFactory.CreateLogger<DynamicEndpointDataSource>();


        }

        public override IReadOnlyList<Endpoint> Endpoints => _endpoints.AsReadOnly();

        public void AddEndpoint(Endpoint endpoint)
        {
            lock (_lock)
            {
                _endpoints.Add(endpoint);
                NotifyChange();
            }
        }

        private CancellationTokenSource _cts = new CancellationTokenSource();

        public override IChangeToken GetChangeToken()
        {
            _logger.LogWarning("GetChangeToken 被调用");
            return new CancellationChangeToken(_cts.Token);
        }

        public void NotifyChange()
        {
            var previous = Interlocked.Exchange(ref _cts, new CancellationTokenSource());
            previous.Cancel(); // 触发路由系统变更感知
        }

        public void Clear()
        {
            lock (_lock)
            {
                _endpoints.Clear();
                NotifyChange();
            }
        }

        public void AddRange(IEnumerable<Endpoint> endpoints)
        {
            lock (_lock)
            {
                _endpoints.AddRange(endpoints);
                NotifyChange();
            }
        }
    }

    /// <summary>
    /// 动态端点管理器，用于管理ASP.NET Core的路由端点
    /// </summary>
    public class DynamicEndpointManager
    {
        private static readonly Lazy<DynamicEndpointManager> _instance = new Lazy<DynamicEndpointManager>(() => new DynamicEndpointManager());
        private readonly DynamicEndpointDataSource _dynamicEndpointDataSource = new DynamicEndpointDataSource();
        private readonly ILogger<DynamicEndpointManager> _logger;
        private readonly HashSet<string> _unloadedModuleNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        private static IServiceProvider _serviceProvider;

        public static DynamicEndpointManager Instance => _instance.Value;

        // 初始化方法，在服务提供程序可用后调用
        public static void Initialize(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        private DynamicEndpointManager()
        {
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            _logger = loggerFactory.CreateLogger<DynamicEndpointManager>();
        }

        /// <summary>
        /// 获取动态端点数据源
        /// </summary>
        public EndpointDataSource GetDynamicEndpoints()
        {
            return _dynamicEndpointDataSource;
        }

        /// <summary>
        /// 记录已加载的模块名称，并从未加载模块列表中移除
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        public void RecordModuleLoaded(string moduleName)
        {
            if (!string.IsNullOrEmpty(moduleName))
            {
                if (_unloadedModuleNames.Remove(moduleName))
                {
                    _logger.LogInformation("从未加载模块列表中移除已加载模块: {ModuleName}", moduleName);
                }
                else
                {
                    _logger.LogDebug("模块已经被标记为已加载: {ModuleName}", moduleName);
                }
            }
        }

        /// <summary>
        /// 记录已卸载的模块名称
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        public void RecordUnloadedModule(string moduleName)
        {
            if (!string.IsNullOrEmpty(moduleName))
            {
                _unloadedModuleNames.Add(moduleName);
                _logger.LogInformation("记录已卸载模块: {ModuleName}", moduleName);
            }
        }

        /// <summary>
        /// 清除已卸载模块记录
        /// </summary>
        public void ClearUnloadedModules()
        {
            _unloadedModuleNames.Clear();
            _logger.LogInformation("已清除所有已卸载模块记录");
        }

        /// <summary>
        /// 检查模块是否已被卸载
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>如果模块已被卸载返回true，否则返回false</returns>
        public bool IsModuleUnloaded(string moduleName)
        {
            return !string.IsNullOrEmpty(moduleName) && _unloadedModuleNames.Contains(moduleName);
        }

        /// <summary>
        /// 重建所有路由端点
        /// </summary>
        /// <param name="serviceProvider">服务提供程序</param>
        /// <param name="loadedPluginAssemblies">已加载的插件程序集</param>
        public void RebuildEndpoints(IServiceProvider serviceProvider, IEnumerable<Assembly> loadedPluginAssemblies)
        {
            try
            {
                _logger.LogInformation("开始重建路由端点...");
                
                // 清空现有端点
                _dynamicEndpointDataSource.Clear();
                _logger.LogInformation("[Refresh] DataSource Hash: {Hash}", _dynamicEndpointDataSource.GetHashCode());
                
                // 获取端点数据源
                var endpointDataSources = GetEndpointDataSources(serviceProvider);
                if (endpointDataSources == null || !endpointDataSources.Any())
                {
                    _logger.LogWarning("无法获取端点数据源，路由重建失败");
                    return;
                }
                
                // 创建已加载程序集名称的哈希集合，用于快速查找
                var loadedAssemblyNames = loadedPluginAssemblies
                    .Select(a => a.GetName().Name)
                    .ToHashSet(StringComparer.OrdinalIgnoreCase);
                
                // 收集所有有效的端点
                var validEndpoints = new List<Endpoint>();
                int skippedCount = 0;
                
                foreach (var dataSource in endpointDataSources)
                {
                    try
                    {
                        // 获取数据源中的所有端点
                        var endpoints = dataSource.Endpoints;
                        
                        foreach (var endpoint in endpoints)
                        {
                            // 检查是否是模块插件的端点
                            var assembly = GetEndpointAssembly(endpoint);
                            
                            // 跳过已卸载模块的端点
                            if (assembly != null && 
                                assembly.GetName().Name.StartsWith("SSIC.Modules.") && 
                                !loadedAssemblyNames.Contains(assembly.GetName().Name))
                            {
                                _logger.LogInformation($"跳过已卸载模块的端点: {GetEndpointDisplayName(endpoint)}");
                                skippedCount++;
                                continue;
                            }
                            
                            // 添加有效端点
                            validEndpoints.Add(endpoint);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"处理数据源时出错: {dataSource.GetType().FullName}");
                    }
                }
                
                // 将有效端点添加到动态数据源
                _dynamicEndpointDataSource.AddRange(validEndpoints);
                
                _logger.LogInformation($"路由重建完成，共 {_dynamicEndpointDataSource.Endpoints.Count} 个端点, 跳过了 {skippedCount} 个已卸载模块的端点");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重建路由端点时发生错误");
            }
        }

        /// <summary>
        /// 获取所有端点数据源
        /// </summary>
        private IEnumerable<EndpointDataSource> GetEndpointDataSources(IServiceProvider serviceProvider)
        {
            try
            {
                // 直接从DI容器获取所有EndpointDataSource实例
                var dataSources = serviceProvider.GetService(typeof(IEnumerable<EndpointDataSource>)) as IEnumerable<EndpointDataSource>;
                if (dataSources != null && dataSources.Any())
                {
                    _logger.LogInformation("直接获取到 {Count} 个端点数据源", dataSources.Count());
                    return dataSources;
                }

                // 尝试获取单个EndpointDataSource
                var singleDataSource = serviceProvider.GetService(typeof(EndpointDataSource)) as EndpointDataSource;
                if (singleDataSource != null)
                {
                    _logger.LogInformation("获取到单个端点数据源");
                    return new[] { singleDataSource };
                }

                // 尝试从RouteEndpointDataSource获取
                var routeEndpointDataSourceType = Type.GetType("Microsoft.AspNetCore.Routing.RouteEndpointDataSource, Microsoft.AspNetCore.Routing");
                if (routeEndpointDataSourceType != null)
                {
                    var routeDataSource = serviceProvider.GetService(routeEndpointDataSourceType) as EndpointDataSource;
                    if (routeDataSource != null)
                    {
                        _logger.LogInformation("获取到RouteEndpointDataSource");
                        return new[] { routeDataSource };
                    }
                }

                _logger.LogWarning("无法获取任何端点数据源，返回空集合");
                return Enumerable.Empty<EndpointDataSource>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取端点数据源时出错");
                return Enumerable.Empty<EndpointDataSource>();
            }
        }

        /// <summary>
        /// 从端点对象获取关联的程序集
        /// </summary>
        /// <param name="endpoint">端点对象</param>
        /// <returns>关联的程序集，未找到则返回null</returns>
        private Assembly GetEndpointAssembly(Endpoint endpoint)
        {
            try
            {
                // 尝试获取Metadata集合
                var metadataProperty = endpoint.GetType().GetProperty("Metadata");
                if (metadataProperty != null)
                {
                    var metadata = metadataProperty.GetValue(endpoint) as IEnumerable;
                    if (metadata != null)
                    {
                        foreach (var item in metadata)
                        {
                            // 检查是否是ControllerActionDescriptor
                            var itemType = item.GetType();
                            if (itemType.Name == "ControllerActionDescriptor" || 
                                itemType.FullName?.Contains("ControllerActionDescriptor") == true)
                            {
                                // 尝试获取ControllerTypeInfo属性
                                var controllerTypeInfoProperty = itemType.GetProperty("ControllerTypeInfo");
                                if (controllerTypeInfoProperty != null)
                                {
                                    var controllerTypeInfo = controllerTypeInfoProperty.GetValue(item) as TypeInfo;
                                    if (controllerTypeInfo != null)
                                    {
                                        return controllerTypeInfo.Assembly;
                                    }
                                }
                                
                                // 尝试获取ControllerType属性
                                var controllerTypeProperty = itemType.GetProperty("ControllerType");
                                if (controllerTypeProperty != null)
                                {
                                    var controllerType = controllerTypeProperty.GetValue(item) as Type;
                                    if (controllerType != null)
                                    {
                                        return controllerType.Assembly;
                                    }
                                }
                            }
                        }
                    }
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取端点关联程序集时出错");
                return null;
            }
        }
        
        /// <summary>
        /// 获取端点的展示名称
        /// </summary>
        private string GetEndpointDisplayName(Endpoint endpoint)
        {
            try
            {
                var displayNameProperty = endpoint.GetType().GetProperty("DisplayName");
                if (displayNameProperty != null)
                {
                    return displayNameProperty.GetValue(endpoint) as string;
                }
                return endpoint.ToString();
            }
            catch
            {
                return endpoint.ToString();
            }
        }

        /// <summary>
        /// 刷新路由端点
        /// </summary>
        /// <param name="services">服务提供程序</param>
        /// <param name="loadedAssemblies">已加载的模块程序集集合（可选）</param>
        public void RefreshEndpoints(IServiceProvider services, IEnumerable<Assembly> loadedAssemblies = null)
        {
            try
            {
                _logger.LogInformation("开始刷新路由端点...");
                
                // 保存所有发现的端点
                var endpoints = new List<Endpoint>();

                // 方式1：从MVC系统获取控制器（如果可用）
                if (services.GetService(typeof(IActionDescriptorCollectionProvider)) is IActionDescriptorCollectionProvider actionProvider)
                {
                    _logger.LogInformation("从MVC系统获取控制器动作");
                    var mvcEndpoints = GetEndpointsFromMvc(actionProvider);
                    endpoints.AddRange(mvcEndpoints);
                    _logger.LogInformation("从MVC系统获取到 {Count} 个端点", mvcEndpoints.Count);
                }
                else
                {
                    _logger.LogWarning("未找到IActionDescriptorCollectionProvider服务，无法从MVC系统获取控制器");
                }
                
                // 方式2：直接从程序集扫描控制器（如果提供了程序集列表）
                if (loadedAssemblies != null && loadedAssemblies.Any())
                {
                    _logger.LogInformation("从 {Count} 个程序集直接扫描控制器", loadedAssemblies.Count());
                    
                    var assemblyEndpoints = GetEndpointsFromAssemblies(loadedAssemblies);
                    if (assemblyEndpoints.Any())
                    {
                        endpoints.AddRange(assemblyEndpoints);
                        _logger.LogInformation("从程序集扫描添加了 {Count} 个端点", assemblyEndpoints.Count);
                    }
                }

                // 更新端点数据源
                _dynamicEndpointDataSource.Clear();
                _dynamicEndpointDataSource.AddRange(endpoints);
                _logger.LogInformation("已刷新路由端点，共 {Count} 个端点", endpoints.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新路由端点时出错");
            }
        }
        
        private List<Endpoint> GetEndpointsFromMvc(IActionDescriptorCollectionProvider actionProvider)
        {
            var endpoints = new List<Endpoint>();
            
            // 获取所有控制器操作
            foreach (var action in actionProvider.ActionDescriptors.Items.OfType<ControllerActionDescriptor>())
            {
                try
                {
                    // 跳过已卸载模块的控制器
                    var assemblyName = action.ControllerTypeInfo.Assembly.GetName().Name;
                    if (_unloadedModuleNames.Contains(assemblyName))
                    {
                        _logger.LogInformation("跳过已卸载模块的控制器: {Controller}.{Action}", 
                            action.ControllerName, action.ActionName);
                        continue;
                    }
                    
                    // 创建路由模式
                    var template = action.AttributeRouteInfo?.Template;
                    if (string.IsNullOrEmpty(template))
                    {
                        // 如果没有路由模板，使用控制器名和操作名创建默认路由
                        template = $"api/{action.ControllerName}/{action.ActionName}";
                    }
                    
                    var routePattern = RoutePatternFactory.Parse(template);
                    
                    // 创建元数据集合
                    var metadata = new EndpointMetadataCollection(
                        new[] { action } // 确保包含ControllerActionDescriptor
                    );
                    
                    // 创建请求处理委托
                    RequestDelegate handler = context => 
                    {
                        context.SetEndpoint(new Endpoint(c => Task.CompletedTask, metadata, $"MVC: {action.DisplayName}"));
                        return Task.CompletedTask;
                    };
                    
                    // 创建端点
                    var endpoint = new RouteEndpoint(
                        handler,
                        routePattern,
                        0,
                        metadata,
                        action.DisplayName
                    );
                    
                    endpoints.Add(endpoint);
                    _logger.LogDebug("添加MVC端点: {DisplayName} → {Template}", 
                        action.DisplayName, action.AttributeRouteInfo?.Template);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "创建MVC端点时出错: {Controller}.{Action}", 
                        action.ControllerTypeInfo.Name, action.ActionName);
                }
            }
            
            return endpoints;
        }
        
        private List<Endpoint> GetEndpointsFromAssemblies(IEnumerable<Assembly> assemblies)
        {
            var endpoints = new List<Endpoint>();
            
            foreach (var assembly in assemblies)
            {
                try
                {
                    // 获取程序集名称
                    var assemblyName = assembly.GetName().Name;
                    
                    // 跳过已卸载的模块
                    if (_unloadedModuleNames.Contains(assemblyName))
                    {
                        _logger.LogInformation("跳过已卸载的模块: {AssemblyName}", assemblyName);
                        continue;
                    }
                    
                    // 扫描控制器类型
                    var controllerTypes = assembly.GetExportedTypes()
                        .Where(t => !t.IsAbstract && 
                                   (t.Name.EndsWith("Controller") || 
                                    t.IsSubclassOf(typeof(ControllerBase)) || 
                                    t.GetInterfaces().Any(i => i.Name == "IController")));
                    
                    foreach (var controllerType in controllerTypes)
                    {
                        // 记录发现的控制器
                        _logger.LogInformation("从程序集直接发现控制器: {Assembly}.{Controller}", 
                            assemblyName, controllerType.Name);
                        
                        // 自动创建控制器的端点
                        if (assemblyName.StartsWith("SSIC.Modules."))
                        {
                            try
                            {
                                // 提取模块名和控制器名
                                var moduleName = assemblyName.Split('.')[2].ToLower();
                                var controllerName = controllerType.Name;
                                if (controllerName.EndsWith("Controller", StringComparison.OrdinalIgnoreCase))
                                {
                                    controllerName = controllerName.Substring(0, controllerName.Length - 10);
                                }
                                
                                // 获取控制器中的所有公共方法（动作方法）
                                var actionMethods = controllerType.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly)
                                    .Where(m => !m.IsSpecialName && 
                                               m.DeclaringType != typeof(object) && 
                                               !IsBaseControllerMethod(m) &&
                                               IsActionMethod(m));
                                
                                if (!actionMethods.Any())
                                {
                                    // 如果没有找到动作方法，至少创建一个默认路由
                                    var defaultRoute = $"api/{moduleName}/{controllerName}";
                                    CreateAndAddEndpoint(endpoints, defaultRoute, controllerType.FullName);
                                }
                                else
                                {
                                    // 为每个动作方法创建端点
                                    foreach (var actionMethod in actionMethods)
                                    {
                                        try
                                        {
                                            var actionName = actionMethod.Name;
                                            
                                            // 检查是否有Route特性
                                            string routeTemplate = null;
                                            
                                            // 检查HttpGet/HttpPost等特性
                                            var httpMethodAttributes = actionMethod.GetCustomAttributes()
                                                .Where(a => a.GetType().Name.StartsWith("Http") && 
                                                          a.GetType().Name.EndsWith("Attribute"))
                                                .ToList();
                                                
                                            if (httpMethodAttributes.Any())
                                            {
                                                foreach (var attr in httpMethodAttributes)
                                                {
                                                    // 尝试获取Template属性
                                                    var templateProperty = attr.GetType().GetProperty("Template");
                                                    if (templateProperty != null)
                                                    {
                                                        routeTemplate = templateProperty.GetValue(attr) as string;
                                                        if (!string.IsNullOrEmpty(routeTemplate))
                                                        {
                                                            break;
                                                        }
                                                    }
                                                }
                                            }
                                            
                                            // 如果没有找到路由模板，使用与ModuleRoutePrefixConvention一致的规则
                                            if (string.IsNullOrEmpty(routeTemplate))
                                            {
                                                // 使用控制器名称（小写）作为路由，与ModuleRoutePrefixConvention保持一致
                                                var controllerNameLower = controllerName.ToLowerInvariant();
                                                routeTemplate = $"api/{moduleName}/{controllerNameLower}";
                                            }
                                            else if (!routeTemplate.StartsWith("api/"))
                                            {
                                                // 如果有自定义模板但不是完整路径，需要添加模块前缀
                                                var controllerNameLower = controllerName.ToLowerInvariant();
                                                if (routeTemplate.StartsWith("{") && routeTemplate.EndsWith("}"))
                                                {
                                                    // 参数路由，如 {id}
                                                    routeTemplate = $"api/{moduleName}/{controllerNameLower}/{routeTemplate}";
                                                }
                                                else if (routeTemplate.Contains("/"))
                                                {
                                                    // 复合路由，如 {id}/permissions
                                                    routeTemplate = $"api/{moduleName}/{controllerNameLower}/{routeTemplate}";
                                                }
                                                else
                                                {
                                                    // 其他自定义路由
                                                    routeTemplate = $"api/{moduleName}/{controllerNameLower}/{routeTemplate}";
                                                }
                                            }
                                            _logger.LogWarning($"路由地址{routeTemplate}");
                                            // 创建端点
                                            CreateAndAddEndpoint(endpoints, routeTemplate, $"{controllerType.FullName}.{actionName}");
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.LogWarning(ex, "为动作方法创建端点时出错: {Controller}.{Action}", 
                                                controllerType.Name, actionMethod.Name);
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "处理模块控制器时出错: {Controller}", controllerType.Name);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "从程序集扫描控制器时出错: {Assembly}", assembly.GetName().Name);
                }
            }
            
            return endpoints;
        }
        
        private void CreateAndAddEndpoint(List<Endpoint> endpoints, string routeTemplate, string displayName)
        {
            try
            {
                var routePattern = RoutePatternFactory.Parse(routeTemplate);
                var metadata = new EndpointMetadataCollection();
                
                // 请求处理委托
                RequestDelegate handler = context => 
                {
                    context.Response.StatusCode = 200;
                    return Task.CompletedTask;
                };
                
                var endpoint = new RouteEndpoint(
                    handler,
                    routePattern,
                    0,
                    metadata,
                    displayName
                );
                
                endpoints.Add(endpoint);
                _logger.LogDebug("添加动态端点: {DisplayName} → {Template}", displayName, routeTemplate);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "创建端点时出错: {Template}", routeTemplate);
            }
        }
        
        private bool IsBaseControllerMethod(MethodInfo method)
        {
            // 获取声明该方法的类型
            var declaringType = method.DeclaringType;
            
            // 检查方法是否来自Controller或ControllerBase
            return declaringType != null && 
                   (declaringType == typeof(Controller) || 
                    declaringType == typeof(ControllerBase) ||
                    (declaringType.IsAssignableFrom(typeof(Controller)) && !declaringType.IsSubclassOf(typeof(Controller))) ||
                    (declaringType.IsAssignableFrom(typeof(ControllerBase)) && !declaringType.IsSubclassOf(typeof(ControllerBase))));
        }

        private bool IsActionMethod(MethodInfo method)
        {
            // 如果方法有HTTP动词特性，则是动作方法
            var hasHttpMethodAttribute = method.GetCustomAttributes()
                .Any(attr => attr.GetType().Name.StartsWith("Http") && attr.GetType().Name.EndsWith("Attribute"));
                
            if (hasHttpMethodAttribute)
                return true;
                
            // 检查常见的动作方法名称
            var commonActionNames = new[] { 
                "Get", "Post", "Put", "Delete", "Patch", 
                "Head", "Options", "Index", "Create", "Edit", 
                "Update", "Remove", "Query", "Find" 
            };
            
            foreach (var prefix in commonActionNames)
            {
                if (method.Name.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                    return true;
            }
            
            return false;
        }
    }
} 