@SSIC.Modules.Auth_HostAddress = http://localhost:5129
@token = 

### 用户登录 - 成功案例
POST {{SSIC.Modules.Auth_HostAddress}}/api/auth/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456",
  "rememberMe": true
}

### 用户登录 - 失败案例（错误密码）
POST {{SSIC.Modules.Auth_HostAddress}}/api/auth/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "wrongpassword",
  "rememberMe": false
}

### 用户登录 - 失败案例（空用户名）
POST {{SSIC.Modules.Auth_HostAddress}}/api/auth/auth/login
Content-Type: application/json

{
  "username": "",
  "password": "123456",
  "rememberMe": false
}

### 用户注册 - 成功案例
POST {{SSIC.Modules.Auth_HostAddress}}/api/auth/auth/register
Content-Type: application/json

{
  "username": "newuser",
  "password": "newpassword123",
  "confirmPassword": "newpassword123",
  "email": "<EMAIL>"
}

### 用户注册 - 失败案例（密码不匹配）
POST {{SSIC.Modules.Auth_HostAddress}}/api/auth/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123",
  "confirmPassword": "password456",
  "email": "<EMAIL>"
}

### 获取用户信息（需要认证）
GET {{SSIC.Modules.Auth_HostAddress}}/api/auth/auth/profile
Authorization: Bearer {{token}}
Accept: application/json

### 获取用户信息（无认证 - 应该失败）
GET {{SSIC.Modules.Auth_HostAddress}}/api/auth/auth/profile
Accept: application/json

### 用户登出
POST {{SSIC.Modules.Auth_HostAddress}}/api/auth/auth/logout
Authorization: Bearer {{token}}
Content-Type: application/json

### 修改密码 - 成功案例
POST {{SSIC.Modules.Auth_HostAddress}}/api/auth/auth/change-password
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "oldPassword": "123456",
  "newPassword": "newpassword123",
  "confirmNewPassword": "newpassword123"
}

### 修改密码 - 失败案例（新密码不匹配）
POST {{SSIC.Modules.Auth_HostAddress}}/api/auth/auth/change-password
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "oldPassword": "123456",
  "newPassword": "newpassword123",
  "confirmNewPassword": "differentpassword"
}

### 修改密码 - 失败案例（空密码）
POST {{SSIC.Modules.Auth_HostAddress}}/api/auth/auth/change-password
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "oldPassword": "",
  "newPassword": "newpassword123",
  "confirmNewPassword": "newpassword123"
}

### ========== 角色管理接口测试 ==========

### 获取所有角色
GET {{SSIC.Modules.Auth_HostAddress}}/api/auth/role
Authorization: Bearer {{token}}
Accept: application/json

### 根据ID获取角色
GET {{SSIC.Modules.Auth_HostAddress}}/api/auth/role/1
Authorization: Bearer {{token}}
Accept: application/json

### 获取不存在的角色
GET {{SSIC.Modules.Auth_HostAddress}}/api/auth/role/999
Authorization: Bearer {{token}}
Accept: application/json

### 创建新角色 - 成功案例
POST {{SSIC.Modules.Auth_HostAddress}}/api/auth/role
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Editor",
  "description": "内容编辑员"
}

### 创建新角色 - 失败案例（重复名称）
POST {{SSIC.Modules.Auth_HostAddress}}/api/auth/role
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Admin",
  "description": "重复的管理员角色"
}

### 创建新角色 - 失败案例（空名称）
POST {{SSIC.Modules.Auth_HostAddress}}/api/auth/role
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "",
  "description": "空名称角色"
}

### 更新角色 - 成功案例
PUT {{SSIC.Modules.Auth_HostAddress}}/api/auth/role/2
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "StandardUser",
  "description": "标准用户（已更新）"
}

### 更新角色 - 失败案例（不存在的角色）
PUT {{SSIC.Modules.Auth_HostAddress}}/api/auth/role/999
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "NonExistentRole",
  "description": "不存在的角色"
}

### 获取角色权限
GET {{SSIC.Modules.Auth_HostAddress}}/api/auth/role/1/permissions
Authorization: Bearer {{token}}
Accept: application/json

### 删除角色 - 成功案例
DELETE {{SSIC.Modules.Auth_HostAddress}}/api/auth/role/3
Authorization: Bearer {{token}}

### 删除角色 - 失败案例（不存在的角色）
DELETE {{SSIC.Modules.Auth_HostAddress}}/api/auth/role/999
Authorization: Bearer {{token}}

###
# 测试说明：
# 1. 所有接口都会自动路由到 /api/auth/ 前缀下
# 2. 需要认证的接口需要在Authorization头中提供Bearer token
# 3. 可以先调用登录接口获取token，然后在其他需要认证的接口中使用
# 4. 测试用户名: admin, 密码: 123456
# 5. 角色管理接口都需要认证，请先登录获取token
###
