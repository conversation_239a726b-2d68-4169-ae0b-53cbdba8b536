<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SSIC.Modules.Sys</name>
    </assembly>
    <members>
        <member name="T:SSIC.Modules.Sys.Controllers.SystemConfigController">
            <summary>
            系统配置控制器
            </summary>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.SystemConfigController.GetConfigs(System.String)">
            <summary>
            获取所有系统配置
            </summary>
            <param name="category">配置分类（可选）</param>
            <returns>配置列表</returns>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.SystemConfigController.GetConfig(System.String)">
            <summary>
            根据键获取配置
            </summary>
            <param name="key">配置键</param>
            <returns>配置信息</returns>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.SystemConfigController.UpdateConfig(System.String,SSIC.Modules.Sys.Controllers.UpdateConfigRequest)">
            <summary>
            更新配置
            </summary>
            <param name="key">配置键</param>
            <param name="request">更新请求</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.SystemConfigController.CreateConfig(SSIC.Modules.Sys.Controllers.CreateConfigRequest)">
            <summary>
            创建新配置
            </summary>
            <param name="request">创建请求</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.SystemConfigController.DeleteConfig(System.String)">
            <summary>
            删除配置
            </summary>
            <param name="key">配置键</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.SystemConfigController.GetCategories">
            <summary>
            获取配置分类
            </summary>
            <returns>分类列表</returns>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.SystemConfigController.BatchUpdateConfigs(System.Collections.Generic.List{SSIC.Modules.Sys.Controllers.BatchUpdateConfigRequest})">
            <summary>
            批量更新配置
            </summary>
            <param name="requests">批量更新请求</param>
            <returns>更新结果</returns>
        </member>
        <member name="T:SSIC.Modules.Sys.Controllers.SystemConfig">
            <summary>
            系统配置模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.SystemConfig.Key">
            <summary>
            配置键
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.SystemConfig.Value">
            <summary>
            配置值
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.SystemConfig.Description">
            <summary>
            配置描述
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.SystemConfig.Category">
            <summary>
            配置分类
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.SystemConfig.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.SystemConfig.UpdatedAt">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:SSIC.Modules.Sys.Controllers.CreateConfigRequest">
            <summary>
            创建配置请求模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.CreateConfigRequest.Key">
            <summary>
            配置键
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.CreateConfigRequest.Value">
            <summary>
            配置值
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.CreateConfigRequest.Description">
            <summary>
            配置描述
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.CreateConfigRequest.Category">
            <summary>
            配置分类
            </summary>
        </member>
        <member name="T:SSIC.Modules.Sys.Controllers.UpdateConfigRequest">
            <summary>
            更新配置请求模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.UpdateConfigRequest.Value">
            <summary>
            配置值
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.UpdateConfigRequest.Description">
            <summary>
            配置描述
            </summary>
        </member>
        <member name="T:SSIC.Modules.Sys.Controllers.BatchUpdateConfigRequest">
            <summary>
            批量更新配置请求模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.BatchUpdateConfigRequest.Key">
            <summary>
            配置键
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.BatchUpdateConfigRequest.Value">
            <summary>
            配置值
            </summary>
        </member>
        <member name="T:SSIC.Modules.Sys.Controllers.SystemLogController">
            <summary>
            系统日志控制器
            </summary>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.SystemLogController.GetLogs(System.Int32,System.Int32,System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取系统日志（分页）
            </summary>
            <param name="page">页码</param>
            <param name="pageSize">页大小</param>
            <param name="level">日志级别</param>
            <param name="source">日志来源</param>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>日志列表</returns>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.SystemLogController.GetLog(System.Int32)">
            <summary>
            根据ID获取日志详情
            </summary>
            <param name="id">日志ID</param>
            <returns>日志详情</returns>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.SystemLogController.CreateLog(SSIC.Modules.Sys.Controllers.CreateLogRequest)">
            <summary>
            创建日志记录
            </summary>
            <param name="request">创建日志请求</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.SystemLogController.GetStatistics">
            <summary>
            获取日志统计信息
            </summary>
            <returns>统计信息</returns>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.SystemLogController.CleanupLogs(System.DateTime)">
            <summary>
            清理日志
            </summary>
            <param name="beforeDate">清理此日期之前的日志</param>
            <returns>清理结果</returns>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.SystemLogController.ExportLogs(System.String,System.String,System.String)">
            <summary>
            导出日志
            </summary>
            <param name="format">导出格式（json/csv）</param>
            <param name="level">日志级别</param>
            <param name="source">日志来源</param>
            <returns>导出文件</returns>
        </member>
        <member name="T:SSIC.Modules.Sys.Controllers.SystemLog">
            <summary>
            系统日志模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.SystemLog.Id">
            <summary>
            日志ID
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.SystemLog.Level">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.SystemLog.Message">
            <summary>
            日志消息
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.SystemLog.Source">
            <summary>
            日志来源
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.SystemLog.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.SystemLog.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.SystemLog.Details">
            <summary>
            详细信息
            </summary>
        </member>
        <member name="T:SSIC.Modules.Sys.Controllers.CreateLogRequest">
            <summary>
            创建日志请求模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.CreateLogRequest.Level">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.CreateLogRequest.Message">
            <summary>
            日志消息
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.CreateLogRequest.Source">
            <summary>
            日志来源
            </summary>
        </member>
        <member name="P:SSIC.Modules.Sys.Controllers.CreateLogRequest.Details">
            <summary>
            详细信息
            </summary>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.WeatherForecastController.GetForecast(System.Int32)">
            <summary>
            获取指定天数的天气预报
            </summary>
            <param name="days">天数</param>
            <returns>天气预报列表</returns>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.WeatherForecastController.GetWeatherByCity(System.String)">
            <summary>
            获取指定城市的天气
            </summary>
            <param name="city">城市名称</param>
            <returns>天气信息</returns>
        </member>
        <member name="M:SSIC.Modules.Sys.Controllers.WeatherForecastController.GetStatistics">
            <summary>
            获取天气统计信息
            </summary>
            <returns>统计信息</returns>
        </member>
    </members>
</doc>
