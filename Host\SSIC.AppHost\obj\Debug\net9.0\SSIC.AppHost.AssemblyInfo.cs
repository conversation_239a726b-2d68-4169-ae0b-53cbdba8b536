//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute("31079f85-6506-48ff-8d22-65c6a98af2d3")]
[assembly: System.Reflection.AssemblyMetadata("dcpclipath", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.1.0\\tools\\dcp" +
    ".exe")]
[assembly: System.Reflection.AssemblyMetadata("dcpextensionpaths", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.1.0\\tools\\ext" +
    "\\")]
[assembly: System.Reflection.AssemblyMetadata("dcpbinpath", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.hosting.orchestration.win-x64\\9.1.0\\tools\\ext" +
    "\\bin\\")]
[assembly: System.Reflection.AssemblyMetadata("apphostprojectpath", "D:\\Saisi\\SSIC\\Host\\SSIC.AppHost")]
[assembly: System.Reflection.AssemblyMetadata("apphostprojectname", "SSIC.AppHost.csproj")]
[assembly: System.Reflection.AssemblyMetadata("aspiredashboardpath", "C:\\Users\\<USER>\\.nuget\\packages\\aspire.dashboard.sdk.win-x64\\9.1.0\\tools\\Aspire.Dash" +
    "board.exe")]
[assembly: System.Reflection.AssemblyMetadataAttribute("apphostprojectbaseintermediateoutputpath", "D:\\Saisi\\SSIC\\Host\\SSIC.AppHost\\obj\\")]
[assembly: System.Reflection.AssemblyCompanyAttribute("SSIC.AppHost")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0+4b98eef429519a91cb1c2f787b37b1f29df67a81")]
[assembly: System.Reflection.AssemblyProductAttribute("SSIC.AppHost")]
[assembly: System.Reflection.AssemblyTitleAttribute("SSIC.AppHost")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]

// 由 MSBuild WriteCodeFragment 类生成。

