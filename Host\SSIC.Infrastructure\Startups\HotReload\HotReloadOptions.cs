﻿using System;
using System.Collections.Generic;

namespace SSIC.Infrastructure.Startups.HotReload
{
    /// <summary>
    /// 热插拔配置选项
    /// </summary>
    public class HotReloadOptions
    {
        /// <summary>
        /// 是否启用热插拔功能
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 监控的DLL文件扩展名
        /// </summary>
        public string[] WatchExtensions { get; set; } = new[] { ".dll" };

        /// <summary>
        /// 监控间隔（毫秒）
        /// </summary>
        public int WatchIntervalMilliseconds { get; set; } = 1000;

        /// <summary>
        /// 监控的目录路径列表
        /// </summary>
        public List<string> WatchPaths { get; set; } = new List<string> { "bin" };

        /// <summary>
        /// 是否递归扫描子目录
        /// </summary>
        public bool RecursiveScan { get; set; } = true;

        /// <summary>
        /// 最大扫描深度（0表示无限制）
        /// </summary>
        public int MaxScanDepth { get; set; } = 0;

        /// <summary>
        /// 自动创建监控目录
        /// </summary>
        public bool AutoCreateDirectories { get; set; } = true;

        /// <summary>
        /// 模块扫描模式
        /// </summary>
        public ModuleScanMode ScanMode { get; set; } = ModuleScanMode.ByPrefix;

        /// <summary>
        /// 模块名称前缀
        /// </summary>
        public string ModuleNamePrefix { get; set; } = "SSIC.Modules.";

        /// <summary>
        /// 模块搜索模式（用于文件匹配）
        /// </summary>
        public string ModuleSearchPattern { get; set; } = "*.dll";

        /// <summary>
        /// API路由前缀
        /// </summary>
        public string ApiRoutePrefix { get; set; } = "api";

        /// <summary>
        /// 重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 重试间隔（毫秒）
        /// </summary>
        public int RetryDelayMilliseconds { get; set; } = 1000;
        
        /// <summary>
        /// 通配符模式列表（如"SSIC.MODULES.*.DLL"）
        /// </summary>
        public List<string> WildcardPatterns { get; set; } = new List<string> { "SSIC.MODULES.*.DLL" };
    }

    /// <summary>
    /// 模块扫描模式
    /// </summary>
    public enum ModuleScanMode
    {
        /// <summary>
        /// 按前缀匹配
        /// </summary>
        ByPrefix,
        
        /// <summary>
        /// 按目录结构匹配
        /// </summary>
        ByDirectory,
        
        /// <summary>
        /// 按文件匹配模式
        /// </summary>
        ByPattern,
        
        /// <summary>
        /// 所有DLL文件
        /// </summary>
        AllDlls,
        
        /// <summary>
        /// 通配符模式匹配
        /// </summary>
        ByWildcardPattern
    }
} 