﻿using System;

namespace SSIC.Infrastructure.Entity.EntityBase
{
    public class EntityApprove : IEntityApprove
    {
        /// <summary>
        /// 审核(0.提交,1审核中,2.反审核,3.已审核,4.结案，5手动结案)
        /// </summary>
        public int? check { get; set; } = 0;

        /// <summary>
        /// 审核人
        /// </summary>
        public Guid? checkid { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? checktime { get; set; }
    }
}
