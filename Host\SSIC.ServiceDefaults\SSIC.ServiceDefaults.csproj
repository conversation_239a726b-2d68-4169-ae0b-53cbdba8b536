<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireSharedProject>true</IsAspireSharedProject>
    <PackageOutputPath>../SSIC.Packages</PackageOutputPath>
  </PropertyGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />

    <PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="9.4.0" />
    <PackageReference Include="Microsoft.Extensions.ServiceDiscovery" Version="9.1.0" />
    <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.11.2" />
    <PackageReference Include="OpenTelemetry.Exporter.Zipkin" Version="1.11.2" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.11.2" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.11.1" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.11.1" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.11.1" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.1-dev-02307" />
  </ItemGroup>

</Project>
