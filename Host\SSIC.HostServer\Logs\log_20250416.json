{"@t":"2025-04-16T01:32:44.4230628Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:32:44.4688558Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:32:44.5475091Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:32:44.5533221Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:32:44.5554799Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:37:58.4984677Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:37:58.5522099Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:37:58.6272214Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:37:58.6303211Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:37:58.6325108Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:43:34.5927575Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:43:34.6583464Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:43:34.7597947Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:43:34.7632392Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:43:34.7659539Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:51:28.2965966Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:51:28.3473118Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:51:28.4961133Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:51:28.4996001Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:51:28.5021907Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:54:21.7119405Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:54:21.7652975Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:54:21.8613208Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:54:21.8651859Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:54:21.8703086Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:56:48.2254820Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:56:48.2680128Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:56:48.3505404Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:56:48.3537753Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T01:56:48.3567376Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:04:07.2952487Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:04:07.3712073Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:04:07.4639608Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:04:07.4678690Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:04:07.4707633Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:09:29.3067225Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:09:29.3726291Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:09:29.4738593Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:09:29.4774566Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:09:29.4798497Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:27:30.5822292Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:27:30.6327110Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:27:30.7335635Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:27:30.7387708Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:27:30.7435777Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:51:52.4542531Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:51:52.5108853Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:51:52.5995565Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:51:52.6027095Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T02:51:52.6054818Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T05:50:53.6674005Z","@mt":"初始化动态端点管理器","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T05:50:53.7384182Z","@mt":"无法添加动态端点数据源：未找到DataSources属性","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T05:50:53.8075281Z","@mt":"初始化动态端点管理器","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T05:50:53.8102812Z","@mt":"无法添加动态端点数据源：未找到DataSources属性","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T05:50:53.8403377Z","@mt":"开始构建所有模块的端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T05:50:54.5574274Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T05:50:54.5640409Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T05:50:54.7814300Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T05:50:54.7873727Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T05:50:54.7907286Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:07.8768449Z","@mt":"已使用影子复制加载程序集: D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll -> D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\shadow_copies\\SSIC.Modules.Auth_3bd97313.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ShadowCopyManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:07.9539182Z","@mt":"已使用影子复制加载程序集: D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll -> D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\shadow_copies\\SSIC.Modules.Sys_d2027f97.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ShadowCopyManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:07.9716041Z","@mt":"初始化动态端点管理器","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:07.9745106Z","@mt":"无法添加动态端点数据源：未找到DataSources属性","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:08.0253877Z","@mt":"初始化动态端点管理器","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:08.0287977Z","@mt":"无法添加动态端点数据源：未找到DataSources属性","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:08.0386796Z","@mt":"开始构建所有模块的端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:08.6977005Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:08.7044849Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:08.8206090Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:08.8238790Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:08.8268282Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:23.3058969Z","@mt":"移除模块 'sys' 的路由模式: api/sys/[controller], api/sys/api/Hello","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:23.3115269Z","@mt":"从动态端点数据源中移除模块 'sys' 的端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:26.3363929Z","@mt":"开始刷新动态端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:26.3399771Z","@mt":"开始构建所有模块的端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:26.3434225Z","@mt":"成功刷新动态端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:45.7874358Z","@mt":"开始刷新动态端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:45.7905771Z","@mt":"开始构建所有模块的端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:01:45.7937626Z","@mt":"成功刷新动态端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:29.9879546Z","@mt":"已使用影子复制加载程序集: D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll -> D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\shadow_copies\\SSIC.Modules.Auth_3bd97313.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ShadowCopyManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:30.0664720Z","@mt":"已使用影子复制加载程序集: D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll -> D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\shadow_copies\\SSIC.Modules.Sys_d2027f97.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.ShadowCopyManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:30.0817203Z","@mt":"初始化动态端点管理器","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:30.0904319Z","@mt":"无法添加动态端点数据源：未找到DataSources属性","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:30.1573236Z","@mt":"初始化动态端点管理器","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:30.1598885Z","@mt":"无法添加动态端点数据源：未找到DataSources属性","@l":"Warning","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:30.1734906Z","@mt":"开始构建所有模块的端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:31.2456190Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:31.2548061Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:31.4011179Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:31.4049499Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:31.4080697Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:52.8667680Z","@mt":"从动态端点数据源中移除模块 'sys' 的端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:57.6917140Z","@mt":"开始刷新动态端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:57.6965386Z","@mt":"开始构建所有模块的端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:29:57.7022450Z","@mt":"成功刷新动态端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:30:18.9954149Z","@mt":"开始刷新动态端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:30:18.9990695Z","@mt":"开始构建所有模块的端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T06:30:19.0028123Z","@mt":"成功刷新动态端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.DynamicEndpointManager","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:41:23.6750224Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:41:23.7603671Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:41:23.9141047Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:41:23.9177480Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:41:23.9205809Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:42:57.8647627Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:42:57.9515083Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:42:58.1496415Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:42:58.1530554Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:42:58.1562412Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:45:25.1988992Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:45:25.2564139Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:45:25.3783319Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:45:25.3814937Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T07:45:25.3847961Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:00.7368758Z","@mt":"发现 {Count} 个模块文件","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:00.8515686Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:00.8676569Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:00.8727030Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:00.8776031Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:00.8897287Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:01.4736375Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:01.5029322Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:01.6582428Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:01.6616991Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:01.6647675Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:16.1049165Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:16.1117531Z","@mt":"插件未加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:22.2182644Z","@mt":"检测到文件变更: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:22.2228932Z","@mt":"插件未加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:30.4780649Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:02:30.4942744Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:13.9106369Z","@mt":"发现 {Count} 个模块文件","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:14.0438434Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:14.0614082Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:14.0720425Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:14.0751462Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:14.0787774Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:14.7870159Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:14.7937547Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:15.0299596Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:15.0335579Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:15.0371487Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:56.3939498Z","@mt":"发现 {Count} 个模块文件","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:56.5014950Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:56.5147608Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:56.5248613Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:56.5280691Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:56.5316552Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:57.1401197Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:57.1447367Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:57.2758762Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:57.2795367Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:03:57.2821047Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:24.9533155Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.0975935Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.1293162Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.1513704Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.1968362Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.2043653Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.2148278Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.2184311Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.2215140Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.2245846Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.6796316Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.6992235Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.9106656Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.9140023Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:16:25.9172308Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:09.6049448Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:09.8097572Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:09.8138278Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:09.9429159Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:09.9483023Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:09.9583137Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:09.9738804Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:09.9779096Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:09.9817375Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:09.9852732Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:10.5084558Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:10.5130543Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:10.6334227Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:10.6366677Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:10.6393064Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:28.2398923Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:28.2463990Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:28.4118712Z","@mt":"删除临时文件失败: {Path}","@l":"Warning","@x":"System.UnauthorizedAccessException: Access to the path 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_7908cf89c8304b56bfd2d54210a620f6\\4c2bf895897a4515840cce1047af74aa.dll' is denied.\r\n   at System.IO.FileSystem.DeleteFile(String fullPath)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.UnloadPluginAsync(String pluginPath) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 263","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_7908cf89c8304b56bfd2d54210a620f6\\4c2bf895897a4515840cce1047af74aa.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:28.4218327Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:54.1093193Z","@mt":"检测到文件变更: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:54.1138458Z","@mt":"检测到文件变更: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:54.1181047Z","@mt":"插件未加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:54.1490267Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:21:54.1516582Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:22:30.2600382Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:22:30.2632637Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:22:30.4135987Z","@mt":"删除临时文件失败: {Path}","@l":"Warning","@x":"System.UnauthorizedAccessException: Access to the path 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_7908cf89c8304b56bfd2d54210a620f6\\654b0e17d13845da9e07c37cbea7b4ba.dll' is denied.\r\n   at System.IO.FileSystem.DeleteFile(String fullPath)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.UnloadPluginAsync(String pluginPath) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 263","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_7908cf89c8304b56bfd2d54210a620f6\\654b0e17d13845da9e07c37cbea7b4ba.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:22:30.4194459Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:41.7238092Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:41.8803484Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:41.8941755Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:41.9298639Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:41.9329779Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:41.9409542Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:41.9510178Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:41.9545297Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:41.9576635Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:41.9614056Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:42.3946124Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:42.3999924Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:42.5658042Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:42.5701767Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:42.5734225Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:53.9966676Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:54.0017346Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:54.1917554Z","@mt":"删除临时文件失败: {Path}","@l":"Warning","@x":"System.UnauthorizedAccessException: Access to the path 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_aa86cbbca87f4c1e9d3669de42f34d78\\6a9757913ce045f1b309e71a33242f76.dll' is denied.\r\n   at System.IO.FileSystem.DeleteFile(String fullPath)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.UnloadPluginAsync(String pluginPath) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 263","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_aa86cbbca87f4c1e9d3669de42f34d78\\6a9757913ce045f1b309e71a33242f76.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:25:54.2019255Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:26.5213705Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:26.6698164Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:26.6735950Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:26.7303577Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:26.7332522Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:26.7401034Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:26.7527041Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:26.7562091Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:26.7593068Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:26.7626867Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:27.2152896Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:27.2219923Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:27.4096961Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:27.4138235Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:27.4186526Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:39.6539319Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:39.6637481Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:39.6856938Z","@mt":"开始清理临时文件，共 {Count} 个文件","Count":6,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:39.7382223Z","@mt":"删除临时文件失败，已重新标记: {Path}","@l":"Warning","@x":"System.UnauthorizedAccessException: Access to the path 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_0d5bc403cf684f2b875c5ba67d20f941\\13c5bf6ad5414d578a79ea31bb4b7f80.dll' is denied.\r\n   at System.IO.FileSystem.DeleteFile(String fullPath)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.CleanupTempFiles(Object state) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 332","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_0d5bc403cf684f2b875c5ba67d20f941\\13c5bf6ad5414d578a79ea31bb4b7f80.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:39.7826827Z","@mt":"删除临时文件失败，已重新标记: {Path}","@l":"Warning","@x":"System.UnauthorizedAccessException: Access to the path 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_0d5bc403cf684f2b875c5ba67d20f941\\13c5bf6ad5414d578a79ea31bb4b7f80.dll' is denied.\r\n   at System.IO.FileSystem.DeleteFile(String fullPath)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.CleanupTempFiles(Object state) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 332","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_0d5bc403cf684f2b875c5ba67d20f941\\13c5bf6ad5414d578a79ea31bb4b7f80.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:33:39.7875638Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:34:23.7292557Z","@mt":"检测到文件变更: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:34:23.7350628Z","@mt":"检测到文件变更: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:34:23.7400631Z","@mt":"插件未加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:34:23.7780218Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:34:23.7834109Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:34:30.1752830Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:34:30.1786680Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:34:30.2015985Z","@mt":"开始清理临时文件，共 {Count} 个文件","Count":8,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:34:30.2523379Z","@mt":"删除临时文件失败，已重新标记: {Path}","@l":"Warning","@x":"System.UnauthorizedAccessException: Access to the path 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_0d5bc403cf684f2b875c5ba67d20f941\\1111da93b5d24e579bd20b1fc168640e.dll' is denied.\r\n   at System.IO.FileSystem.DeleteFile(String fullPath)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.CleanupTempFiles(Object state) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 332","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_0d5bc403cf684f2b875c5ba67d20f941\\1111da93b5d24e579bd20b1fc168640e.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:34:30.2935865Z","@mt":"删除临时文件失败，已重新标记: {Path}","@l":"Warning","@x":"System.UnauthorizedAccessException: Access to the path 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_0d5bc403cf684f2b875c5ba67d20f941\\1111da93b5d24e579bd20b1fc168640e.dll' is denied.\r\n   at System.IO.FileSystem.DeleteFile(String fullPath)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.CleanupTempFiles(Object state) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 332","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_0d5bc403cf684f2b875c5ba67d20f941\\1111da93b5d24e579bd20b1fc168640e.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:34:30.3406915Z","@mt":"删除临时文件失败，已重新标记: {Path}","@l":"Warning","@x":"System.UnauthorizedAccessException: Access to the path 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_0d5bc403cf684f2b875c5ba67d20f941\\13c5bf6ad5414d578a79ea31bb4b7f80.dll' is denied.\r\n   at System.IO.FileSystem.DeleteFile(String fullPath)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.CleanupTempFiles(Object state) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 332","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_0d5bc403cf684f2b875c5ba67d20f941\\13c5bf6ad5414d578a79ea31bb4b7f80.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:34:30.3827806Z","@mt":"删除临时文件失败，已重新标记: {Path}","@l":"Warning","@x":"System.UnauthorizedAccessException: Access to the path 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_0d5bc403cf684f2b875c5ba67d20f941\\13c5bf6ad5414d578a79ea31bb4b7f80.dll' is denied.\r\n   at System.IO.FileSystem.DeleteFile(String fullPath)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.CleanupTempFiles(Object state) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 332","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_0d5bc403cf684f2b875c5ba67d20f941\\13c5bf6ad5414d578a79ea31bb4b7f80.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:34:30.3878143Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:00.4427168Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:00.6025050Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:00.6161981Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:00.6528088Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:00.6567045Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:00.6649547Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:00.6776263Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:00.6813081Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:00.6845970Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:00.6884694Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:01.1122707Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:01.1180499Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:01.3713049Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:01.3765183Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:01.3807834Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:10.2703211Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:10.2754318Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:10.3015763Z","@mt":"开始清理临时文件，共 {Count} 个文件","Count":6,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:10.3757922Z","@mt":"删除临时文件失败，已重新标记: {Path}","@l":"Warning","@x":"System.UnauthorizedAccessException: Access to the path 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_33725bd91dee49ff8a353a435b6278b1\\e0fabb01a35545b78d0ff22545581bef.dll' is denied.\r\n   at System.IO.FileSystem.DeleteFile(String fullPath)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.CleanupTempFiles(Object state) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 332","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_33725bd91dee49ff8a353a435b6278b1\\e0fabb01a35545b78d0ff22545581bef.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:10.4181654Z","@mt":"删除临时文件失败，已重新标记: {Path}","@l":"Warning","@x":"System.UnauthorizedAccessException: Access to the path 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_33725bd91dee49ff8a353a435b6278b1\\e0fabb01a35545b78d0ff22545581bef.dll' is denied.\r\n   at System.IO.FileSystem.DeleteFile(String fullPath)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.CleanupTempFiles(Object state) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 332","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_33725bd91dee49ff8a353a435b6278b1\\e0fabb01a35545b78d0ff22545581bef.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:35:10.4232847Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:46.4984244Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:46.6539302Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:46.6568995Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:46.6790619Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:46.6820022Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:46.6886163Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:46.6985040Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:46.7008584Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:46.7034772Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:46.7063880Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:47.0842556Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:47.0903635Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:47.2266053Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:47.2295281Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:36:47.2322941Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:37:01.1896460Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:37:01.1948597Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:37:01.1993010Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:08.8536909Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:08.9757251Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:08.9797535Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:09.0082259Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:09.0117887Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:09.0282673Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:09.0421942Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:09.0457638Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:09.0481976Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:09.0512059Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:09.5608747Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:09.5674639Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:09.7784440Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:09.7811550Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:09.7842606Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:22.6252547Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:22.6297396Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:40:22.6369030Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:05.7373995Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:05.9057544Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 302","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:05.9391233Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.0105669Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.0629641Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 302","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.0715299Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.1241979Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.1300759Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.1396259Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.1430504Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.1462452Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.1501927Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.3633385Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.3674295Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.5299515Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.5324995Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:06.5357494Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:22.0384915Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:22.0426153Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:51:22.0492028Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:52.5359903Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:52.7537982Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 327","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:52.7969531Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.0438808Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.1025434Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 327","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.1086819Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.1613753Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.1683676Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.1921014Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.1955427Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.1986904Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.2180332Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.5888690Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.5947563Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.6662804Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.6701685Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T08:56:53.6740493Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:15.6993267Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:15.9200187Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 327","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.0069910Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.2541719Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.3319927Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 327","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.3487054Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.3927379Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.3980428Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.4106944Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.4141220Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.4172335Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.4203452Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.8158766Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.8215726Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.9308372Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.9335979Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:19:16.9363049Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:36.4035395Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:36.6481956Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 327","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:36.6875589Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:36.9171040Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:36.9759779Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 327","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:36.9819083Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:37.0884867Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:37.1175610Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:37.1493882Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:37.1531248Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:37.1564740Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:37.1598335Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:37.4367641Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:37.4514866Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:37.5009285Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:37.5049289Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:20:37.5078637Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:02.8458647Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:03.1168727Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 327","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:03.2206106Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:03.4431780Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:03.5120207Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 327","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:03.5189711Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:03.5868979Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:03.6008739Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:03.6207441Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:03.6262565Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:03.6314054Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:03.6360268Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:04.1111244Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:04.1199908Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:04.2644329Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:04.2683607Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:04.2728815Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:19.4306259Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:19.4362925Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:19.4448677Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:27:19.5308750Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:30:01.1816509Z","@mt":"检测到文件变更: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:30:01.1875560Z","@mt":"检测到文件变更: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:30:01.1942209Z","@mt":"插件未加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:30:01.2826974Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 327","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:30:01.2904386Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:30:01.3491191Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:30:12.5033071Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:30:12.5159694Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:30:12.5214575Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:30:12.5937710Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:34.2721713Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:34.5605596Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 327","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:34.5929621Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:34.7352957Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:34.8366181Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 327","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:34.8440550Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:34.9055555Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:34.9118486Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:34.9218879Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:34.9262330Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:34.9382637Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:34.9413187Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:35.1832111Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:35.2395485Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:35.2925268Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:35.2952512Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:35.2974836Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:51.0580610Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:51.0645919Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:51.0736353Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:31:51.0894422Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:32:03.2521991Z","@mt":"检测到文件变更: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:32:03.2580312Z","@mt":"检测到文件变更: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:32:03.2662439Z","@mt":"插件未加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:32:03.3471007Z","@mt":"配置模块服务失败: {Name}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.PluginManager.ConfigureModuleServicesAsync(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\HotReload\\PluginManager.cs:line 327","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:32:03.3561720Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:32:03.3746328Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:57.7039163Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:57.9072701Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:57.9255755Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.1003567Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.1215495Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.1264784Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.1330359Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.1581321Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.1725134Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.1760251Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.1824939Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.1927833Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.5319224Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.5579392Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.6605529Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.6650177Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:36:58.6685047Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:37:13.6084742Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:37:13.6148751Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:37:13.6257271Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:37:13.6430495Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:37:33.0126201Z","@mt":"检测到文件变更: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:37:33.0194402Z","@mt":"检测到文件变更: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:37:33.0260529Z","@mt":"插件未加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:37:33.0580563Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:37:33.0625151Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:37:33.0790291Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:38:50.7397479Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:27.1525334Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:35.2357293Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:35.2398543Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:35.3686073Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:35.4254835Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:35.4286755Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:35.4361025Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:35.4447252Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:35.4610981Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:35.4659640Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:35.4693407Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:35.4733994Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":17,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:35.9216260Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:35.9305859Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:36.0277901Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:36.0306730Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:46:36.0341581Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.0869500Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.2325385Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.2369566Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.3635802Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.4082474Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.4127355Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.4607531Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.5057663Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.5202222Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.5231246Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.5276567Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.5321950Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.7197779Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.7257550Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.8324744Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.8368362Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:52:32.8411033Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:25.6072967Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:25.8040419Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:25.8143121Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:25.9697307Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:26.0001741Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:26.0092325Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:26.1085006Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:26.1547861Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:26.1742105Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:26.1791988Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:26.1852866Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:26.1890424Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:26.4742284Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:26.4799400Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:26.5812512Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:26.5847958Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T09:53:26.5880304Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:08.4634318Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:08.6841555Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:08.6871452Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:08.8030536Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:08.8242002Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:08.8268785Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:08.8458721Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:08.8527343Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:08.8651266Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:08.8687208Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:08.8719761Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:08.8754029Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:09.2170747Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:09.2235343Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:09.2985224Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:09.3020947Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:09.3060716Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:51.2691471Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:51.2745967Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:51.2819722Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:51.2996148Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:58.8078189Z","@mt":"检测到文件变更: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:58.8140927Z","@mt":"检测到文件变更: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:58.8198629Z","@mt":"插件未加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:58.8448646Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:58.8483490Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:08:58.8649026Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:11:30.3926566Z","@mt":"检测到文件删除: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:11:30.4023760Z","@mt":"检测到文件删除: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:11:30.4073747Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:11:30.4246530Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":28,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:11:34.4109595Z","@mt":"检测到文件变更: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:11:34.4146595Z","@mt":"检测到文件变更: {FilePath}","FilePath":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:11:34.4180870Z","@mt":"插件未加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:11:34.4422360Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:11:34.4453267Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-16T10:11:34.4590879Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
