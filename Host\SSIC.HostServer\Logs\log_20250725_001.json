{"@t":"2025-07-24T17:34:27.5855368Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:27.5824009Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:27.6100867Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:27.6208602Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:27.6220255Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:27.6490134Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:27.6522823Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:27.6532024Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:27.6554949Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:27.7500433Z","@mt":"Hosting failed to start","@l":"Error","@x":"System.IO.IOException: Failed to bind to address http://127.0.0.1:5246: address already in use.\r\n ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。\r\n ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。\r\n   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)\r\n   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)\r\n   at System.Net.Sockets.Socket.Bind(EndPoint localEP)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)","EventId":{"Id":11,"Name":"HostedServiceStartupFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:27.7639728Z","@mt":"文件监控已停止","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:27.7662974Z","@mt":"已删除临时目录: {Path}","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_a851556c21c14ab1869ae25f8352eedf","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:29.1845621Z","@mt":"初始化热插拔服务失败","@l":"Error","@x":"System.ObjectDisposedException: Cannot access a disposed object.\r\nObject name: 'IServiceProvider'.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ThrowHelper.ThrowObjectDisposedException()\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.<>c__DisplayClass11_0.<<UseHotReload>b__0>d.MoveNext() in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\HotReload\\HotReloadExtensions.cs:line 202","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:04.1466584Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:04.1433658Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:04.1796539Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:04.1954076Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:04.1974637Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:04.2273573Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:04.2321332Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:04.2333722Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:04.2374316Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:04.3368893Z","@mt":"Hosting failed to start","@l":"Error","@x":"System.IO.IOException: Failed to bind to address http://127.0.0.1:5246: address already in use.\r\n ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。\r\n ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。\r\n   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)\r\n   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)\r\n   at System.Net.Sockets.Socket.Bind(EndPoint localEP)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)","EventId":{"Id":11,"Name":"HostedServiceStartupFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:04.3535285Z","@mt":"文件监控已停止","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:04.3566795Z","@mt":"已删除临时目录: {Path}","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_7c107271a58449c9a901a5b65e668e2d","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:05.7610107Z","@mt":"初始化热插拔服务失败","@l":"Error","@x":"System.ObjectDisposedException: Cannot access a disposed object.\r\nObject name: 'IServiceProvider'.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ThrowHelper.ThrowObjectDisposedException()\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.<>c__DisplayClass11_0.<<UseHotReload>b__0>d.MoveNext() in F:\\SSIC\\Host\\SSIC.Infrastructure\\Startups\\HotReload\\HotReloadExtensions.cs:line 202","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:35.5817079Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:35.5890260Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:35.6122330Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:35.6219443Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:35.6231613Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:35.6511143Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:35.6550329Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:35.6560117Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:35.6584592Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:35.7526074Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:35.7543874Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:35.7553141Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:35.7560623Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:37.1828787Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:37.1848021Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:37.1859177Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:37.1867026Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:37.1874398Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:37.6912184Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:37.6938024Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:37.6956347Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:37.6968061Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:35:37.6974034Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:35.6758448Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:35.6744217Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:35.7134329Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:35.7271889Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:35.7288794Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:35.7546750Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:35.7574490Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:35.7586745Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:35.7611786Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:35.8497330Z","@mt":"Now listening on: {address}","address":"http://localhost:5247","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:35.8525970Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:35.8540328Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:35.8550297Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:37.2874658Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:37.2894322Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:37.2906429Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:37.2914311Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:37.2921375Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:37.7972404Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:37.8001457Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:37.8022483Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:37.8031722Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:36:37.8045672Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.3224704Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.3476599Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.3727857Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.4394019Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.4415504Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.4912887Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.5049851Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.5072525Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.5167763Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.8022502Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.8061467Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.8425751Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.8476782Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:09.8777317Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.0501923Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.0784824Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.0816458Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.0836213Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.0857071Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.1565441Z","@mt":"首次请求触发端点刷新","@tr":"0169f6205fdc9a9967bc2ee9e63bdbb9","@sp":"d196c29ac517da55","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR3DMQDUM:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAR3DMQDUM","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.1753550Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"0169f6205fdc9a9967bc2ee9e63bdbb9","@sp":"d196c29ac517da55","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR3DMQDUM:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAR3DMQDUM","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.6049764Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"14fec75ccb6da66ac2c633d3c783cc80","@sp":"9f91336f3d50d81d","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAR3DMQDUM:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAR3DMQDUM","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.6096417Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"14fec75ccb6da66ac2c633d3c783cc80","@sp":"9f91336f3d50d81d","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAR3DMQDUM:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAR3DMQDUM","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.6182307Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.6212950Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"14fec75ccb6da66ac2c633d3c783cc80","@sp":"9f91336f3d50d81d","PathCount":24,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAR3DMQDUM:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAR3DMQDUM","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.6242552Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.6295582Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.6325750Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:38:11.6352735Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:28.2119363Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:28.2081082Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:28.2456826Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:28.2608855Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:28.2627470Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:28.2955361Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:28.3009086Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:28.3022040Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:28.3079066Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:28.4223632Z","@mt":"Now listening on: {address}","address":"http://localhost:5247","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:28.4247238Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:28.4264840Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:28.4278013Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:29.8313942Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:29.8345295Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:29.8359387Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:29.8379885Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:29.8395629Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:30.3433534Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:30.3456992Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:30.3477676Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:30.3484892Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:30.3492324Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8046617Z","@mt":"检测到文件删除: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Infrastructure.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8067932Z","@mt":"检测到文件删除: {FilePath}","FilePath":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Infrastructure.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8099330Z","@mt":"插件未加载: {Name}","@l":"Warning","Name":"SSIC.Infrastructure","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8176560Z","@mt":"检测到文件变更: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Infrastructure.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8196592Z","@mt":"检测到文件变更: {FilePath}","FilePath":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Infrastructure.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8213195Z","@mt":"插件未加载: {Name}","@l":"Warning","Name":"SSIC.Infrastructure","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8316252Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Infrastructure","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8337314Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Infrastructure","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8377380Z","@mt":"检测到文件删除: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8391729Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Infrastructure","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8410308Z","@mt":"检测到文件删除: {FilePath}","FilePath":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8448305Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8486492Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8495545Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8541396Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8541983Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8581034Z","@mt":"检测到文件变更: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8588681Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8602989Z","@mt":"检测到文件变更: {FilePath}","FilePath":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8614164Z","@mt":"已使用简化服务刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8637431Z","@mt":"插件未加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8694321Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8712148Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8752301Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8829793Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8844022Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8858574Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8872014Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:40:41.8883872Z","@mt":"已使用简化服务刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:37.3341599Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:37.3279420Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:37.3732148Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:37.3907427Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:37.3940693Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:37.4302382Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:37.4351509Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:37.4367913Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:37.4407228Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:37.5557551Z","@mt":"Now listening on: {address}","address":"http://localhost:5247","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:37.5594320Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:37.5613479Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:37.5633131Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:38.9605085Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:38.9628323Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:38.9651167Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:38.9663595Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:38.9677500Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:39.4810765Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:39.4839520Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:39.4861654Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:39.4873596Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:41:39.4883947Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:42:19.4696431Z","@mt":"首次请求触发端点刷新","@tr":"e76ab6244b90cd487591bc7bcdc30103","@sp":"b7eff3512e284bc6","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR5O1RNEP:00000001","RequestPath":"/api/auth/role","ConnectionId":"0HNEAR5O1RNEP","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:42:19.4785032Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"e76ab6244b90cd487591bc7bcdc30103","@sp":"b7eff3512e284bc6","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR5O1RNEP:00000001","RequestPath":"/api/auth/role","ConnectionId":"0HNEAR5O1RNEP","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:42:19.4864449Z","@mt":"Failed to determine the https port for redirect.","@l":"Warning","@tr":"e76ab6244b90cd487591bc7bcdc30103","@sp":"b7eff3512e284bc6","EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEAR5O1RNEP:00000001","RequestPath":"/api/auth/role","ConnectionId":"0HNEAR5O1RNEP","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:12.2515099Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:12.2462779Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:12.2749084Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:12.2869336Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:12.2883161Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:12.3169311Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:12.3208380Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:12.3215761Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:12.3243612Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:12.4119736Z","@mt":"Now listening on: {address}","address":"http://localhost:5247","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:12.4142429Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:12.4152011Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:12.4160574Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:13.8408854Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:13.8433300Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:13.8447743Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:13.8456151Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:13.8464751Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:14.3596349Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:14.3628400Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:14.3646042Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:14.3655022Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:44:14.3661873Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:38.2890128Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:38.2890735Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:38.3241967Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:38.3395660Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:38.3416498Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:38.3718015Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:38.3775914Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:38.3789661Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:38.3851784Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:38.4911220Z","@mt":"Now listening on: {address}","address":"http://localhost:5247","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:38.4932575Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:38.4942593Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:38.4953602Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:39.9019266Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:39.9042985Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:39.9053038Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:39.9062134Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:39.9074278Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:40.4093886Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:40.4137161Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:40.4157048Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:40.4167118Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:45:40.4174955Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:40.6783951Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:40.6713673Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:40.7025159Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:40.7145756Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:40.7162778Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:40.7455957Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:40.7489335Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:40.7509367Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:40.7573586Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:40.8548278Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:40.8568279Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:40.8577186Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:40.8585484Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:42.2828251Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:42.2857240Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:42.2872467Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:42.2879148Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:42.2884955Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:42.8037400Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:42.8071658Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:42.8097708Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:42.8108729Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:46:42.8118036Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:47:19.4370843Z","@mt":"首次请求触发端点刷新","@tr":"d56372632a756a1ae4a144fe749a2a56","@sp":"e690c2f5129b22df","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR8HEIIVP:00000001","RequestPath":"/api/auth/role","ConnectionId":"0HNEAR8HEIIVP","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:47:19.4428981Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"d56372632a756a1ae4a144fe749a2a56","@sp":"e690c2f5129b22df","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR8HEIIVP:00000001","RequestPath":"/api/auth/role","ConnectionId":"0HNEAR8HEIIVP","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:47:19.4494737Z","@mt":"Failed to determine the https port for redirect.","@l":"Warning","@tr":"d56372632a756a1ae4a144fe749a2a56","@sp":"e690c2f5129b22df","EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEAR8HEIIVP:00000001","RequestPath":"/api/auth/role","ConnectionId":"0HNEAR8HEIIVP","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:48:02.4843094Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"afacba48abaebd96ab346865c8e79275","@sp":"55a5b72b24e6ac2b","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAR8HEIIVS:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAR8HEIIVS","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:48:02.4874766Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"afacba48abaebd96ab346865c8e79275","@sp":"55a5b72b24e6ac2b","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAR8HEIIVS:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAR8HEIIVS","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:48:02.4949663Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"afacba48abaebd96ab346865c8e79275","@sp":"55a5b72b24e6ac2b","PathCount":24,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAR8HEIIVS:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAR8HEIIVS","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:48:23.1743101Z","@mt":"Application is shutting down...","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":3,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:48:23.1866795Z","@mt":"文件监控已停止","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:48:23.1898565Z","@mt":"已删除临时目录: {Path}","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_665d935d64ad4e279be9d075a2a3cab2","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:26.0034283Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:26.0075705Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:26.0358222Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:26.0440794Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:26.0457522Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:26.0725306Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:26.0780696Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:26.0800657Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:26.0845463Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:26.1949164Z","@mt":"Now listening on: {address}","address":"http://localhost:5248","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:26.1971428Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:26.1984229Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:26.2002117Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:27.6120094Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:27.6143170Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:27.6160025Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:27.6172962Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:27.6185474Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:28.1346976Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:28.1413871Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:28.1444990Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:28.1454814Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:28.1464377Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:49.7219021Z","@mt":"首次请求触发端点刷新","@tr":"d7df92efc2dd14c2f4d935fd592bc298","@sp":"e40707c23514e773","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR9U7VNRV:00000001","RequestPath":"/api/auth/role","ConnectionId":"0HNEAR9U7VNRV","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:49.7262118Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"d7df92efc2dd14c2f4d935fd592bc298","@sp":"e40707c23514e773","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAR9U7VNRV:00000001","RequestPath":"/api/auth/role","ConnectionId":"0HNEAR9U7VNRV","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:49:49.7306669Z","@mt":"Failed to determine the https port for redirect.","@l":"Warning","@tr":"d7df92efc2dd14c2f4d935fd592bc298","@sp":"e40707c23514e773","EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEAR9U7VNRV:00000001","RequestPath":"/api/auth/role","ConnectionId":"0HNEAR9U7VNRV","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:10.3376879Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:10.3506881Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:10.3760946Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:10.3911075Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:10.3927415Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:10.4269894Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:10.4326582Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:10.4343336Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:10.4400375Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:10.5631674Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:10.5670872Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:10.5686135Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:10.5703308Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:11.9642063Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:11.9658394Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:11.9669393Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:11.9675884Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:11.9683200Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:12.4732322Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:12.4767884Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:12.4791660Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:12.4806336Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:09:12.4816673Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:33.8431028Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:33.8365363Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:33.8684950Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:33.8831126Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:33.8843628Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:33.9126115Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:33.9181125Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:33.9196289Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:33.9239155Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:34.0361613Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:34.0379635Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:34.0388141Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:34.0395841Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:35.4525860Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:35.4541270Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:35.4553637Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:35.4561522Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:35.4571849Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:35.9614521Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:35.9645180Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:35.9672947Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:35.9688984Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:10:35.9699172Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:14.5039380Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:14.5098053Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:14.5333200Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:14.5447954Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:14.5462509Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:14.5790759Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:14.5859166Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:14.5878469Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:14.5948698Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:14.7314594Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:14.7334891Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:14.7343918Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:14.7355488Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:16.1203637Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:16.1221928Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:16.1238531Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:16.1246904Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:16.1255211Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:16.6287281Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:16.6316232Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:16.6348435Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:16.6362661Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:11:16.6378093Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:02.1504144Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:02.1447680Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:02.1747620Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:02.1853455Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:02.1868952Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:02.2166118Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:02.2197332Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:02.2205173Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:02.2232331Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:02.3269336Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:02.3301189Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:02.3310252Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:02.3318348Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:03.7482971Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:03.7505731Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:03.7529759Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:03.7541096Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:03.7560885Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:04.2584542Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:04.2614963Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:04.2636148Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:04.2653044Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:12:04.2660855Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:18.9077112Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:18.9039294Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:18.9342422Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:18.9443820Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:18.9454739Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:18.9751978Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:18.9792662Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:18.9802313Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:18.9827889Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:19.0870631Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:19.0890096Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:19.0899451Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:19.0908087Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:20.5155633Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:20.5174770Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:20.5183823Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:20.5189746Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:20.5196817Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:21.0233017Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:21.0286017Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:21.0310653Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:21.0319170Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:13:21.0328417Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:02.9315047Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:02.9246206Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:02.9617135Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:02.9741402Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:02.9755392Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:03.0018662Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:03.0044533Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:03.0051789Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:03.0077353Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:03.1148976Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:03.1165817Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:03.1173873Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:03.1181698Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:04.5361436Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:04.5389032Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:04.5404114Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:04.5413845Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:04.5421315Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:05.0530556Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:05.0572967Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:05.0595265Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:05.0607691Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:05.0621256Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:35.3288250Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:35.3345265Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:35.3628750Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:35.3727411Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:35.3741199Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:35.4108915Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:35.4170002Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:35.4191828Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:35.4247952Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:35.5334207Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:35.5361587Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:35.5374016Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:35.5385098Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:36.9425192Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:36.9440739Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:36.9453410Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:36.9461371Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:36.9471448Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:37.4511307Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:37.4538600Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:37.4559328Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:37.4566272Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:14:37.4572985Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:56.7669293Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:56.7614238Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:56.7921192Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:56.8044619Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:56.8059524Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:56.8319867Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:56.8361279Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:56.8369681Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:56.8420595Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:56.9491191Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:56.9509341Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:56.9522681Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:56.9532164Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:58.3582493Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:58.3598629Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:58.3610912Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:58.3620833Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:58.3627735Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:58.8710566Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:58.8764223Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:58.8804759Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:58.8817373Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:17:58.8825786Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:52.1048035Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:52.0999349Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:52.1290003Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:52.1402654Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:52.1415597Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:52.1690512Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:52.1736207Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:52.1747626Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:52.1798189Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:52.3047567Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:52.3066284Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:52.3076352Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:52.3086671Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:53.7056881Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:53.7078366Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:53.7096128Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:53.7106862Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:53.7117811Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:54.2128805Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:54.2153654Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:54.2175321Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:54.2184412Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:19:54.2196554Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:19.3097282Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:19.3030268Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:19.3357515Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:19.3493762Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:19.3503742Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:19.3762417Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:19.3815155Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:19.3825525Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:19.3852783Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:19.4909308Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:19.4929427Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:19.4938544Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:19.4950091Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:20.9090388Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:20.9105183Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:20.9117248Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:20.9125798Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:20.9134941Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:21.4161977Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:21.4189395Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:21.4216525Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:21.4230349Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:21:21.4241835Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:40.7252438Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:40.7322628Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:40.7537211Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:40.7638189Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:40.7647362Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:40.7905219Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:40.7936325Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:40.7949052Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:40.7976128Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:40.8985528Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:40.9005269Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:40.9019204Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:40.9031479Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:42.3201170Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:42.3216996Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:42.3231865Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:42.3239195Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:42.3250972Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:42.8300757Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:42.8334384Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:42.8359182Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:42.8368488Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:23:42.8379117Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.8655307Z","@mt":"检测到文件删除: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.8681656Z","@mt":"检测到文件删除: {FilePath}","FilePath":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.8726951Z","@mt":"成功卸载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.8763269Z","@mt":"已重建路由端点","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.8851373Z","@mt":"检测到文件变更: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.8876679Z","@mt":"检测到文件变更: {FilePath}","FilePath":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.8901662Z","@mt":"插件未加载: {Name}","@l":"Warning","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.8938693Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.8952792Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.8995082Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.9032798Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.9070170Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.9082094Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.9094317Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:24:33.9106493Z","@mt":"已使用简化服务刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:21.9097922Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:21.9097929Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:21.9586724Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:21.9733504Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:21.9750988Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:22.0132401Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:22.0198319Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:22.0214981Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:22.0271613Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:22.1405684Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:22.1428177Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:22.1439538Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:22.1454991Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:23.5464808Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:23.5486245Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:23.5502524Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:23.5512532Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:23.5521678Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:24.0561680Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:24.0584418Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:24.0612151Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:24.0628206Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:26:24.0636784Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:30:06.7345007Z","@mt":"Application is shutting down...","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:30:06.7432523Z","@mt":"文件监控已停止","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-25T10:30:06.7454870Z","@mt":"已删除临时目录: {Path}","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_67b33dc3526945f1b3cbd8c20aadc63c","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":27,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
