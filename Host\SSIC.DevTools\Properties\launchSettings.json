{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "launchUrl": "scalar/v1", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5159"}, "https": {"commandName": "Project", "launchBrowser": true, "launchUrl": "scalar/v1", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7290;http://localhost:5159"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "scalar/v1", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/scalar/v1", "environmentVariables": {"ASPNETCORE_HTTPS_PORTS": "8082", "ASPNETCORE_HTTP_PORTS": "8083"}, "publishAllPorts": true, "useSSL": true}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:44055", "sslPort": 44359}}}