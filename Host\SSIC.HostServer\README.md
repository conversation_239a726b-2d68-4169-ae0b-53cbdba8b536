# SSIC.HostServer 热拔插功能说明

## 功能介绍

SSIC.HostServer 提供了模块的热拔插功能，可以在服务运行期间动态加载和卸载模块，无需重启服务器。这使得系统具有以下特点：

1. **模块化架构**：将不同功能划分为独立模块，便于开发和维护
2. **动态更新**：在不停机的情况下更新模块
3. **灵活扩展**：根据需求动态添加或移除功能模块

## 实现原理

热拔插功能基于以下关键组件实现：

- **DynamicModuleLoader**：负责模块的加载、卸载和重载
- **ApplicationPartManager**：ASP.NET Core的应用部件管理器，用于动态注册控制器
- **FileSystemWatcher**：监控模块目录的文件变化
- **AssemblyLoadContext**：提供程序集的隔离加载和卸载能力

## 目录结构

```
SSIC.HostServer/
├── Modules/                # 模块存放目录
│   ├── SSIC.Modules.Sys/   # 系统管理模块
│   ├── SSIC.Modules.Auth/  # 认证授权模块
│   └── ...                 # 其他模块
├── Program.cs              # 主程序入口，包含热拔插逻辑
└── ...
```

## 使用方法

### 1. 创建模块

模块项目需要遵循以下规范：

- 项目名称以 `SSIC.Modules.` 开头
- 在项目文件(.csproj)中设置输出路径到主服务的Modules目录：

```xml
<PropertyGroup>
    <OutputPath>..\..\SSIC.HostServer\Modules\$(MSBuildProjectName)\</OutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
</PropertyGroup>
```

### 2. 部署模块

将编译好的模块DLL文件及其依赖放入 `SSIC.HostServer/Modules` 目录，系统会自动检测并加载。

### 3. 更新模块

在服务运行期间，直接替换模块DLL文件，系统会自动检测变更并重新加载模块。

### 4. 删除模块

从 `Modules` 目录中删除模块DLL文件，系统会自动卸载该模块。

## 事件监听

系统提供了模块加载和卸载的事件机制，可以通过以下方式监听这些事件：

```csharp
// 在Program.cs中
var dynamicModuleLoader = scope.ServiceProvider.GetRequiredService<DynamicModuleLoader>();

// 监听模块加载事件
dynamicModuleLoader.ModuleLoaded += (sender, args) => {
    if (args.Success) {
        Console.WriteLine($"模块已加载: {args.ModuleName}");
    }
};

// 监听模块卸载事件
dynamicModuleLoader.ModuleUnloaded += (sender, args) => {
    if (args.Success) {
        Console.WriteLine($"模块已卸载: {args.ModuleName}");
    }
};
```

## 注意事项

1. **模块隔离**：确保模块之间的依赖关系清晰，避免循环依赖
2. **状态管理**：模块被卸载时应正确释放资源，避免内存泄漏
3. **版本兼容**：更新模块时注意保持API兼容性，避免破坏性变更
4. **错误处理**：模块加载失败不会影响其他模块和主服务的运行

## 故障排除

如果模块加载失败，请检查：

1. 模块文件名是否符合规范 (`SSIC.Modules.*.dll`)
2. 模块所需的依赖项是否齐全
3. 查看日志中的详细错误信息

## 日志记录

系统会在日志中记录模块的加载、卸载和错误信息，可通过以下方式查看：

1. 控制台输出
2. 应用程序日志文件
3. 监控仪表板(如有) 