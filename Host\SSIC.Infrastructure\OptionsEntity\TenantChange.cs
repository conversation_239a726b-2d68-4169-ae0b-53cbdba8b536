﻿using System.Threading;

namespace SSIC.Infrastructure.OptionsEntity
{
    /// <summary>
    /// 租户设置
    /// </summary>
    public class TenantChange
    {
        /// <summary>
        /// 租户ID
        /// </summary>
        public static AsyncLocal<string> AsyncLocalTenantId = new AsyncLocal<string>();

        /// <summary>
        /// 租户类型1.同库;2.分库
        /// </summary>
        public static AsyncLocal<int> AsyncLocalTenanttype = new AsyncLocal<int>();
    }
}