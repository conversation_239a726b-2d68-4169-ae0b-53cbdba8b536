{"version": 3, "targets": {"net10.0": {"AngleSharp/1.3.0-beta.468": {"type": "package", "compile": {"lib/net8.0/AngleSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/AngleSharp.dll": {"related": ".xml"}}}, "AngleSharp.Css/1.0.0-beta.151": {"type": "package", "dependencies": {"AngleSharp": "[1.0.0, 2.0.0)"}, "compile": {"lib/net8.0/AngleSharp.Css.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/AngleSharp.Css.dll": {"related": ".xml"}}}, "Castle.Core/5.1.1": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "compile": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}}, "DnsClient/1.8.0": {"type": "package", "compile": {"lib/net8.0/DnsClient.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DnsClient.dll": {"related": ".xml"}}}, "Masuit.Tools.Abstractions/2025.1.7": {"type": "package", "dependencies": {"AngleSharp": "1.3.0-beta.468", "AngleSharp.Css": "1.0.0-beta.151", "Castle.Core": "5.1.1", "DnsClient": "1.8.0", "Microsoft.Extensions.Configuration.Json": "9.0.2", "Newtonsoft.Json": "13.0.3", "SharpCompress": "0.39.0", "SixLabors.ImageSharp": "3.1.7", "SixLabors.ImageSharp.Drawing": "2.1.5", "System.Diagnostics.PerformanceCounter": "9.0.2", "System.Management": "9.0.2"}, "compile": {"lib/net9.0/Masuit.Tools.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Masuit.Tools.Abstractions.dll": {"related": ".xml"}}}, "Masuit.Tools.Core/2025.1.7": {"type": "package", "dependencies": {"Masuit.Tools.Abstractions": "2025.1.7", "Microsoft.EntityFrameworkCore": "9.0.2"}, "compile": {"lib/net9.0/Masuit.Tools.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Masuit.Tools.Core.dll": {"related": ".xml"}}}, "Microsoft.DependencyValidation.Analyzers/0.11.0": {"type": "package"}, "Microsoft.EntityFrameworkCore/9.0.2": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.2", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.2", "Microsoft.Extensions.Logging": "9.0.2"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.2": {"type": "package", "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.2": {"type": "package"}, "Microsoft.Extensions.Caching.Abstractions/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.FileProviders.Physical": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.2", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.2": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.2", "Microsoft.Extensions.FileSystemGlobbing": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.2": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.2", "Microsoft.Extensions.Logging.Abstractions": "9.0.2", "Microsoft.Extensions.Options": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/9.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.2", "Microsoft.Extensions.Primitives": "9.0.2"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.2": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "QRCoder/1.6.0": {"type": "package", "compile": {"lib/net6.0/QRCoder.dll": {}}, "runtime": {"lib/net6.0/QRCoder.dll": {}}}, "SharpCompress/0.39.0": {"type": "package", "dependencies": {"ZstdSharp.Port": "0.8.4"}, "compile": {"lib/net8.0/SharpCompress.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/SharpCompress.dll": {"related": ".pdb"}}}, "SixLabors.Fonts/2.0.8": {"type": "package", "compile": {"lib/net6.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "SixLabors.ImageSharp/3.1.7": {"type": "package", "compile": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "SixLabors.ImageSharp.Drawing/2.1.5": {"type": "package", "dependencies": {"SixLabors.Fonts": "2.0.8", "SixLabors.ImageSharp": "3.1.6"}, "compile": {"lib/net6.0/SixLabors.ImageSharp.Drawing.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.Drawing.dll": {"related": ".xml"}}}, "System.CodeDom/9.0.2": {"type": "package", "compile": {"lib/net9.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Configuration.ConfigurationManager/9.0.2": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "9.0.2", "System.Security.Cryptography.ProtectedData": "9.0.2"}, "compile": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Diagnostics.EventLog/9.0.2": {"type": "package", "compile": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.PerformanceCounter/9.0.2": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "9.0.2"}, "compile": {"lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Management/9.0.2": {"type": "package", "dependencies": {"System.CodeDom": "9.0.2"}, "compile": {"lib/net9.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/9.0.2": {"type": "package", "compile": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "ZstdSharp.Port/0.8.4": {"type": "package", "compile": {"lib/net9.0/ZstdSharp.dll": {}}, "runtime": {"lib/net9.0/ZstdSharp.dll": {}}}}}, "libraries": {"AngleSharp/1.3.0-beta.468": {"sha512": "3uIK3EUQbQ9Wl9ufL+ysh3jd5nx4NKZgjAZhjqKbWr8o3oPiBBGzEXSEdLYeh4dlt+PiuO1mjaM+wVtJQeVDTA==", "type": "package", "path": "anglesharp/1.3.0-beta.468", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "anglesharp.1.3.0-beta.468.nupkg.sha512", "anglesharp.nuspec", "lib/net462/AngleSharp.dll", "lib/net462/AngleSharp.xml", "lib/net472/AngleSharp.dll", "lib/net472/AngleSharp.xml", "lib/net8.0/AngleSharp.dll", "lib/net8.0/AngleSharp.xml", "lib/netstandard2.0/AngleSharp.dll", "lib/netstandard2.0/AngleSharp.xml", "logo.png"]}, "AngleSharp.Css/1.0.0-beta.151": {"sha512": "oEnqXQcwpc/kkUIi2rxWHfFrmKlcJFpZZZjhEIHVc+aEJFo3U+5cptOHByQh+FW0PCW6ssVJ+GGBgbgyN8YPiw==", "type": "package", "path": "anglesharp.css/1.0.0-beta.151", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "anglesharp.css.1.0.0-beta.151.nupkg.sha512", "anglesharp.css.nuspec", "lib/net461/AngleSharp.Css.dll", "lib/net461/AngleSharp.Css.xml", "lib/net472/AngleSharp.Css.dll", "lib/net472/AngleSharp.Css.xml", "lib/net6.0/AngleSharp.Css.dll", "lib/net6.0/AngleSharp.Css.xml", "lib/net7.0/AngleSharp.Css.dll", "lib/net7.0/AngleSharp.Css.xml", "lib/net8.0/AngleSharp.Css.dll", "lib/net8.0/AngleSharp.Css.xml", "lib/netstandard2.0/AngleSharp.Css.dll", "lib/netstandard2.0/AngleSharp.Css.xml", "logo.png"]}, "Castle.Core/5.1.1": {"sha512": "rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "type": "package", "path": "castle.core/5.1.1", "files": [".nupkg.metadata", ".signature.p7s", "ASL - Apache Software Foundation License.txt", "CHANGELOG.md", "LICENSE", "castle-logo.png", "castle.core.5.1.1.nupkg.sha512", "castle.core.nuspec", "lib/net462/Castle.Core.dll", "lib/net462/Castle.Core.xml", "lib/net6.0/Castle.Core.dll", "lib/net6.0/Castle.Core.xml", "lib/netstandard2.0/Castle.Core.dll", "lib/netstandard2.0/Castle.Core.xml", "lib/netstandard2.1/Castle.Core.dll", "lib/netstandard2.1/Castle.Core.xml", "readme.txt"]}, "DnsClient/1.8.0": {"sha512": "RRwtaCXkXWsx0mmsReGDqCbRLtItfUbkRJlet1FpdciVhyMGKcPd57T1+8Jki9ojHlq9fntVhXQroOOgRak8DQ==", "type": "package", "path": "dnsclient/1.8.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "dnsclient.1.8.0.nupkg.sha512", "dnsclient.nuspec", "icon.png", "lib/net472/DnsClient.dll", "lib/net472/DnsClient.xml", "lib/net6.0/DnsClient.dll", "lib/net6.0/DnsClient.xml", "lib/net8.0/DnsClient.dll", "lib/net8.0/DnsClient.xml", "lib/netstandard2.0/DnsClient.dll", "lib/netstandard2.0/DnsClient.xml", "lib/netstandard2.1/DnsClient.dll", "lib/netstandard2.1/DnsClient.xml"]}, "Masuit.Tools.Abstractions/2025.1.7": {"sha512": "KTvP4GXXHwRTRdrbOK8QxGi1EdyhKV3geUvPJr9L7TFevdxB1yoT66qtNOJlTtUdz7RChPYf6yTBoPX4+JcT4g==", "type": "package", "path": "masuit.tools.abstractions/2025.1.7", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net461/Masuit.Tools.Abstractions.dll", "lib/net461/Masuit.Tools.Abstractions.xml", "lib/net6.0/Masuit.Tools.Abstractions.dll", "lib/net6.0/Masuit.Tools.Abstractions.xml", "lib/net8.0/Masuit.Tools.Abstractions.dll", "lib/net8.0/Masuit.Tools.Abstractions.xml", "lib/net9.0/Masuit.Tools.Abstractions.dll", "lib/net9.0/Masuit.Tools.Abstractions.xml", "lib/netstandard2.0/Masuit.Tools.Abstractions.dll", "lib/netstandard2.0/Masuit.Tools.Abstractions.xml", "lib/netstandard2.1/Masuit.Tools.Abstractions.dll", "lib/netstandard2.1/Masuit.Tools.Abstractions.xml", "masuit.tools.abstractions.2025.1.7.nupkg.sha512", "masuit.tools.abstractions.nuspec"]}, "Masuit.Tools.Core/2025.1.7": {"sha512": "azDIcg0e3ad3HQysim8/3Janiy57hB259G99qJuCc0rEj80cI3cb0SrbOCKeLTEE3opTSarK9+pj21hvr3AtMQ==", "type": "package", "path": "masuit.tools.core/2025.1.7", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Masuit.Tools.Core.dll", "lib/net6.0/Masuit.Tools.Core.xml", "lib/net8.0/Masuit.Tools.Core.dll", "lib/net8.0/Masuit.Tools.Core.xml", "lib/net9.0/Masuit.Tools.Core.dll", "lib/net9.0/Masuit.Tools.Core.xml", "lib/netstandard2.0/Masuit.Tools.Core.dll", "lib/netstandard2.0/Masuit.Tools.Core.xml", "lib/netstandard2.1/Masuit.Tools.Core.dll", "lib/netstandard2.1/Masuit.Tools.Core.xml", "masuit.tools.core.2025.1.7.nupkg.sha512", "masuit.tools.core.nuspec"]}, "Microsoft.DependencyValidation.Analyzers/0.11.0": {"sha512": "3xBVhMsM4o9cDpsvmlv78T1FG+cTH8i6bjl1Irk6hoIwEc6Ekoz4mKAR6Hn5M/2G2ZsHNrYBNwGkgF7aiw74UA==", "type": "package", "path": "microsoft.dependencyvalidation.analyzers/0.11.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/Microsoft.DependencyValidation.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/de/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/es/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/fr/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/it/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/ja/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/ko/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/pl/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/pt-BR/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/ru/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/tr/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/zh-HANS/Microsoft.DependencyValidation.Analyzers.resources.dll", "analyzers/dotnet/zh-HANT/Microsoft.DependencyValidation.Analyzers.resources.dll", "microsoft.dependencyvalidation.analyzers.0.11.0.nupkg.sha512", "microsoft.dependencyvalidation.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.EntityFrameworkCore/9.0.2": {"sha512": "P90ZuybgcpW32y985eOYxSoZ9IiL0UTYQlY0y1Pt1iHAnpZj/dQHREpSpry1RNvk8YjAeoAkWFdem5conqB9zQ==", "type": "package", "path": "microsoft.entityframeworkcore/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props", "lib/net8.0/Microsoft.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.9.0.2.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.2": {"sha512": "oVSjNSIYHsk0N66eqAWgDcyo9etEFbUswbz7SmlYR6nGp05byHrJAYM5N8U2aGWJWJI6WvIC2e4TXJgH6GZ6HQ==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.9.0.2.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.2": {"sha512": "w4jzX7XI+L3erVGzbHXpx64A3QaLXxqG3f1vPpGYYZGpxOIHkh7e4iLLD7cq4Ng1vjkwzWl5ZJp0Kj/nHsgFYg==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "docs/PACKAGE.md", "microsoft.entityframeworkcore.analyzers.9.0.2.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/9.0.2": {"sha512": "a7QhA25n+BzSM5r5d7JznfyluMBGI7z3qyLlFviZ1Eiqv6DdiK27sLZdP/rpYirBM6UYAKxu5TbmfhIy13GN9A==", "type": "package", "path": "microsoft.extensions.caching.abstractions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.9.0.2.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/9.0.2": {"sha512": "AlEfp0DMz8E1h1Exi8LBrUCNmCYcGDfSM4F/uK1D1cYx/R3w0LVvlmjICqxqXTsy7BEZaCf5leRZY2FuPEiFaw==", "type": "package", "path": "microsoft.extensions.caching.memory/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net9.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net9.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.9.0.2.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/9.0.2": {"sha512": "EBZW+u96tApIvNtjymXEIS44tH0I/jNwABHo4c33AchWOiDWCq2rL3klpnIo+xGrxoVGJzPDISV6hZ+a9C9SzQ==", "type": "package", "path": "microsoft.extensions.configuration/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.2.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.2": {"sha512": "I0O/270E/lUNqbBxlRVjxKOMZyYjP88dpEgQTveml+h2lTzAP4vbawLVwjS9SC7lKaU893bwyyNz0IVJYsm9EA==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.2.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.2": {"sha512": "tPgue19a0G+8Vb1ShCj4PmhjvVaEOfFb1L89WCr5aEpY1JUgIuYWsfELKf92Njwg53o4C+yWbE4UqbyQtLpKTg==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.9.0.2.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/9.0.2": {"sha512": "u2eIYagO91VmRbKYQ5pmElWC6JWX7GPQbP57EX09zzFcI1ZMPDCykr07ikPB4ecgBZzG+UAhTcViTLe0gSF4WQ==", "type": "package", "path": "microsoft.extensions.configuration.json/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.9.0.2.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.2": {"sha512": "ZffbJrskOZ40JTzcTyKwFHS5eACSWp2bUQBBApIgGV+es8RaTD4OxUG7XxFr3RIPLXtYQ1jQzF2DjKB5fZn7Qg==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.2": {"sha512": "MNe7GSTBf3jQx5vYrXF0NZvn6l7hUKF6J54ENfAgCO8y6xjN1XUmKKWG464LP2ye6QqDiA1dkaWEZBYnhoZzjg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.2": {"sha512": "IcOBmTlr2jySswU+3x8c3ql87FRwTVPQgVKaV5AXzPT5u0VItfNU8SMbESpdSp5STwxT/1R99WYszgHWsVkzhg==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.2.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/9.0.2": {"sha512": "WcPkJx/OAaXG5xHvxYsoLY8qGsyCfHWsbDJtfMtHRWtceF/EmqAsqkHYsouh82gjxdZwfySvj3nGVi8AkwlYhA==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.9.0.2.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/9.0.2": {"sha512": "wAjk+6rvvU4WesskJ6rJX1FYL/S9zvnpqMai/pXb07+gtXpO7DhFfuKzYHwkKN3HAUq2W4CD+YLYenHwAS3DCA==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.9.0.2.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/9.0.2": {"sha512": "loV/0UNpt2bD+6kCDzFALVE63CDtqzPeC0LAetkdhiEr/tTNbvOlQ7CBResH7BQBd3cikrwiBfaHdyHMFUlc2g==", "type": "package", "path": "microsoft.extensions.logging/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.2.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.2": {"sha512": "dV9s2Lamc8jSaqhl2BQSPn/AryDIH2sSbQUyLitLXV0ROmsb+SROnn2cH939JFbsNrnf3mIM3GNRKT7P0ldwLg==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.2.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.2": {"sha512": "zr98z+AN8+isdmDmQRuEJ/DAKZGUTHmdv3t0ZzjHvNqvA44nAgkXE9kYtfoN6581iALChhVaSw2Owt+Z2lVbkQ==", "type": "package", "path": "microsoft.extensions.options/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.2.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.2": {"sha512": "puBMtKe/wLuYa7H6docBkLlfec+h8L35DXqsDKKJgW0WY5oCwJ3cBJKcDaZchv6knAyqOMfsl6VUbaR++E5LXA==", "type": "package", "path": "microsoft.extensions.primitives/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.2.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "QRCoder/1.6.0": {"sha512": "XmPA81eo+oRxBuyVdswsSkTGTE1d3thfF11Z1PdD7oB56A6HU4G4AAOdySmGRMb/cljwlFTMWUtosGEnwpS6GA==", "type": "package", "path": "qrcoder/1.6.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/QRCoder.dll", "lib/net40/QRCoder.dll", "lib/net5.0-windows7.0/QRCoder.dll", "lib/net5.0/QRCoder.dll", "lib/net6.0-windows7.0/QRCoder.dll", "lib/net6.0/QRCoder.dll", "lib/netstandard1.3/QRCoder.dll", "lib/netstandard2.0/QRCoder.dll", "nuget-icon.png", "nuget-readme.md", "qrcoder.1.6.0.nupkg.sha512", "qrcoder.nuspec"]}, "SharpCompress/0.39.0": {"sha512": "0esqIUDlg68Z7+Weuge4QzEvNtawUO4obTJFL7xuf4DBHMxVRr+wbNgiX9arMrj3kGXQSvLe0zbZG3oxpkwJOA==", "type": "package", "path": "sharpcompress/0.39.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net48/SharpCompress.dll", "lib/net48/SharpCompress.pdb", "lib/net481/SharpCompress.dll", "lib/net481/SharpCompress.pdb", "lib/net6.0/SharpCompress.dll", "lib/net6.0/SharpCompress.pdb", "lib/net8.0/SharpCompress.dll", "lib/net8.0/SharpCompress.pdb", "lib/netstandard2.0/SharpCompress.dll", "lib/netstandard2.0/SharpCompress.pdb", "sharpcompress.0.39.0.nupkg.sha512", "sharpcompress.nuspec"]}, "SixLabors.Fonts/2.0.8": {"sha512": "psTLKJVKaD8PKm+Bz/mSSNo4m0VCTYoJ97TF/ynxEWkn4sFUipI0rzE2V/KBtfU0TOn/gF/+1XBOEvusGZZMhA==", "type": "package", "path": "sixlabors.fonts/2.0.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/net6.0/SixLabors.Fonts.dll", "lib/net6.0/SixLabors.Fonts.xml", "sixlabors.fonts.128.png", "sixlabors.fonts.2.0.8.nupkg.sha512", "sixlabors.fonts.nuspec"]}, "SixLabors.ImageSharp/3.1.7": {"sha512": "9fIOOAsyLFid6qKypM2Iy0Z3Q9yoanV8VoYAHtI2sYGMNKzhvRTjgFDHonIiVe+ANtxIxM6SuqUzj0r91nItpA==", "type": "package", "path": "sixlabors.imagesharp/3.1.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "build/SixLabors.ImageSharp.props", "lib/net6.0/SixLabors.ImageSharp.dll", "lib/net6.0/SixLabors.ImageSharp.xml", "sixlabors.imagesharp.128.png", "sixlabors.imagesharp.3.1.7.nupkg.sha512", "sixlabors.imagesharp.nuspec"]}, "SixLabors.ImageSharp.Drawing/2.1.5": {"sha512": "cER1JfvshYDmTxw+gUy/x5e1xoNWhrD6s3AFf8rRUx9hWSXYdOFreQfUrM/QooMj0rF7+hkVtvGnV3EdMx4dxA==", "type": "package", "path": "sixlabors.imagesharp.drawing/2.1.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/net6.0/SixLabors.ImageSharp.Drawing.dll", "lib/net6.0/SixLabors.ImageSharp.Drawing.xml", "sixlabors.imagesharp.drawing.128.png", "sixlabors.imagesharp.drawing.2.1.5.nupkg.sha512", "sixlabors.imagesharp.drawing.nuspec"]}, "System.CodeDom/9.0.2": {"sha512": "VnlzS7afE8LaXXwKdHOe2D6FcDekqThzU1E67CONV7gp71q3zposqcSeXH+PxARMXC5j31efwXrxP8VGvD70Ug==", "type": "package", "path": "system.codedom/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/net9.0/System.CodeDom.dll", "lib/net9.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.9.0.2.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Configuration.ConfigurationManager/9.0.2": {"sha512": "pX5GhR4/mfYUbebRdINO6P4B8stIbVWaAWGgWzYO1eC30nUVpCQdzQjKwCPhtLdzjR3d/q+MQ0sRG3B1GLSyqw==", "type": "package", "path": "system.configuration.configurationmanager/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/net9.0/System.Configuration.ConfigurationManager.dll", "lib/net9.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.9.0.2.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/9.0.2": {"sha512": "i+Fe6Fpst/onydFLBGilCr/Eh9OFdlaTU/c3alPp6IbLZXQJOgpIu3l4MOnmsN8fDYq5nAyHSqNIJesc74Yw3Q==", "type": "package", "path": "system.diagnostics.eventlog/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/net9.0/System.Diagnostics.EventLog.dll", "lib/net9.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.9.0.2.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.PerformanceCounter/9.0.2": {"sha512": "hRq8SBG2CYALIJqOYLsZW749wITem4JTXIL0YxYfovWSbCKF6Eu1AEMJ54Va1rg2I5o3aTD6QvBaRw4x/v7Sfw==", "type": "package", "path": "system.diagnostics.performancecounter/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.PerformanceCounter.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.PerformanceCounter.targets", "lib/net462/System.Diagnostics.PerformanceCounter.dll", "lib/net462/System.Diagnostics.PerformanceCounter.xml", "lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "lib/net9.0/System.Diagnostics.PerformanceCounter.dll", "lib/net9.0/System.Diagnostics.PerformanceCounter.xml", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net9.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net9.0/System.Diagnostics.PerformanceCounter.xml", "system.diagnostics.performancecounter.9.0.2.nupkg.sha512", "system.diagnostics.performancecounter.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/9.0.2": {"sha512": "U6SXFe1kfJySAvcPPdbdb+fycaY+3c/KV0PwTjurvrALMnlSm37s5z8zcoI7qbkV2kYhxiLfsZRGiF0XeSsqSQ==", "type": "package", "path": "system.management/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/net9.0/System.Management.dll", "lib/net9.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "runtimes/win/lib/net9.0/System.Management.dll", "runtimes/win/lib/net9.0/System.Management.xml", "system.management.9.0.2.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/9.0.2": {"sha512": "+z9JWtU9di45tW/w6zpuJms4SnAAqKY2Usmmxs7MpOhxWoIeR2pKl1vEkaFbEx52ZfrMFcZXsFe+8WERMozzGg==", "type": "package", "path": "system.security.cryptography.protecteddata/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net8.0/System.Security.Cryptography.ProtectedData.dll", "lib/net8.0/System.Security.Cryptography.ProtectedData.xml", "lib/net9.0/System.Security.Cryptography.ProtectedData.dll", "lib/net9.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.9.0.2.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "ZstdSharp.Port/0.8.4": {"sha512": "eieSXq3kakCUXbgdxkKaRqWS6hF0KBJcqok9LlDCs60GOyrynLvPOcQ0pRw7shdPF7lh/VepJ9cP9n9HHc759g==", "type": "package", "path": "zstdsharp.port/0.8.4", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/ZstdSharp.dll", "lib/net5.0/ZstdSharp.dll", "lib/net6.0/ZstdSharp.dll", "lib/net7.0/ZstdSharp.dll", "lib/net8.0/ZstdSharp.dll", "lib/net9.0/ZstdSharp.dll", "lib/netcoreapp3.1/ZstdSharp.dll", "lib/netstandard2.0/ZstdSharp.dll", "lib/netstandard2.1/ZstdSharp.dll", "zstdsharp.port.0.8.4.nupkg.sha512", "zstdsharp.port.nuspec"]}}, "projectFileDependencyGroups": {"net10.0": ["Masuit.Tools.Core >= 2025.1.7", "Microsoft.DependencyValidation.Analyzers >= 0.11.0", "QRCoder >= 1.6.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\SSIC\\Host\\SSIC.Utilities\\SSIC.Utilities.csproj", "projectName": "SSIC.Utilities", "projectPath": "F:\\SSIC\\Host\\SSIC.Utilities\\SSIC.Utilities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\SSIC\\Host\\SSIC.Utilities\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net10.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "http://mes:10881/repository/MESCore/": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}, "https://nuget.pkg.github.com/zydld888/index.json": {}}, "frameworks": {"net10.0": {"targetAlias": "net10.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net10.0": {"targetAlias": "net10.0", "dependencies": {"Masuit.Tools.Core": {"target": "Package", "version": "[2025.1.7, )"}, "Microsoft.DependencyValidation.Analyzers": {"target": "Package", "version": "[0.11.0, )"}, "QRCoder": {"target": "Package", "version": "[1.6.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.2.25164.34/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(, 4.7.0]", "Microsoft.NETCore.App": "(, 2.1.0]", "Microsoft.VisualBasic": "(, 10.4.0]", "Microsoft.Win32.Primitives": "(, 4.3.0]", "Microsoft.Win32.Registry": "(, 5.0.0]", "runtime.any.System.Collections": "(, 4.3.0]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.0]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.0]", "runtime.any.System.Globalization": "(, 4.3.0]", "runtime.any.System.Globalization.Calendars": "(, 4.3.0]", "runtime.any.System.IO": "(, 4.3.0]", "runtime.any.System.Reflection": "(, 4.3.0]", "runtime.any.System.Reflection.Extensions": "(, 4.3.0]", "runtime.any.System.Reflection.Primitives": "(, 4.3.0]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.0]", "runtime.any.System.Runtime": "(, 4.3.1]", "runtime.any.System.Runtime.Handles": "(, 4.3.0]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.0]", "runtime.any.System.Text.Encoding": "(, 4.3.0]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.0]", "runtime.any.System.Threading.Tasks": "(, 4.3.0]", "runtime.any.System.Threading.Timer": "(, 4.3.0]", "runtime.aot.System.Collections": "(, 4.3.0]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.0]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.0]", "runtime.aot.System.Globalization": "(, 4.3.0]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.0]", "runtime.aot.System.IO": "(, 4.3.0]", "runtime.aot.System.Reflection": "(, 4.3.0]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.0]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.0]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.0]", "runtime.aot.System.Runtime": "(, 4.3.1]", "runtime.aot.System.Runtime.Handles": "(, 4.3.0]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.0]", "runtime.aot.System.Text.Encoding": "(, 4.3.0]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.0]", "runtime.aot.System.Threading.Tasks": "(, 4.3.0]", "runtime.aot.System.Threading.Timer": "(, 4.3.0]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.1]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.0]", "runtime.unix.System.Console": "(, 4.3.1]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.0]", "runtime.unix.System.IO.FileSystem": "(, 4.3.0]", "runtime.unix.System.Net.Primitives": "(, 4.3.0]", "runtime.unix.System.Net.Sockets": "(, 4.3.0]", "runtime.unix.System.Private.Uri": "(, 4.3.1]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.1]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.0]", "runtime.win.System.Console": "(, 4.3.1]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.0]", "runtime.win.System.IO.FileSystem": "(, 4.3.0]", "runtime.win.System.Net.Primitives": "(, 4.3.0]", "runtime.win.System.Net.Sockets": "(, 4.3.0]", "runtime.win.System.Runtime.Extensions": "(, 4.3.1]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win7.System.Private.Uri": "(, 4.3.1]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.2]", "System.AppContext": "(, 4.3.0]", "System.Buffers": "(, 5.0.0]", "System.Collections": "(, 4.3.0]", "System.Collections.Concurrent": "(, 4.3.0]", "System.Collections.Immutable": "(, 10.0.0]", "System.Collections.NonGeneric": "(, 4.3.0]", "System.Collections.Specialized": "(, 4.3.0]", "System.ComponentModel": "(, 4.3.0]", "System.ComponentModel.Annotations": "(, 4.3.0]", "System.ComponentModel.EventBasedAsync": "(, 4.3.0]", "System.ComponentModel.Primitives": "(, 4.3.0]", "System.ComponentModel.TypeConverter": "(, 4.3.0]", "System.Console": "(, 4.3.1]", "System.Data.Common": "(, 4.3.0]", "System.Data.DataSetExtensions": "(, 4.4.0]", "System.Diagnostics.Contracts": "(, 4.3.0]", "System.Diagnostics.Debug": "(, 4.3.0]", "System.Diagnostics.DiagnosticSource": "(, 10.0.0]", "System.Diagnostics.FileVersionInfo": "(, 4.3.0]", "System.Diagnostics.Process": "(, 4.3.0]", "System.Diagnostics.StackTrace": "(, 4.3.0]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.0]", "System.Diagnostics.Tools": "(, 4.3.0]", "System.Diagnostics.TraceSource": "(, 4.3.0]", "System.Diagnostics.Tracing": "(, 4.3.0]", "System.Drawing.Primitives": "(, 4.3.0]", "System.Dynamic.Runtime": "(, 4.3.0]", "System.Formats.Asn1": "(, 10.0.0]", "System.Formats.Tar": "(, 10.0.0]", "System.Globalization": "(, 4.3.0]", "System.Globalization.Calendars": "(, 4.3.0]", "System.Globalization.Extensions": "(, 4.3.0]", "System.IO": "(, 4.3.0]", "System.IO.Compression": "(, 4.3.0]", "System.IO.Compression.ZipFile": "(, 4.3.0]", "System.IO.FileSystem": "(, 4.3.0]", "System.IO.FileSystem.AccessControl": "(, 4.4.0]", "System.IO.FileSystem.DriveInfo": "(, 4.3.1]", "System.IO.FileSystem.Primitives": "(, 4.3.0]", "System.IO.FileSystem.Watcher": "(, 4.3.0]", "System.IO.IsolatedStorage": "(, 4.3.0]", "System.IO.MemoryMappedFiles": "(, 4.3.0]", "System.IO.Pipelines": "(, 10.0.0]", "System.IO.Pipes": "(, 4.3.0]", "System.IO.Pipes.AccessControl": "(, 5.0.0]", "System.IO.UnmanagedMemoryStream": "(, 4.3.0]", "System.Linq": "(, 4.3.0]", "System.Linq.AsyncEnumerable": "(, 10.0.0]", "System.Linq.Expressions": "(, 4.3.0]", "System.Linq.Parallel": "(, 4.3.0]", "System.Linq.Queryable": "(, 4.3.0]", "System.Memory": "(, 5.0.0]", "System.Net.Http": "(, 4.3.4]", "System.Net.Http.Json": "(, 10.0.0]", "System.Net.NameResolution": "(, 4.3.0]", "System.Net.NetworkInformation": "(, 4.3.0]", "System.Net.Ping": "(, 4.3.0]", "System.Net.Primitives": "(, 4.3.1]", "System.Net.Requests": "(, 4.3.0]", "System.Net.Security": "(, 4.3.2]", "System.Net.Sockets": "(, 4.3.0]", "System.Net.WebHeaderCollection": "(, 4.3.0]", "System.Net.WebSockets": "(, 4.3.0]", "System.Net.WebSockets.Client": "(, 4.3.2]", "System.Numerics.Vectors": "(, 5.0.0]", "System.ObjectModel": "(, 4.3.0]", "System.Private.DataContractSerialization": "(, 4.3.0]", "System.Private.Uri": "(, 4.3.2]", "System.Reflection": "(, 4.3.0]", "System.Reflection.DispatchProxy": "(, 6.0.0]", "System.Reflection.Emit": "(, 4.7.0]", "System.Reflection.Emit.ILGeneration": "(, 4.7.0]", "System.Reflection.Emit.Lightweight": "(, 4.7.0]", "System.Reflection.Extensions": "(, 4.3.0]", "System.Reflection.Metadata": "(, 10.0.0]", "System.Reflection.Primitives": "(, 4.3.0]", "System.Reflection.TypeExtensions": "(, 4.3.0]", "System.Resources.Reader": "(, 4.3.0]", "System.Resources.ResourceManager": "(, 4.3.0]", "System.Resources.Writer": "(, 4.3.0]", "System.Runtime": "(, 4.3.1]", "System.Runtime.CompilerServices.Unsafe": "(, 7.0.0]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.0]", "System.Runtime.Extensions": "(, 4.3.1]", "System.Runtime.Handles": "(, 4.3.0]", "System.Runtime.InteropServices": "(, 4.3.0]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.0]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.0]", "System.Runtime.Loader": "(, 4.3.0]", "System.Runtime.Numerics": "(, 4.3.0]", "System.Runtime.Serialization.Formatters": "(, 4.3.0]", "System.Runtime.Serialization.Json": "(, 4.3.0]", "System.Runtime.Serialization.Primitives": "(, 4.3.0]", "System.Runtime.Serialization.Xml": "(, 4.3.0]", "System.Runtime.WindowsRuntime": "(, 4.7.0]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.0]", "System.Security.AccessControl": "(, 6.0.1]", "System.Security.Claims": "(, 4.3.0]", "System.Security.Cryptography.Algorithms": "(, 4.3.1]", "System.Security.Cryptography.Cng": "(, 5.0.0]", "System.Security.Cryptography.Csp": "(, 4.3.0]", "System.Security.Cryptography.Encoding": "(, 4.3.0]", "System.Security.Cryptography.OpenSsl": "(, 5.0.0]", "System.Security.Cryptography.Primitives": "(, 4.3.0]", "System.Security.Cryptography.X509Certificates": "(, 4.3.2]", "System.Security.Cryptography.Xml": "(, 4.4.0]", "System.Security.Principal": "(, 4.3.0]", "System.Security.Principal.Windows": "(, 5.0.0]", "System.Security.SecureString": "(, 4.3.0]", "System.Text.Encoding": "(, 4.3.0]", "System.Text.Encoding.CodePages": "(, 10.0.0]", "System.Text.Encoding.Extensions": "(, 4.3.0]", "System.Text.Encodings.Web": "(, 10.0.0]", "System.Text.Json": "(, 10.0.0]", "System.Text.RegularExpressions": "(, 4.3.1]", "System.Threading": "(, 4.3.0]", "System.Threading.Channels": "(, 10.0.0]", "System.Threading.Overlapped": "(, 4.3.0]", "System.Threading.Tasks": "(, 4.3.0]", "System.Threading.Tasks.Dataflow": "(, 10.0.0]", "System.Threading.Tasks.Extensions": "(, 5.0.0]", "System.Threading.Tasks.Parallel": "(, 4.3.0]", "System.Threading.Thread": "(, 4.3.0]", "System.Threading.ThreadPool": "(, 4.3.0]", "System.Threading.Timer": "(, 4.3.0]", "System.ValueTuple": "(, 4.5.0]", "System.Xml.ReaderWriter": "(, 4.3.1]", "System.Xml.XDocument": "(, 4.3.0]", "System.Xml.XmlDocument": "(, 4.3.0]", "System.Xml.XmlSerializer": "(, 4.3.0]", "System.Xml.XPath": "(, 4.3.0]", "System.Xml.XPath.XDocument": "(, 5.0.0]"}}}}}