﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>9b77aedb-b566-4a4e-b85a-5155c2a252a6</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <DockerfileContext>..\..</DockerfileContext>
    <DockerComposeProjectPath>..\..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>



  <ItemGroup>
    <PackageReference Include="Aspire.Seq" Version="9.2.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="Microsoft.DependencyValidation.Analyzers" Version="0.11.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1-Preview.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Modules\" />	  
  </ItemGroup>

	<PropertyGroup>
		<!-- 禁用默认内容项，避免冲突 -->
		<EnableDefaultContentItems>false</EnableDefaultContentItems>
	</PropertyGroup>

	<ItemGroup>
		<!-- 拷贝整个 Modules 文件夹到输出目录 -->
		<Content Include="Modules\**\*">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
	  <None Remove="modulesettings.json" />
	</ItemGroup>
	<ItemGroup>
	  <Content Include="$(NuGetPackageRoot)\ssic.infrastructure\2.0.0\contentFiles\any\any\appsettings.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Include="$(NuGetPackageRoot)\ssic.infrastructure\2.0.0\contentFiles\any\any\Corssettings.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Include="$(NuGetPackageRoot)\ssic.infrastructure\2.0.0\contentFiles\any\any\DbInfosettings.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Include="modulesettings.json">
	    <PackagePath>contentFiles\any\any\</PackagePath>
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <Pack>true</Pack>
	  </Content>
  </ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\SSIC.Infrastructure\SSIC.Infrastructure.csproj" />
	</ItemGroup>

</Project>
