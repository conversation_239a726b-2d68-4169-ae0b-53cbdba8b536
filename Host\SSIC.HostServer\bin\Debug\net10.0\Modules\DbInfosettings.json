{
  "DbInfo": {
    "dataservice": "192.168.1.79", //数据库服务器地址
    "datatype": 2, //数据库类型 SqlServer = 1,PostgreSQL = 2,Oracle = 3,Sqlite = 4,Dameng = 12,OdbcKingbaseES = 13,ShenTong = 14,KingbaseES = 15,Firebird = 16
    "dataname": "ss_auth", //数据库名称
    "datauid": "saas", //数据库用户id
    "datapwd": "sa!220210", //数据库用户密码
    "saasmodel": false, //是否启用租户模式
    "dataport": 5432, //数据库端口
    "orgmodel": false //是否启用组织模式
  }
}