﻿using Microsoft.AspNetCore.Http;
using SSIC.Infrastructure.OptionsEntity;
using System.Linq;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Orm
{
    public class ContextMiddleware
    {
        private readonly RequestDelegate _next;
        private DbInfoOptions _dbInfoOptions;
        private IFreeSql _fsql;

        public ContextMiddleware(RequestDelegate next, DbInfoOptions options, IFreeSql fsql)
        {
            _next = next;
            _dbInfoOptions = options;
            _fsql = fsql;
        }

        public async Task Invoke(HttpContext context)
        {
            //根据HostUrl设置调用租户连接
            var host = context.Request.Host.Host; // 获取完整的域名地址（包括 scheme 和 port）
            var FindTenantBase = _dbInfoOptions.tenantLists.FirstOrDefault(n => n.Host.Contains(host));
            if (FindTenantBase != null)
            {
                _fsql.Change(FindTenantBase.Tenantid);
            }
            try { await _next(context); }
            finally { _fsql.Change("DbMaster"); }
        }
    }
}