﻿using SSIC.Infrastructure.ConfigurableOptions;
using SSIC.Infrastructure.Startups.HotReload;
using System.Collections.Generic;

namespace SSIC.Infrastructure.OptionsEntity
{
    /// <summary>
    /// 模块路径配置选项
    /// </summary>
    public class ModulePathOptions : IConfigurableOptions
    {
        /// <summary>
        /// 是否启用自定义模块路径功能
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 开发环境基础路径
        /// </summary>
        public string DevelopmentBasePath { get; set; } = string.Empty;

        /// <summary>
        /// 生产环境基础路径
        /// </summary>
        public string ProductionBasePath { get; set; } = string.Empty;

        /// <summary>
        /// 模块相对路径
        /// </summary>
        public string ModulePath { get; set; } = "Modules";

        /// <summary>
        /// 是否扫描子文件夹
        /// </summary>
        public bool ScanSubFolders { get; set; } = true;

        /// <summary>
        /// 模块目录前缀
        /// </summary>
        public string ModuleDirectoryPrefix { get; set; } = "SSIC.Modules.";

        /// <summary>
        /// 扫描模式（0=ByPrefix, 1=ByDirectory, 2=ByPattern, 3=ByWildcardPattern, 4=AllDlls）
        /// </summary>
        public int ScanMode { get; set; } = 3;

        /// <summary>
        /// 通配符模式列表
        /// </summary>
        public List<string> WildcardPatterns { get; set; } = new List<string> { "SSIC.Modules.*.dll" };

        /// <summary>
        /// 搜索选项
        /// </summary>
        public string SearchOption { get; set; } = "TopDirectoryOnly";

        /// <summary>
        /// 获取当前环境的基础路径
        /// </summary>
        /// <param name="isDevelopment">是否为开发环境</param>
        /// <returns>基础路径</returns>
        public string GetBasePath(bool isDevelopment)
        {
            // 如果功能未启用，直接返回应用程序基础目录
            if (!Enabled)
            {
                return AppContext.BaseDirectory;
            }

            if (isDevelopment && !string.IsNullOrEmpty(DevelopmentBasePath))
            {
                return DevelopmentBasePath;
            }
            
            if (!isDevelopment && !string.IsNullOrEmpty(ProductionBasePath))
            {
                return ProductionBasePath;
            }

            // 如果没有配置，则使用应用程序基础目录
            return AppContext.BaseDirectory;
        }

        /// <summary>
        /// 获取完整的模块目录路径
        /// </summary>
        /// <param name="isDevelopment">是否为开发环境</param>
        /// <returns>模块目录路径</returns>
        public string GetModuleDirectory(bool isDevelopment)
        {
            var basePath = GetBasePath(isDevelopment);
            return Path.Combine(basePath, ModulePath);
        }

        /// <summary>
        /// 获取扫描模式枚举值
        /// </summary>
        /// <returns>扫描模式枚举</returns>
        public ModuleScanMode GetScanMode()
        {
            return ScanMode switch
            {
                0 => ModuleScanMode.ByPrefix,
                1 => ModuleScanMode.ByDirectory,
                2 => ModuleScanMode.ByPattern,
                3 => ModuleScanMode.ByWildcardPattern,
                4 => ModuleScanMode.AllDlls,
                _ => ModuleScanMode.ByWildcardPattern
            };
        }

        /// <summary>
        /// 获取搜索选项枚举值
        /// </summary>
        /// <returns>搜索选项枚举</returns>
        public SearchOption GetSearchOption()
        {
            return SearchOption switch
            {
                "TopDirectoryOnly" => System.IO.SearchOption.TopDirectoryOnly,
                "AllDirectories" => System.IO.SearchOption.AllDirectories,
                _ => System.IO.SearchOption.TopDirectoryOnly
            };
        }

        /// <summary>
        /// 获取模块扫描路径列表
        /// </summary>
        /// <param name="isDevelopment">是否为开发环境</param>
        /// <returns>模块扫描路径列表</returns>
        public List<string> GetModuleSubFolders(bool isDevelopment)
        {
            var modulePath = GetModuleDirectory(isDevelopment);

            if (!Directory.Exists(modulePath))
            {
                return new List<string>();
            }

            if (!ScanSubFolders)
            {
                // 当 ScanSubFolders = false 时：
                // 直接在模块基础路径（basePath/Modules）中扫描符合通配符规则的 DLL 文件
                // 不进入子目录，只扫描根目录级别的文件
                return new List<string> { modulePath };
            }
            else
            {
                // 当 ScanSubFolders = true 时：
                // 先扫描模块基础路径下符合 ModuleDirectoryPrefix 前缀的子文件夹
                // 然后在这些子文件夹中根据 WildcardPatterns 通配符规则扫描 DLL 文件
                // 使用 SearchOption 配置控制递归深度
                return Directory.GetDirectories(modulePath, "*", GetSearchOption())
                               .Where(dir => new DirectoryInfo(dir).Name.StartsWith(ModuleDirectoryPrefix))
                               .ToList();
            }
        }
    }
}
