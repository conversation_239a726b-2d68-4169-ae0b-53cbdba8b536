﻿using FreeSql;
using FreeSql.DataAnnotations;
using Mapster;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using SSIC.Infrastructure.Enums;
using SSIC.Infrastructure.OptionsEntity;
using SSIC.Infrastructure.Entity;
using SSIC.Infrastructure.Entity.TenantEntity;
using StackExchange.Profiling;
using System;
using System.Collections.Generic;
using System.Reflection;
using SSIC.Infrastructure.Startups;
using SSIC.Infrastructure.ConfigurableOptions.Realization;

namespace SSIC.Infrastructure.Orm

{
    [Startup(500)]
    public class FreeSqlContexts : IStartups
    {
        public void ConfigureServices(IServiceCollection services)
        {
            var fsql = new MultiFreeSql();
            var Dboption = services.GetOptions<DbInfoOptions>();
            //默认加载DbSettings.json数据库配置
            fsql.Register("DBMaster",
                () =>
                {
                    var fsql = new FreeSqlBuilder()
                        .UseConnectionString(Dboption.datatype, Dboption.ComposeUrl)
                        .UseAutoSyncStructure(false)
                        .Build();
                    //fsql.CodeFirst.SyncStructure(GetTypesByTableAttribute());
                    //是否启用租户过滤器
                    if (Dboption.saasmodel)
                    {
                        fsql.GlobalFilter.ApplyIf<EntityBasic>("Tenant_Master", () => !string.IsNullOrWhiteSpace(Dboption.tenantid), a => a.tenantid == Guid.Parse(Dboption.tenantid));
                    }
                    SqlCommandLog(fsql);
                    return fsql;
                }
                );
            //是否启用租户管理
            if (Dboption.saasmodel)
            {
                // 获取租户配置表
                var LoadTenantList = fsql.Select<TenantInfo>().ToList();
                //批量注册租户配置表
                foreach (var item in LoadTenantList)
                {
                    if (!string.IsNullOrWhiteSpace(item.dataservice))
                    {
                        var SetDbInfoOptions = item.Adapt<DbInfoOptions>();
                        Dboption.tenantLists.Add(new TenantList
                        {
                            Tenantid = item.id.ToString(),
                            Tenattype = (TenatType)item.tenanttype,
                            Host = item.host
                        });

                        fsql.Register(item.id.ToString(),
                            () =>
                            {
                                var fsql = new FreeSqlBuilder()
                                    .UseConnectionString(SetDbInfoOptions.datatype, SetDbInfoOptions.ComposeUrl).Build();
                                if (item.tenanttype == 1)
                                {
                                    //当同库同表时如何做处理

                                    fsql.GlobalFilter.ApplyIf<EntityBasic>("Tenant" + item.id, () => item.id != Guid.Empty, a => a.tenantid == item.id);
                                }
                                SqlCommandLog(fsql);
                                return fsql;
                            }
                        );
                    }
                }
            }
            services.AddSingleton<IFreeSql>(fsql);
            services.AddSingleton(Dboption);
        }

        /// <summary>
        /// 监视数据库语句
        /// </summary>
        /// <param name="freeSql"></param>
        private void SqlCommandLog(IFreeSql freeSql)
        {
            freeSql.SetDbContextOptions(opt =>
            {
                opt.OnEntityChange = report => { };
            });
            freeSql.Aop.CurdBefore += (s, e) =>
            {
                //MiniProfiler和Swagger配合
                using (MiniProfiler.Current.Step("Sql执行前跟踪"))
                {
                    MiniProfiler.Current.CustomTiming("操作类型", e.CurdType.ToString());
                    MiniProfiler.Current.CustomTiming("操作语句", e.Sql);
                }
                //导出控制面板

                Log.Information($"执行前==>操作类型:{e.CurdType.ToString()};操作语句:{e.Sql}");
            };
            freeSql.Aop.CurdAfter += (s, e) => { };
            freeSql.Aop.CommandAfter += (s, e) =>
            {
                using (MiniProfiler.Current.Step("Sql执行跟踪"))
                {
                    //  MiniProfiler.Current.CustomTiming("执行耗时", e.ElapsedMilliseconds.ToString());
                    MiniProfiler.Current.CustomTiming("执行结果", e.Log);
                }
                //导出控制面板
                Log.Information($"执行后==>:{e.Log}");
            };
            //freeSql.Aop.SyncStructureAfter += (s, e) => {
            //    var ss = GetTypesByHasDataAttribute();
            //    var a = e.EntityTypes;
            //    foreach (var et in e.EntityTypes) {
            //        if (et != typeof(EntityHasData)) continue;
            //        if (freeSql.Select<object>().AsType(et).Any()) continue;

            //        var repo = freeSql.GetRepository<object>();
            //        repo.DbContextOptions.EnableCascadeSave = true;
            //        repo.DbContextOptions.NoneParameter = true;
            //        repo.AsType(et);
            //        //repo.Insert(sd);
            //    }
            //};
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.UseContextMiddleware(); //引入自定义的HtppContextMiddleware中间件
        }

        /// <summary>
        /// 扫描 IEntity类所在程序集，反射得到类上有特性标签为TableAttribute 的所有类，该方法需在实体类上指定了 [Table(Name = "xxx")]特性标签
        /// </summary>
        /// <returns></returns>
        public static Type[] GetTypesByTableAttribute()
        {
            List<Type> tableAssembies = new List<Type>();
            foreach (Type type in Assembly.GetAssembly(typeof(EntityTableBase)).GetExportedTypes())
                foreach (Attribute attribute in type.GetCustomAttributes())
                    if (attribute is TableAttribute tableAttribute)
                        if (tableAttribute.DisableSyncStructure == false)
                            tableAssembies.Add(type);
            return tableAssembies.ToArray();
        }
        /// <summary>
        /// 扫描种子数据
        /// </summary>
        /// <returns></returns>
        //public static Type[] GetTypesByHasDataAttribute()
        //{
        //    List<Type> tableAssembies = new List<Type>();
        //    foreach (Type type in Assembly.GetAssembly(typeof(EntityHasData)).GetExportedTypes())
        //        foreach (Attribute attribute in type.GetCustomAttributes())
        //            if (attribute is TableAttribute tableAttribute)
        //                if (tableAttribute.DisableSyncStructure == false)
        //                    tableAssembies.Add(type);
        //    return tableAssembies.ToArray();
        //}

    }
}