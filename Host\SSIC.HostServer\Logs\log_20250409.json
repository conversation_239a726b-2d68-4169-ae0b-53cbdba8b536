{"@t":"2025-04-09T10:30:25.2841107Z","@mt":"Now listening on: {address}","address":"https://localhost:63095","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:25.3359749Z","@mt":"Now listening on: {address}","address":"http://localhost:63096","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:25.4533086Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:25.4539355Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:25.4541428Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:30.0552541Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":47.9553,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:45.1327067Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":8.3266,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:46.0941782Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-09\",\"temperatureC\":27,\"temperatureF\":80,\"summary\":\"Warm\",\"test\":\"123\"}条","@tr":"32ac7be52788d4894b01b3aa08bcc94c","@sp":"c4b65967a4152140","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"c4b65967a4152140","TraceId":"32ac7be52788d4894b01b3aa08bcc94c","ParentId":"0000000000000000","ConnectionId":"0HNBNA6560CN5","RequestId":"0HNBNA6560CN5:00000001","RequestPath":"/WeatherForecast2","ActionId":"3cbcfc6a-b7fc-49f8-9afc-91ff715bb479","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:46.0953506Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-09\",\"temperatureC\":27,\"temperatureF\":80,\"summary\":\"Warm\",\"test\":\"123\"}条","@l":"Error","@tr":"32ac7be52788d4894b01b3aa08bcc94c","@sp":"c4b65967a4152140","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"c4b65967a4152140","TraceId":"32ac7be52788d4894b01b3aa08bcc94c","ParentId":"0000000000000000","ConnectionId":"0HNBNA6560CN5","RequestId":"0HNBNA6560CN5:00000001","RequestPath":"/WeatherForecast2","ActionId":"3cbcfc6a-b7fc-49f8-9afc-91ff715bb479","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:47.2367446Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-09\",\"temperatureC\":36,\"temperatureF\":96,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@tr":"3b9a2d88a14fb5f0fee4a3ddfab28a76","@sp":"1e85c29df44f8684","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"1e85c29df44f8684","TraceId":"3b9a2d88a14fb5f0fee4a3ddfab28a76","ParentId":"0000000000000000","ConnectionId":"0HNBNA6560CN5","RequestId":"0HNBNA6560CN5:00000003","RequestPath":"/WeatherForecast2","ActionId":"3cbcfc6a-b7fc-49f8-9afc-91ff715bb479","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:47.2369995Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-09\",\"temperatureC\":36,\"temperatureF\":96,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@l":"Error","@tr":"3b9a2d88a14fb5f0fee4a3ddfab28a76","@sp":"1e85c29df44f8684","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"1e85c29df44f8684","TraceId":"3b9a2d88a14fb5f0fee4a3ddfab28a76","ParentId":"0000000000000000","ConnectionId":"0HNBNA6560CN5","RequestId":"0HNBNA6560CN5:00000003","RequestPath":"/WeatherForecast2","ActionId":"3cbcfc6a-b7fc-49f8-9afc-91ff715bb479","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:50.1968609Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":46.7563,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
