﻿using Dapr;
using Dapr.Client;
using Google.Api;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SSIC.HostServer;
using SSIC.Infrastructure.Startups;
using SSIC.Infrastructure.Startups.HotReload;
using System.IO;
using System.Reflection;

try
{
    Console.WriteLine("开始启动应用程序...");
    var builder = WebApplication.CreateBuilder(args);
    Console.WriteLine("WebApplication.CreateBuilder 完成");

    var app = builder.AddHotReload(true).AddStartup();
    Console.WriteLine("AddHotReload 和 AddStartup 完成");

    Console.WriteLine("开始运行应用程序...");
    app.Run();
}
catch (Exception ex)
{
    Console.WriteLine($"启动应用程序时发生错误: {ex.Message}");
    Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
    throw;
}
