using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using System.Reflection;

namespace SSIC.Infrastructure.Startups.Endpoints
{
    /// <summary>
    /// 端点刷新中间件，在第一次请求时强制刷新路由系统
    /// </summary>
    public class EndpointRefreshMiddleware
    {
        private readonly RequestDelegate _next;
        private static bool _refreshed = false;
        private static readonly object _lock = new object();
        private readonly ILogger<EndpointRefreshMiddleware> _logger;

        public EndpointRefreshMiddleware(RequestDelegate next, ILogger<EndpointRefreshMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // 确保只执行一次刷新
            if (!_refreshed)
            {
                lock (_lock)
                {
                    if (!_refreshed)
                    {
                        try
                        {
                            _logger.LogInformation("首次请求触发端点刷新");
                            
                            // 获取已加载的模块程序集
                            var loadedAssemblies = AppDomain.CurrentDomain.GetAssemblies()
                                .Where(a => a.GetName().Name?.StartsWith("SSIC.Modules.") == true)
                                .ToList();
                            
                            if (loadedAssemblies.Any())
                            {
                                // 获取端点管理器
                                var endpointManager = DynamicEndpointManager.Instance;
                                
                                // 先重建端点
                                endpointManager.RebuildEndpoints(context.RequestServices, loadedAssemblies);
                                
                                // 再刷新端点
                                endpointManager.RefreshEndpoints(context.RequestServices, loadedAssemblies);
                                
                                _logger.LogInformation("请求触发的端点刷新完成，发现 {Count} 个模块", loadedAssemblies.Count);
                            }
                            else
                            {
                                _logger.LogWarning("未找到任何模块程序集，跳过端点刷新");
                            }
                            
                            _refreshed = true;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "请求触发的端点刷新失败");
                        }
                    }
                }
            }
            
            await _next(context);
        }
    }

    /// <summary>
    /// 中间件扩展方法
    /// </summary>
    public static class EndpointRefreshMiddlewareExtensions
    {
        public static IApplicationBuilder UseEndpointRefresh(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<EndpointRefreshMiddleware>();
        }
    }
} 