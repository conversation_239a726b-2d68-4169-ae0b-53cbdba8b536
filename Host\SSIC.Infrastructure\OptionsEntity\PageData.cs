﻿using FreeSql.Internal.Model;

namespace SSIC.Infrastructure.OptionsEntity
{
    /// <summary>
    /// 分页数据
    /// </summary>
    public class PageData
    {
        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 行数
        /// </summary>
        public int Rows { get; set; } = 20;

        /// <summary>
        /// 总条数
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 表格名称
        /// </summary>
        public string TableName { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        public string Sort { get; set; }

        /// <summary>
        /// 排序方式
        /// </summary>
        public string Order { get; set; }

        /// <summary>
        /// 条件
        /// </summary>
        public DynamicFilterInfo Wheres { get; set; }

        /// <summary>
        /// 是否导出excel
        /// </summary>
        public bool Export { get; set; }

        /// <summary>
        /// 自定义条件
        /// </summary>
        public object Value { get; set; }
    }
}