﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>SSIC.Infrastructure</id>
    <version>1.0.0</version>
    <authors>SSIC.Infrastructure</authors>
    <description>Package Description</description>
    <repository type="git" commit="0af9b0b8e9d5bc2dee3b5e9c904a540fb6a3b1c0" />
    <dependencies>
      <group targetFramework="net10.0">
        <dependency id="Dapr.AspNetCore" version="1.15.3" exclude="Build,Analyzers" />
        <dependency id="FreeRedis" version="1.3.6" exclude="Build,Analyzers" />
        <dependency id="FreeScheduler" version="2.0.33" exclude="Build,Analyzers" />
        <dependency id="FreeSql.All" version="3.5.202" exclude="Build,Analyzers" />
        <dependency id="FreeSql.Cloud" version="2.0.0" exclude="Build,Analyzers" />
        <dependency id="Mapster" version="7.4.2-pre02" exclude="Build,Analyzers" />
        <dependency id="Masa.Contrib.Development.DaprStarter.AspNetCore" version="1.2.0-preview.6" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Authentication.JwtBearer" version="10.0.0-preview.2.25164.1" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.OpenApi" version="9.0.4" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Data.SqlClient" version="6.0.1" exclude="Build,Analyzers" />
        <dependency id="Microsoft.DependencyValidation.Analyzers" version="0.11.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Configuration" version="10.0.0-preview.2.25163.2" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Configuration.Json" version="10.0.0-preview.2.25163.2" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection" version="10.0.0-preview.2.25163.2" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Hosting" version="10.0.0-preview.2.25163.2" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Http.Resilience" version="9.4.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.ServiceDiscovery" version="9.1.0" exclude="Build,Analyzers" />
        <dependency id="MiniProfiler.AspNetCore" version="4.5.4" exclude="Build,Analyzers" />
        <dependency id="MiniProfiler.AspNetCore.Mvc" version="4.5.4" exclude="Build,Analyzers" />
        <dependency id="Npgsql" version="9.0.3" exclude="Build,Analyzers" />
        <dependency id="OpenTelemetry" version="1.11.2" exclude="Build,Analyzers" />
        <dependency id="OpenTelemetry.Exporter.OpenTelemetryProtocol" version="1.11.2" exclude="Build,Analyzers" />
        <dependency id="OpenTelemetry.Exporter.Zipkin" version="1.11.2" exclude="Build,Analyzers" />
        <dependency id="OpenTelemetry.Extensions.Hosting" version="1.11.2" exclude="Build,Analyzers" />
        <dependency id="OpenTelemetry.Instrumentation.AspNetCore" version="1.11.1" exclude="Build,Analyzers" />
        <dependency id="OpenTelemetry.Instrumentation.Http" version="1.11.1" exclude="Build,Analyzers" />
        <dependency id="OpenTelemetry.Instrumentation.Runtime" version="1.11.1" exclude="Build,Analyzers" />
        <dependency id="SSIC.Utilities" version="1.0.1" exclude="Build,Analyzers" />
        <dependency id="Scalar.AspNetCore" version="2.1.8" exclude="Build,Analyzers" />
        <dependency id="Serilog" version="4.2.1-dev-02352" exclude="Build,Analyzers" />
        <dependency id="Serilog.AspNetCore" version="9.0.0" exclude="Build,Analyzers" />
        <dependency id="Serilog.Enrichers.Environment" version="3.0.1" exclude="Build,Analyzers" />
        <dependency id="Serilog.Enrichers.Thread" version="4.0.0" exclude="Build,Analyzers" />
        <dependency id="Serilog.Expressions" version="5.0.0" exclude="Build,Analyzers" />
        <dependency id="Serilog.Extensions.Hosting" version="9.0.0" exclude="Build,Analyzers" />
        <dependency id="Serilog.Extensions.Logging" version="9.0.1" exclude="Build,Analyzers" />
        <dependency id="Serilog.Settings.Configuration" version="9.0.1-dev-02317" exclude="Build,Analyzers" />
        <dependency id="Serilog.Sinks.Console" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Serilog.Sinks.Seq" version="9.0.0" exclude="Build,Analyzers" />
        <dependency id="SixLabors.ImageSharp" version="3.1.7" exclude="Build,Analyzers" />
        <dependency id="Swashbuckle.AspNetCore" version="8.1.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <contentFiles>
      <files include="any/any/appsettings.json" buildAction="Content" />
      <files include="any/any/Corssettings.json" buildAction="Content" />
      <files include="any/any/DbInfosettings.json" buildAction="Content" />
    </contentFiles>
  </metadata>
  <files>
    <file src="D:\Saisi\SSIC_Core\SSIC.Infrastructure\bin\Debug\net10.0\SSIC.Infrastructure.dll" target="lib\net10.0\SSIC.Infrastructure.dll" />
    <file src="D:\Saisi\SSIC_Core\SSIC.Infrastructure\bin\Debug\net10.0\SSIC.Infrastructure.xml" target="lib\net10.0\SSIC.Infrastructure.xml" />
    <file src="D:\Saisi\SSIC_Core\SSIC.Infrastructure\appsettings.json" target="contentFiles\any\any\appsettings.json" />
    <file src="D:\Saisi\SSIC_Core\SSIC.Infrastructure\Corssettings.json" target="contentFiles\any\any\Corssettings.json" />
    <file src="D:\Saisi\SSIC_Core\SSIC.Infrastructure\DbInfosettings.json" target="contentFiles\any\any\DbInfosettings.json" />
  </files>
</package>