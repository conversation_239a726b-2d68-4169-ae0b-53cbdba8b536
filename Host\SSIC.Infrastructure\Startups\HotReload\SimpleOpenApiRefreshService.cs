using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Reflection;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.HotReload
{
    /// <summary>
    /// 简化的OpenAPI文档刷新服务
    /// </summary>
    public interface ISimpleOpenApiRefreshService
    {
        /// <summary>
        /// 刷新OpenAPI相关组件
        /// </summary>
        Task RefreshAsync();
    }

    /// <summary>
    /// 简化的OpenAPI文档刷新服务实现
    /// </summary>
    public class SimpleOpenApiRefreshService : ISimpleOpenApiRefreshService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<SimpleOpenApiRefreshService> _logger;

        public SimpleOpenApiRefreshService(
            IServiceProvider serviceProvider,
            ILogger<SimpleOpenApiRefreshService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        /// <summary>
        /// 刷新OpenAPI相关组件
        /// </summary>
        public async Task RefreshAsync()
        {
            try
            {
                _logger.LogInformation("开始刷新OpenAPI相关组件...");

                // 1. 刷新ActionDescriptorCollectionProvider
                await RefreshActionDescriptors();

                // 2. 通知系统重新扫描控制器
                await NotifyControllerChange();

                _logger.LogInformation("OpenAPI组件刷新完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新OpenAPI组件时出错");
                throw;
            }
        }

        /// <summary>
        /// 刷新ActionDescriptor集合
        /// </summary>
        private async Task RefreshActionDescriptors()
        {
            try
            {
                var actionDescriptorProvider = _serviceProvider.GetService<IActionDescriptorCollectionProvider>();
                if (actionDescriptorProvider != null)
                {
                    // 方法1：尝试通过反射调用Update方法
                    if (actionDescriptorProvider is ActionDescriptorCollectionProvider provider)
                    {
                        var updateMethod = provider.GetType().GetMethod("Update", BindingFlags.NonPublic | BindingFlags.Instance);
                        if (updateMethod != null)
                        {
                            updateMethod.Invoke(provider, null);
                            _logger.LogDebug("已通过反射刷新ActionDescriptorCollectionProvider");
                        }
                    }

                    // 方法2：尝试触发变更通知
                    var changeTokenMethod = actionDescriptorProvider.GetType().GetMethod("GetChangeToken", BindingFlags.Public | BindingFlags.Instance);
                    if (changeTokenMethod != null)
                    {
                        var changeToken = changeTokenMethod.Invoke(actionDescriptorProvider, null);
                        _logger.LogDebug("已获取ActionDescriptor变更令牌");
                    }

                    _logger.LogInformation("ActionDescriptor刷新完成");
                }
                else
                {
                    _logger.LogWarning("未找到IActionDescriptorCollectionProvider服务");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新ActionDescriptor时出错");
            }
        }

        /// <summary>
        /// 通知控制器变更
        /// </summary>
        private async Task NotifyControllerChange()
        {
            try
            {
                // 尝试获取ApplicationPartManager并触发刷新
                var partManager = _serviceProvider.GetService<Microsoft.AspNetCore.Mvc.ApplicationParts.ApplicationPartManager>();
                if (partManager != null)
                {
                    // 触发ApplicationParts的变更通知
                    var changeTokenProperty = partManager.GetType().GetProperty("ChangeToken", BindingFlags.Public | BindingFlags.Instance);
                    if (changeTokenProperty != null)
                    {
                        var changeToken = changeTokenProperty.GetValue(partManager);
                        _logger.LogDebug("已获取ApplicationPartManager变更令牌");
                    }

                    _logger.LogInformation("已通知控制器变更");
                }
                else
                {
                    _logger.LogDebug("未找到ApplicationPartManager服务");
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "通知控制器变更时出现异常（可忽略）");
            }
        }
    }
}
