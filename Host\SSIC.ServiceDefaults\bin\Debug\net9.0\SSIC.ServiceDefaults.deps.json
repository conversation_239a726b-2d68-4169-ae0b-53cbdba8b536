{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"SSIC.ServiceDefaults/1.0.0": {"dependencies": {"Microsoft.Extensions.Http.Resilience": "9.0.0", "Microsoft.Extensions.ServiceDiscovery": "9.0.0", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.9.0", "OpenTelemetry.Extensions.Hosting": "1.9.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.9.0", "OpenTelemetry.Instrumentation.Http": "1.9.0", "OpenTelemetry.Instrumentation.Runtime": "1.9.0"}, "runtime": {"SSIC.ServiceDefaults.dll": {}}}, "Google.Protobuf/3.22.5": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Grpc.Core.Api/2.52.0": {"runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Grpc.Net.Client/2.52.0": {"dependencies": {"Grpc.Net.Common": "2.52.0"}, "runtime": {"lib/net7.0/Grpc.Net.Client.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Grpc.Net.Common/2.52.0": {"dependencies": {"Grpc.Core.Api": "2.52.0"}, "runtime": {"lib/net7.0/Grpc.Net.Common.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Microsoft.Extensions.AmbientMetadata.Application/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.55605"}}}, "Microsoft.Extensions.Compliance.Abstractions/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.55605"}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.55605"}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.55605"}}}, "Microsoft.Extensions.Http.Diagnostics/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "9.0.0", "Microsoft.Extensions.Telemetry": "9.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.55605"}}}, "Microsoft.Extensions.Http.Resilience/9.0.0": {"dependencies": {"Microsoft.Extensions.Http.Diagnostics": "9.0.0", "Microsoft.Extensions.Resilience": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.55605"}}}, "Microsoft.Extensions.Resilience/9.0.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.0.0", "Microsoft.Extensions.Telemetry.Abstractions": "9.0.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.55605"}}}, "Microsoft.Extensions.ServiceDiscovery/9.0.0": {"dependencies": {"Microsoft.Extensions.ServiceDiscovery.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.55103"}}}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.55103"}}}, "Microsoft.Extensions.Telemetry/9.0.0": {"dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.0.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.0.0", "Microsoft.Extensions.Telemetry.Abstractions": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.55605"}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.55605"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}}, "OpenTelemetry/1.9.0": {"dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.9.0"}, "runtime": {"lib/net8.0/OpenTelemetry.dll": {"assemblyVersion": "*******", "fileVersion": "1.9.0.1312"}}}, "OpenTelemetry.Api/1.9.0": {"runtime": {"lib/net8.0/OpenTelemetry.Api.dll": {"assemblyVersion": "*******", "fileVersion": "1.9.0.1312"}}}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.9.0": {"dependencies": {"OpenTelemetry.Api": "1.9.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "1.9.0.1312"}}}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.9.0": {"dependencies": {"Google.Protobuf": "3.22.5", "Grpc.Net.Client": "2.52.0", "OpenTelemetry": "1.9.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll": {"assemblyVersion": "*******", "fileVersion": "1.9.0.1312"}}}, "OpenTelemetry.Extensions.Hosting/1.9.0": {"dependencies": {"OpenTelemetry": "1.9.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "1.9.0.1312"}}}, "OpenTelemetry.Instrumentation.AspNetCore/1.9.0": {"dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.9.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "OpenTelemetry.Instrumentation.Http/1.9.0": {"dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.9.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Http.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "OpenTelemetry.Instrumentation.Runtime/1.9.0": {"dependencies": {"OpenTelemetry.Api": "1.9.0"}, "runtime": {"lib/net6.0/OpenTelemetry.Instrumentation.Runtime.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Polly.Core/8.4.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.Extensions/8.4.2": {"dependencies": {"Polly.Core": "8.4.2"}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.RateLimiting/8.4.2": {"dependencies": {"Polly.Core": "8.4.2"}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}}}, "libraries": {"SSIC.ServiceDefaults/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Google.Protobuf/3.22.5": {"type": "package", "serviceable": true, "sha512": "sha512-tTMtDZPbLxJew8pk7NBdqhLqC4OipfkZdwPuCEUNr2AoDo1siUGcxFqJK0wDewTL8ge5Cjrb16CToMPxBUHMGA==", "path": "google.protobuf/3.22.5", "hashPath": "google.protobuf.3.22.5.nupkg.sha512"}, "Grpc.Core.Api/2.52.0": {"type": "package", "serviceable": true, "sha512": "sha512-SQiPyBczG4vKPmI6Fd+O58GcxxDSFr6nfRAJuBDUNj+PgdokhjWJvZE/La1c09AkL2FVm/jrDloG89nkzmVF7A==", "path": "grpc.core.api/2.52.0", "hashPath": "grpc.core.api.2.52.0.nupkg.sha512"}, "Grpc.Net.Client/2.52.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWVH9g/Nnjz40ni//2S8UIOyEmhueQREoZIkD0zKHEPqLxXcNlbp4eebXIOicZtkwDSx0TFz9NpkbecEDn6rBw==", "path": "grpc.net.client/2.52.0", "hashPath": "grpc.net.client.2.52.0.nupkg.sha512"}, "Grpc.Net.Common/2.52.0": {"type": "package", "serviceable": true, "sha512": "sha512-di9qzpdx525IxumZdYmu6sG2y/gXJyYeZ1ruFUzB9BJ1nj4kU1/dTAioNCMt1VLRvNVDqh8S8B1oBdKhHJ4xRg==", "path": "grpc.net.common/2.52.0", "hashPath": "grpc.net.common.2.52.0.nupkg.sha512"}, "Microsoft.Extensions.AmbientMetadata.Application/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-F/pe4t8UXc65l0KFLoUDtYttoLI0qtXiWQ4HStw3Cq2ExaNARxS4isAvA6w0PNCyIs8dLxkaruJHeyKN8mK1lA==", "path": "microsoft.extensions.ambientmetadata.application/9.0.0", "hashPath": "microsoft.extensions.ambientmetadata.application.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Compliance.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y85iXLdg55OYwJR23KvCYAPDaIa+723vus9WzLROVWMy0sblHlxVtPR01QR+mwVFbYiljqa9NknmELVitzxbXw==", "path": "microsoft.extensions.compliance.abstractions/9.0.0", "hashPath": "microsoft.extensions.compliance.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wv4APocpxkPf0zcEFDwGrdwP6tOOSSQ5ByHUxRYcvXu8CXY0N0Pf95usTVs605jG2txxLOpRESH6ficdPDNiTg==", "path": "microsoft.extensions.dependencyinjection.autoactivation/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.autoactivation.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eRuTbk4jB0JcNEcYngXNAiLM6tpDyuUfFd13bPy/TJCdHU3IP+C7dZN7qu+Z7mOg3CmxS78YtRULQX8QhL+fMQ==", "path": "microsoft.extensions.diagnostics.exceptionsummarization/9.0.0", "hashPath": "microsoft.extensions.diagnostics.exceptionsummarization.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Diagnostics/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-68vpnYcWc/6WZQItq1P4bwJ5K14jFrj0BvFl7yIKwsGrQK+hvqU/1sqT/DnjmvgXSUAnTLluAtBIVMomadYD/g==", "path": "microsoft.extensions.http.diagnostics/9.0.0", "hashPath": "microsoft.extensions.http.diagnostics.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Resilience/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XkzXX1r+J6wxzR9jLZtJhW+h2x3Ff/hgwVBS9hrzAS7CUFaQgL+oYTAFiP6fyp93ruDxx0rDi/kGzy3KWUDCiQ==", "path": "microsoft.extensions.http.resilience/9.0.0", "hashPath": "microsoft.extensions.http.resilience.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Resilience/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZwG0HaAUbtTaQ6gTnSXd+2mmh3oWbjh+/HKQJRxjBpIOmdKXVCJrs7N6nbVzAZ7GS6eilR1XJFZmjDyJ8QGhRA==", "path": "microsoft.extensions.resilience/9.0.0", "hashPath": "microsoft.extensions.resilience.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.ServiceDiscovery/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bRJ7+R+Qz6ueGGH2WqiZjyAJJho9UBLBclSTf8ReOIeMxjvbaBXTtOGmmrckSSJ/RLQRJKM9QNyK9+y1UUpVGw==", "path": "microsoft.extensions.servicediscovery/9.0.0", "hashPath": "microsoft.extensions.servicediscovery.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gJRU3MTYVtB6reZGJGVOcYhNLgt1i86Pt8yMnekCe7mEz9hr2g5fg5mu2l1lWoxM4eagqDHtk9HkrQFS88yJ3w==", "path": "microsoft.extensions.servicediscovery.abstractions/9.0.0", "hashPath": "microsoft.extensions.servicediscovery.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aid79SF7sJmn/rpliUscwV3gnDOjqx1GmG+R/5E9mK8Cy5tNhEcGnak7RW9RSOOCzqpLEP3jOwdcH3YZ2F/kNA==", "path": "microsoft.extensions.telemetry/9.0.0", "hashPath": "microsoft.extensions.telemetry.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-F84i7q3FleH1kjmaSuslZ33yzWtzxZXWjd1ylXcuwM5jNjWN2ifJSO0xQ5c5BYWZA5VeIWWUWupIYr6+3TnDqA==", "path": "microsoft.extensions.telemetry.abstractions/9.0.0", "hashPath": "microsoft.extensions.telemetry.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-irv0HuqoH8Ig5i2fO+8dmDNdFdsrO+DoQcedwIlb810qpZHBNQHZLW7C/AHBQDgLLpw2T96vmMAy/aE4Yj55Sg==", "path": "microsoft.io.recyclablememorystream/3.0.0", "hashPath": "microsoft.io.recyclablememorystream.3.0.0.nupkg.sha512"}, "OpenTelemetry/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-7scS6BUhwYeSXEDGhCxMSezmvyCoDU5kFQbmfyW9iVvVTcWhec+1KIN33/LOCdBXRkzt2y7+g03mkdAB0XZ9Fw==", "path": "opentelemetry/1.9.0", "hashPath": "opentelemetry.1.9.0.nupkg.sha512"}, "OpenTelemetry.Api/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xz8ZvM1Lm0m7BbtGBnw2JlPo++YKyMp08zMK5p0mf+cIi5jeMt2+QsYu9X6YEAbjCxBQYwEak5Z8sY6Ig2WcwQ==", "path": "opentelemetry.api/1.9.0", "hashPath": "opentelemetry.api.1.9.0.nupkg.sha512"}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-L0D4LBR5JFmwLun5MCWVGapsJLV0ANZ+XXu9NEI3JE/HRKkRuUO+J2MuHD5DBwiU//QMYYM4B22oev1hVLoHDQ==", "path": "opentelemetry.api.providerbuilderextensions/1.9.0", "hashPath": "opentelemetry.api.providerbuilderextensions.1.9.0.nupkg.sha512"}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-qzFOP3V2eYIVbug3U4BJzzidHe9JhAJ42WZ/H8pUp/45Ry3MQQg/+e/ZieClJcxKnpbkXi7dUq1rpvuNp+yBYA==", "path": "opentelemetry.exporter.opentelemetryprotocol/1.9.0", "hashPath": "opentelemetry.exporter.opentelemetryprotocol.1.9.0.nupkg.sha512"}, "OpenTelemetry.Extensions.Hosting/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-QBQPrKDVCXxTBE+r8tgjmFNKKHi4sKyczmip2XGUcjy8kk3quUNhttnjiMqC4sU50Hemmn4i5752Co26pnKe3A==", "path": "opentelemetry.extensions.hosting/1.9.0", "hashPath": "opentelemetry.extensions.hosting.1.9.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.AspNetCore/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-x4HuWBw1rbWZUh5j8/GpXz3xa7JnrTuKne+ACmBqvcoO/rNGkG7HayRruwoQ7gf52xpMtRGr4gxlhLW8eU0EiQ==", "path": "opentelemetry.instrumentation.aspnetcore/1.9.0", "hashPath": "opentelemetry.instrumentation.aspnetcore.1.9.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Http/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-+ZXppf8Qxz3OdC803T8fB6i8iSscc8PsxMnM/JizSOYmkz+8vGiScEiaBBBFNZtMh2KpA0q+qxwnSwQUkbvzog==", "path": "opentelemetry.instrumentation.http/1.9.0", "hashPath": "opentelemetry.instrumentation.http.1.9.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Runtime/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-6raJb9Pvi1CaBB59SX86Mr9NQiQbiv9ialO+cQKFRGCq3Bl2WC8cTTcbfGtaRX0quqWnZC/dK7xrXuOuYcwANA==", "path": "opentelemetry.instrumentation.runtime/1.9.0", "hashPath": "opentelemetry.instrumentation.runtime.1.9.0.nupkg.sha512"}, "Polly.Core/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "path": "polly.core/8.4.2", "hashPath": "polly.core.8.4.2.nupkg.sha512"}, "Polly.Extensions/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "path": "polly.extensions/8.4.2", "hashPath": "polly.extensions.8.4.2.nupkg.sha512"}, "Polly.RateLimiting/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "path": "polly.ratelimiting/8.4.2", "hashPath": "polly.ratelimiting.8.4.2.nupkg.sha512"}}}