{"$schema": "http://json-schema.org/draft-04/schema#", "definitions": {"webOptimizer": {"title": "web optimizer", "type": "object", "description": "Settings for WebOptimizer.Core", "properties": {"enableCaching": {"description": "Determines if the \"cache-control\" HTTP headers should be set and if conditional GET (304) requests should be supported. This could be helpful to disable while in development mode.", "type": "boolean"}, "enableTagHelperBundling": {"description": "Determines if `<script>` and `<link>` elements should point to the bundled path or a reference per source file should be created. This is helpful to disable when in development mode.", "type": "boolean", "default": true}}}, "cdn": {"title": "CDN", "type": "object", "description": "Definitions for WebEssentials.AspNetCore.CdnTagHelpers", "properties": {"url": {"description": "An absolute URL used as a prefix for static resources", "type": "string", "pattern": "^((//|https?://).+|)$"}, "prefetch": {"description": "If true, injects a <link rel='dns-prefetch'> tag that speeds up DNS resolution to the CDN.", "type": "boolean", "default": true}}}, "pwa": {"properties": {"cacheId": {"description": "The cache identifier of the service worker (can be any string). Change this property to force the service worker to reload in browsers.", "type": "string", "default": "v1.0"}, "offlineRoute": {"description": "The route to the page to show when offline.", "type": "string", "default": "/offline.html"}, "registerServiceWorker": {"description": "Determines if a script that registers the service worker should be injected into the bottom of the HTML page.", "type": "boolean", "default": true}, "registerWebmanifest": {"description": "Determines if a meta tag that points to the web manifest should be inserted at the end of the head element.", "type": "boolean", "default": true}, "routesToPreCache": {"description": "A comma separated list of routes to pre-cache when service worker installs in the browser.", "type": "string", "default": ""}, "strategy": {"description": "Selects one of the predefined service worker types.", "enum": ["cacheFirst", "cacheFirstSafe", "minimal", "networkFirst"], "default": "cacheFirstSafe"}}}, "ElmahIo": {"properties": {"ApiKey": {"description": "An elmah.io API key with the Messages | Write permission.", "type": "string", "pattern": "^([0-9a-f]{32})|(#\\{.*\\}#?)$"}, "LogId": {"description": "The Id of the elmah.io log to store messages in.", "type": "string", "pattern": "^([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})|(#\\{.*\\}#?)$"}, "Application": {"description": "An application name to put on all error messages.", "type": "string"}, "HandledStatusCodesToLog": {"description": "A list of HTTP status codes (besides 404) to log even though no exception is thrown.", "type": "array", "items": {"type": "integer"}}, "TreatLoggingAsBreadcrumbs": {"description": "Include log messages from Microsoft.Extensions.Logging as breadcrumbs.", "type": "boolean"}, "HeartbeatId": {"description": "The Id of the elmah.io heartbeat to notify.", "type": "string", "pattern": "^([0-9a-f]{32})|(#\\{.*\\}#?)$"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "LogId"]}, "protocols": {"description": "The protocols enabled on the endpoint.", "type": "string", "enum": ["None", "Http1", "Http2", "Http1AndHttp2", "Http3", "Http1AndHttp2AndHttp3"]}, "certificate": {"title": "certificate", "description": "Certificate configuration.", "type": "object", "properties": {"Path": {"description": "The certificate file path. If a file path is specified then the certificate will be loaded from the file system.", "type": "string"}, "KeyPath": {"description": "The certificate key file path. Available in .NET 5 and later.", "type": "string"}, "Password": {"description": "The certificate password used to access the private key.", "type": "string"}, "Subject": {"description": "The certificate subject. If a subject is specified then the certificate will be loaded from the certificate store.", "type": "string"}, "Store": {"description": "The certificate store name. Defaults to '<PERSON>'.", "type": "string", "default": "My"}, "Location": {"description": "The certificate store location. Defaults to 'CurrentUser'.", "type": "string", "enum": ["LocalMachine", "CurrentUser"], "default": "CurrentUser"}, "AllowInvalid": {"description": "A value indicating whether or not to load certificates that are considered invalid. Defaults to false.", "type": "boolean", "default": false}}}, "sslProtocols": {"description": "Specifies allowable SSL protocols. Defaults to 'None' which allows the operating system to choose the best protocol to use, and to block protocols that are not secure. Unless your app has a specific reason not to, you should use this default. Available in .NET 5 and later.", "type": "array", "items": {"type": "string", "enum": ["None", "Tls", "Tls11", "Tls12", "Tls13"], "default": "None"}}, "clientCertificateMode": {"description": "Specifies the client certificate requirements for a HTTPS connection. Defaults to 'NoCertificate'. Available in .NET 5 and later.", "type": "string", "enum": ["NoCertificate", "AllowCertificate", "RequireCertificate"], "default": "NoCertificate"}, "kestrel": {"title": "kestrel", "type": "object", "description": "ASP.NET Core Kestrel server configuration.", "properties": {"Endpoints": {"title": "endpoints", "description": "Endpoints that Kestrel listens to for network requests. Each endpoint has a name specified by its JSON property name.", "type": "object", "additionalProperties": {"title": "endpoint options", "description": "Kestrel endpoint configuration.", "type": "object", "properties": {"Url": {"description": "The scheme, host name, and port the endpoint will listen on. A Url is required.", "type": "string", "format": "uri"}, "Protocols": {"$ref": "#/definitions/protocols"}, "SslProtocols": {"$ref": "#/definitions/sslProtocols"}, "Certificate": {"$ref": "#/definitions/certificate"}, "ClientCertificateMode": {"$ref": "#/definitions/clientCertificateMode"}, "Sni": {"title": "SNI", "description": "Server Name Indication (SNI) configuration. This enables the mapping of client requested host names to certificates and other TLS settings. Wildcard names prefixed with '*.', as well as a top level '*' are supported. Available in .NET 5 and later.", "type": "object", "additionalProperties": {"title": "SNI options", "description": "Endpoint SNI configuration.", "type": "object", "properties": {"Protocols": {"$ref": "#/definitions/protocols"}, "SslProtocols": {"$ref": "#/definitions/sslProtocols"}, "Certificate": {"$ref": "#/definitions/certificate"}, "ClientCertificateMode": {"$ref": "#/definitions/clientCertificateMode"}}}}}, "required": ["Url"]}}, "EndpointDefaults": {"title": "endpoint defaults", "description": "Default configuration applied to all endpoints. Named endpoint specific configuration overrides defaults.", "type": "object", "properties": {"Protocols": {"$ref": "#/definitions/protocols"}, "SslProtocols": {"$ref": "#/definitions/sslProtocols"}, "ClientCertificateMode": {"$ref": "#/definitions/clientCertificateMode"}}}, "Certificates": {"title": "certificates", "description": "Certificates that Kestrel uses with HTTPS endpoints. Each certificate has a name specified by its JSON property name. The 'Default' certificate is used by HTTPS endpoints that haven't specified a certificate.", "type": "object", "additionalProperties": {"$ref": "#/definitions/certificate"}}}}, "logLevelThreshold": {"description": "Log level threshold.", "type": "string", "enum": ["Trace", "Debug", "Information", "Warning", "Error", "Critical", "None"]}, "logLevel": {"title": "logging level options", "description": "Log level configurations used when creating logs. Only logs that exceeds its matching log level will be enabled. Each log level configuration has a category specified by its JSON property name. For more information about configuring log levels, see https://docs.microsoft.com/aspnet/core/fundamentals/logging/#configure-logging.", "type": "object", "additionalProperties": {"$ref": "#/definitions/logLevelThreshold"}, "properties": {"Seq": {"$ref": "#/definitions/logLevelThreshold"}}}, "logging": {"title": "logging options", "type": "object", "description": "Configuration for Microsoft.Extensions.Logging.", "properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}, "Console": {"properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}, "FormatterName": {"description": "Name of the log message formatter to use. Defaults to 'simple'.", "type": "string", "default": "simple"}, "FormatterOptions": {"title": "formatter options", "description": "Log message formatter options. Additional properties are available on the options depending on the configured formatter. The formatter is specified by FormatterName.", "type": "object", "properties": {"IncludeScopes": {"description": "Include scopes when true. Defaults to false.", "type": "boolean", "default": false}, "TimestampFormat": {"description": "Format string used to format timestamp in logging messages. Defaults to null.", "type": "string"}, "UseUtcTimestamp": {"description": "Indication whether or not UTC timezone should be used to for timestamps in logging messages. Defaults to false.", "type": "boolean", "default": false}}}, "LogToStandardErrorThreshold": {"$ref": "#/definitions/logLevelThreshold", "description": "The minimum level of messages are written to Console.Error."}}}, "EventSource": {"properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}}}, "Debug": {"properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}}}, "EventLog": {"properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}}}, "ElmahIo": {"properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}}}, "ElmahIoBreadcrumbs": {"properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}}}}, "additionalProperties": {"title": "provider logging settings", "type": "object", "description": "Logging configuration for a provider. The provider name must match the configuration's JSON property property name.", "properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}}}}, "allowedHosts": {"description": "ASP.NET Core host filtering middleware configuration. Allowed hosts is a semicolon-delimited list of host names without port numbers. Requests without a matching host name will be refused. Host names may be prefixed with a '*.' wildcard, or use '*' to allow all hosts.", "type": "string"}, "connectionStrings": {"title": "connection string options", "description": "Connection string configuration. Get connection strings with the IConfiguration.GetConnectionString(string) extension method.", "type": "object", "additionalProperties": {"description": "Connection string configuration. Each connection string has a name specified by its JSON property name.", "type": "string"}}, "NLog": {"title": "NLog options", "type": "object", "description": "NLog configuration", "default": {}, "properties": {"autoReload": {"type": "boolean", "description": "Automatically reload the NLog configuration when notified that appsettings.json file has changed.", "default": false}, "throwConfigExceptions": {"type": ["boolean", "null"], "description": "Throws an exception when there is a config error? If not set, then throwExceptions will be used for this setting.", "default": false}, "throwExceptions": {"type": "boolean", "description": "Throws an exception when there is an error. For unit testing only and advanced troubleshooting.", "default": false}, "internalLogLevel": {"type": "string", "description": "The minimal log level for the internal logger.", "enum": ["Trace", "Debug", "Info", "<PERSON><PERSON>", "Error", "Fatal", "Off"], "default": "Off"}, "internalLogFile": {"type": "string", "description": "Write internal log to the specified filepath"}, "internalLogToConsole": {"type": "boolean", "description": "Write internal log to a console", "default": "false"}, "internalLogToConsoleError": {"type": "boolean", "description": "Write internal log to a console with error stream", "default": "false"}, "globalThreshold": {"type": "string", "description": "Log events below this threshold are not logged.", "enum": ["Trace", "Debug", "Info", "<PERSON><PERSON>", "Error", "Fatal", "Off"], "default": "Off"}, "autoShutdown": {"type": "boolean", "description": "Automatically call `LogFactory.Shutdown` on AppDomain.Unload or AppDomain.ProcessExit", "default": "true"}, "extensions": {"type": "array", "description": "Load NLog extension packages for additional targets and layouts", "default": [], "items": {"title": "extension", "type": "object", "description": "", "default": {}, "properties": {"assembly": {"type": "string", "description": "Assembly Name of the NLog extension package."}, "prefix": {"type": "string", "description": "Appends prefix to all type-names loaded from the assembly", "default": ""}, "assemblyFile": {"type": "string", "description": "Absolute filepath to the Assembly-file of the NLog extension package.", "default": ""}}}}, "variables": {"title": "variables", "type": "object", "description": "Key-value pair of variables", "propertyNames": {"pattern": "^[A-Za-z0-9_.-]+$"}, "patternProperties": {".*": {"type": ["number", "string", "boolean"]}}}, "targetDefaultWrapper": {"title": "default wrapper", "type": "object", "description": "Wrap all defined targets with this custom target wrapper.", "default": {}, "required": ["type"], "properties": {"type": {"type": "string", "description": ""}}}, "targets": {"title": "targets", "type": "object", "description": "", "default": {}, "properties": {"async": {"type": "boolean", "description": "Wrap all defined targets using AsyncWrapper with OverflowAction=Discard for better performance."}}}, "rules": {"oneOf": [{"type": "array", "description": "", "default": [], "items": {"$ref": "#/definitions/NLogRulesItem"}}, {"title": "rules", "type": "object", "propertyNames": {"pattern": "^[0-9]+$"}, "patternProperties": {".*": {"$ref": "#/definitions/NLogRulesItem"}}}]}}}, "NLogRulesItem": {"title": "NLog rule item", "type": "object", "description": "Redirect LogEvents from matching Logger objects to specified targets", "default": {}, "required": ["logger"], "properties": {"logger": {"type": "string", "description": "Match Logger objects based on their Logger-name. Can use wildcard characters ('*' or '?')."}, "ruleName": {"type": "string", "description": "Rule identifier to allow rule lookup with Configuration.FindRuleByName and Configuration.RemoveRuleByName."}, "level": {"anyOf": [{"type": "string", "description": "", "enum": ["Trace", "Debug", "Info", "<PERSON><PERSON>", "Error", "Fatal"]}, {"type": "string"}]}, "levels": {"type": "string", "description": "Comma separated list of levels that this rule matches."}, "minLevel": {"anyOf": [{"type": "string", "description": "", "enum": ["Trace", "Debug", "Info", "<PERSON><PERSON>", "Error", "Fatal"]}, {"type": "string"}]}, "maxLevel": {"anyOf": [{"type": "string", "description": "", "enum": ["Trace", "Debug", "Info", "<PERSON><PERSON>", "Error", "Fatal"]}, {"type": "string"}]}, "finalMinLevel": {"anyOf": [{"type": "string", "description": "", "enum": ["Trace", "Debug", "Info", "<PERSON><PERSON>", "Error", "Fatal"]}, {"type": "string"}]}, "writeTo": {"type": "string", "description": "Name or names of a target - separated by comma. Remove this property for sending events to the blackhole."}, "final": {"type": "boolean", "description": "Ignore further rules if this one matches.", "default": false}, "enabled": {"type": "boolean", "description": "", "default": true}, "filters": {"oneOf": [{"type": "array", "description": "", "default": [], "items": {"title": "filter", "type": "object", "description": "", "default": {}, "required": ["type"], "properties": {"type": {"type": "string", "description": ""}, "action": {"type": "string", "description": "Result action when filter matches logevent.", "enum": ["Neutral", "Log", "Ignore", "LogFinal", "IgnoreFinal"], "default": "Neutral"}}}}, {"title": "filter", "type": "object", "description": "", "default": {}}]}, "filterDefaultAction": {"type": "string", "description": "Default action if none of the filters match.", "enum": ["Neutral", "Log", "Ignore", "LogFinal", "IgnoreFinal"], "default": "Ignore"}}}, "Serilog": {"type": "object", "title": "Serilog appSettings", "description": "Serilog appSettings Configuration", "properties": {"$schema": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "description": "Pointer to the schema against which this document should be validated."}, "Using": {"type": "array", "title": "List of Auto-discovery of configuration assemblies", "description": "Using section contains list of assemblies in which configuration methods. Can be required depending of the project type: See: https://github.com/serilog/serilog-settings-configuration#using-section-and-auto-discovery-of-configuration-assemblies", "uniqueItems": true, "items": {"$ref": "#/definitions/Serilog/definitions/AssemblyReference"}}, "LevelSwitches": {"type": "object", "patternProperties": {"^(?<SerilogLevelSwitcherName>\\${0,1}[A-Za-z]+[A-Za-z0-9]*)$": {"$ref": "#/definitions/Serilog/definitions/SerilogLogEventLevel"}}, "additionalProperties": false}, "FilterSwitches": {"type": "object", "patternProperties": {"^(?<SerilogLevelSwitcherName>\\${0,1}[A-Za-z]+[A-Za-z0-9]*)$": {"type": "string"}}, "additionalProperties": false}, "MinimumLevel": {"type": ["string", "object"], "title": "Minimum LogLevel Threshold", "description": "Minimum LogLevel Threshold. (Support dynamic reload if the underlying IConfigurationProvider supports it)", "oneOf": [{"$ref": "#/definitions/Serilog/definitions/SerilogLogEventLevel"}, {"$ref": "#/definitions/Serilog/definitions/DetailedMinimumLevel"}]}, "Properties": {"type": "object", "title": "Log events Properties", "description": "This section defines a static list of key-value pairs that will enrich log events.", "additionalProperties": {"type": "string"}}, "Enrich": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReference"}], "title": "Log events Enriches", "description": "This section defines Enriches that will be applied to log events."}, "Destructure": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReference"}], "title": "Log events Destructure", "description": "This section defines Destructure."}, "Filter": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReference"}], "title": "Log events filters", "description": "This section defines filters that will be applied to log events."}, "WriteTo": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReference"}], "title": "Configuration for log destination", "description": "This section configures the sinks that log events will be emitted to."}, "AuditTo": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReference"}], "title": "Configuration for log destination for auditing", "description": "This section configures sinks for auditing, instead of regular (safe) logging. Obs: When auditing is used, exceptions from sinks and any intermediate filters propagate back to the caller."}}, "patternProperties": {"^Enrich:((?<EnvironmentVariableName>[a-zA-Z_]\\w*)|(?<ArrayIndex>\\d*))$": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}], "title": "Log events Enriches", "description": "This section defines Enriches that will be applied to log events."}, "^Destructure:((?<EnvironmentVariableName>[a-zA-Z_]\\w*)|(?<ArrayIndex>\\d*))$": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}], "title": "Log events Destructure", "description": "This section defines Destructure."}, "^Filter:((?<EnvironmentVariableName>[a-zA-Z_]\\w*)|(?<ArrayIndex>\\d*))$": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}], "title": "Log events filters", "description": "This section defines filters that will be applied to log events."}, "^WriteTo:((?<EnvironmentVariableName>[a-zA-Z_]\\w*)|(?<ArrayIndex>\\d*))$": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}], "title": "Configuration for log destination", "description": "This section configures the sinks that log events will be emitted to."}, "^AuditTo:((?<EnvironmentVariableName>[a-zA-Z_]\\w*)|(?<ArrayIndex>\\d*))$": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}], "title": "Configuration for log destination for auditing", "description": "This section configures sinks for auditing, instead of regular (safe) logging. Obs: When auditing is used, exceptions from sinks and any intermediate filters propagate back to the caller."}}, "additionalProperties": false, "definitions": {"SerilogLogEventLevel": {"type": "string", "title": "Log level", "description": "Log level threshold.", "enum": ["Verbose", "Debug", "Information", "Warning", "Error", "Fatal"]}, "LoggingLevelSwitch": {"type": "string", "title": "LevelSwitches name", "description": "Log Level Switch string reference.", "pattern": "^(?<SerilogLevelSwitcherName>\\${0,1}[A-Za-z]+[A-Za-z0-9]*)$"}, "SerilogLogLevelThreshold": {"type": "string", "title": "Log Level or LevelSwitches name", "description": "A Serilog Log Level or a reference to a Log Level Switch name on `LevelSwitches` configuration.", "anyOf": [{"$ref": "#/definitions/Serilog/definitions/SerilogLogEventLevel"}, {"$ref": "#/definitions/Serilog/definitions/LoggingLevelSwitch"}]}, "DetailedMinimumLevel": {"type": "object", "title": "Detailed Log level.", "description": "Detailed Log level threshold object. Allowing set log levels be overridden per logging source.", "properties": {"Default": {"$ref": "#/definitions/Serilog/definitions/SerilogLogLevelThreshold"}, "ControlledBy": {"$ref": "#/definitions/Serilog/definitions/LoggingLevelSwitch"}, "Override": {"type": "object", "title": "Logging Source Log level object.", "description": "Set the Log level threshold or LevelSwitcher reference per Logging Source.", "additionalProperties": {"$ref": "#/definitions/Serilog/definitions/SerilogLogLevelThreshold"}}}, "additionalProperties": false}, "AssemblyReference": {"type": "string", "title": "Assembly Name", "description": ".NET Assembly Name, without the file extension", "minLength": 1, "pattern": "^(?<AssemblyName>\\S+)$"}, "ComplexMethodCallReference": {"type": "object", "properties": {"Name": {"$ref": "#/definitions/Serilog/definitions/CSharpMethodName"}, "Args": {"type": "object", "patternProperties": {"^(?<CSharpMethodArgumentName>[a-zA-Z_]\\w*)$": {}}, "additionalProperties": false}}, "additionalProperties": false, "required": ["Name"]}, "MethodCallReferenceItem": {"type": ["string", "object", "null"], "oneOf": [{"$ref": "#/definitions/Serilog/definitions/CSharpMethodName"}, {"$ref": "#/definitions/Serilog/definitions/ComplexMethodCallReference"}]}, "MethodCallReference": {"type": ["array", "string", "object"], "minLength": 1, "pattern": "^(?<CSharpMethodName>[a-zA-Z_]\\w*)$", "minItems": 1, "uniqueItems": true, "items": {"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}, "additionalProperties": {"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}}, "CSharpMethodName": {"type": "string", "title": "Method Name", "description": "A name referring to a C# Class method", "minLength": 1, "pattern": "^(?<CSharpMethodName>[a-zA-Z_]\\w*)$"}, "CSharpMethodArgumentName": {"type": "string", "title": "Argument Name", "description": "A name referring to a C# Class method argument", "minLength": 1, "pattern": "^(?<CSharpMethodArgumentName>[a-zA-Z_]\\w*)$"}, "EnvironmentVariableName": {"type": "string", "title": "Environment Variable Name", "description": "A name referring to a OS Environment Variable", "minLength": 1, "pattern": "^(?<EnvironmentVariableName>[a-zA-Z_]\\w*)$"}, "SerilogLevelSwitcherName": {"type": "string", "title": "A Level Switcher Name", "description": "A name referring to a Serilog Settings Configuration Level Switcher", "minLength": 1, "pattern": "^(?<SerilogLevelSwitcherName>\\${0,1}[A-Za-z]+[A-Za-z0-9]*)$"}}}}, "id": "https://json.schemastore.org/appsettings.json", "patternProperties": {"^WebOptimizer$": {"$ref": "#/definitions/webOptimizer"}, "^webOptimizer$": {"$ref": "#/definitions/webOptimizer"}, "^weboptimizer$": {"$ref": "#/definitions/webOptimizer"}, "^(cdn|Cdn)$": {"$ref": "#/definitions/cdn"}, "^(pwa|PWA|Pwa)$": {"$ref": "#/definitions/pwa"}, "^(ElmahIo|Elmahio|elmahIo|elmahio)$": {"$ref": "#/definitions/ElmahIo"}, "^(nlog|Nlog|NLog)$": {"$ref": "#/definitions/NLog"}, "^(Serilog|serilog)$": {"$ref": "#/definitions/Serilog"}}, "properties": {"Kestrel": {"$ref": "#/definitions/kestrel"}, "Logging": {"$ref": "#/definitions/logging"}, "AllowedHosts": {"$ref": "#/definitions/allowedHosts"}, "ConnectionStrings": {"$ref": "#/definitions/connectionStrings"}, "Aspire": {"type": "object", "properties": {"Seq": {"type": "object", "properties": {"ApiKey": {"type": "string", "description": "Gets or sets a Seq API key that authenticates the client to the Seq server."}, "DisableHealthChecks": {"type": "boolean", "description": "Gets or sets a boolean value that indicates whether the Seq server health check is disabled or not.", "default": false}, "Logs": {"type": "object", "properties": {"BatchExportProcessorOptions": {"type": "object", "properties": {"ExporterTimeoutMilliseconds": {"type": "integer"}, "MaxExportBatchSize": {"type": "integer"}, "MaxQueueSize": {"type": "integer"}, "ScheduledDelayMilliseconds": {"type": "integer"}}, "description": "Gets or sets the BatchExportProcessor options. Ignored unless ExportProcessorType is Batch."}, "Endpoint": {"type": "string", "format": "uri", "description": "Gets or sets the target to which the exporter is going to send telemetry."}, "ExportProcessorType": {"enum": ["Simple", "<PERSON><PERSON>"], "description": "Gets or sets the export processor type to be used with the OpenTelemetry Protocol Exporter. The default value is 'OpenTelemetry.ExportProcessorType.Batch'."}, "Headers": {"type": "string", "description": "Gets or sets optional headers for the connection."}, "Protocol": {"enum": ["Grpc", "HttpProtobuf"], "description": "Gets or sets the the OTLP transport protocol."}, "TimeoutMilliseconds": {"type": "integer", "description": "Gets or sets the max waiting time (in milliseconds) for the backend to process each batch. Default value: 10000."}}, "description": "Gets OTLP exporter options for logs."}, "ServerUrl": {"type": "string", "description": "Gets or sets the base URL of the Seq server (including protocol and port). E.g. \"https://example.seq.com:6789. Overrides endpoints set on Logs and Traces.\""}, "Traces": {"type": "object", "properties": {"BatchExportProcessorOptions": {"type": "object", "properties": {"ExporterTimeoutMilliseconds": {"type": "integer"}, "MaxExportBatchSize": {"type": "integer"}, "MaxQueueSize": {"type": "integer"}, "ScheduledDelayMilliseconds": {"type": "integer"}}, "description": "Gets or sets the BatchExportProcessor options. Ignored unless ExportProcessorType is Batch."}, "Endpoint": {"type": "string", "format": "uri", "description": "Gets or sets the target to which the exporter is going to send telemetry."}, "ExportProcessorType": {"enum": ["Simple", "<PERSON><PERSON>"], "description": "Gets or sets the export processor type to be used with the OpenTelemetry Protocol Exporter. The default value is 'OpenTelemetry.ExportProcessorType.Batch'."}, "Headers": {"type": "string", "description": "Gets or sets optional headers for the connection."}, "Protocol": {"enum": ["Grpc", "HttpProtobuf"], "description": "Gets or sets the the OTLP transport protocol."}, "TimeoutMilliseconds": {"type": "integer", "description": "Gets or sets the max waiting time (in milliseconds) for the backend to process each batch. Default value: 10000."}}, "description": "Gets OTLP exporter options for traces."}}, "description": "Provides the client configuration settings for connecting telemetry to a Seq server."}}}}, "title": "JSON schema ASP.NET Core's appsettings.json file", "type": "object"}