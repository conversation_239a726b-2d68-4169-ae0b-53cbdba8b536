{"definitions": {"logLevel": {"properties": {"Seq": {"$ref": "#/definitions/logLevelThreshold"}}}}, "properties": {"Aspire": {"type": "object", "properties": {"Seq": {"type": "object", "properties": {"ApiKey": {"type": "string", "description": "Gets or sets a Seq API key that authenticates the client to the Seq server."}, "DisableHealthChecks": {"type": "boolean", "description": "Gets or sets a boolean value that indicates whether the Seq server health check is disabled or not.", "default": false}, "Logs": {"type": "object", "properties": {"BatchExportProcessorOptions": {"type": "object", "properties": {"ExporterTimeoutMilliseconds": {"type": "integer"}, "MaxExportBatchSize": {"type": "integer"}, "MaxQueueSize": {"type": "integer"}, "ScheduledDelayMilliseconds": {"type": "integer"}}, "description": "Gets or sets the BatchExportProcessor options. Ignored unless ExportProcessorType is Batch."}, "Endpoint": {"type": "string", "format": "uri", "description": "Gets or sets the target to which the exporter is going to send telemetry."}, "ExportProcessorType": {"enum": ["Simple", "<PERSON><PERSON>"], "description": "Gets or sets the export processor type to be used with the OpenTelemetry Protocol Exporter. The default value is 'OpenTelemetry.ExportProcessorType.Batch'."}, "Headers": {"type": "string", "description": "Gets or sets optional headers for the connection."}, "Protocol": {"enum": ["Grpc", "HttpProtobuf"], "description": "Gets or sets the the OTLP transport protocol."}, "TimeoutMilliseconds": {"type": "integer", "description": "Gets or sets the max waiting time (in milliseconds) for the backend to process each batch. Default value: 10000."}}, "description": "Gets OTLP exporter options for logs."}, "ServerUrl": {"type": "string", "description": "Gets or sets the base URL of the Seq server (including protocol and port). E.g. \"https://example.seq.com:6789. Overrides endpoints set on Logs and Traces.\""}, "Traces": {"type": "object", "properties": {"BatchExportProcessorOptions": {"type": "object", "properties": {"ExporterTimeoutMilliseconds": {"type": "integer"}, "MaxExportBatchSize": {"type": "integer"}, "MaxQueueSize": {"type": "integer"}, "ScheduledDelayMilliseconds": {"type": "integer"}}, "description": "Gets or sets the BatchExportProcessor options. Ignored unless ExportProcessorType is Batch."}, "Endpoint": {"type": "string", "format": "uri", "description": "Gets or sets the target to which the exporter is going to send telemetry."}, "ExportProcessorType": {"enum": ["Simple", "<PERSON><PERSON>"], "description": "Gets or sets the export processor type to be used with the OpenTelemetry Protocol Exporter. The default value is 'OpenTelemetry.ExportProcessorType.Batch'."}, "Headers": {"type": "string", "description": "Gets or sets optional headers for the connection."}, "Protocol": {"enum": ["Grpc", "HttpProtobuf"], "description": "Gets or sets the the OTLP transport protocol."}, "TimeoutMilliseconds": {"type": "integer", "description": "Gets or sets the max waiting time (in milliseconds) for the backend to process each batch. Default value: 10000."}}, "description": "Gets OTLP exporter options for traces."}}, "description": "Provides the client configuration settings for connecting telemetry to a Seq server."}}}}, "type": "object", "SourceSegments": "C:\\Users\\<USER>\\.nuget\\packages\\aspire.seq\\9.1.0\\ConfigurationSchema.json"}