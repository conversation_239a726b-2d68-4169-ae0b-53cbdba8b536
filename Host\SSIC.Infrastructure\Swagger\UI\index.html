﻿<!-- HTML for static distribution bundle build -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>%(DocumentTitle)</title>
    <link rel="stylesheet" type="text/css" href="./swagger-ui.css">
    <link rel="icon" type="image/png" href="./favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="./favicon-16x16.png" sizes="16x16" />
    <style>
        html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }

        *,
        *:before,
        *:after { box-sizing: inherit; }

        body { margin: 0; background: #fafafa; }
    </style>
    %(HeadContent)
</head>

<body>
    <div id="swagger-ui"></div>
    <!-- Workaround for https://github.com/swagger-api/swagger-editor/issues/1371 -->
    <script>
        if (window.navigator.userAgent.indexOf("Edge") > -1) {
            console.log("Removing native Edge fetch in favor of swagger-ui's polyfill")
            window.fetch = undefined;
        }</script>

    <script src="./swagger-ui-bundle.js"></script>
    <script src="./swagger-ui-standalone-preset.js"></script>
    <script>
        var HideEmptyTagsPlugin = function HideEmptyTagsPlugin() {
            return {
                statePlugins: {
                    spec: {
                        wrapSelectors: {
                            taggedOperations: function taggedOperations(ori) {
                                return function () {
                                    return ori.apply(void 0, arguments).filter(function (tagMeta) {
                                        return tagMeta.get("operations") && tagMeta.get("operations").size > 0;
                                    });
                                };
                            }
                        }
                    }
                }
            };
        };

        window.onload = function () {
            var tokenKey = "access-token";

            var configObject = JSON.parse('%(ConfigObject)');
            configObject.responseInterceptor = function (rep) {
                var accessToken = rep.headers[tokenKey];
                if (accessToken && accessToken != "invalid token") {
                    window.localStorage.setItem(tokenKey, accessToken);
                    ui.preauthorizeApiKey("Bearer", accessToken);
                }
                else if (accessToken == "invalid token") {
                    window.localStorage.removeItem(tokenKey);
                }
            }
            configObject.onComplete = function () {
                var accessToken = window.localStorage.getItem(tokenKey);
                if (accessToken) {
                    ui.preauthorizeApiKey("Bearer", accessToken);
                }
            };
            var oauthConfigObject = JSON.parse('%(OAuthConfigObject)');

            // If validatorUrl is not explicitly provided, disable the feature by setting to null
            if (!configObject.hasOwnProperty("validatorUrl"))
                configObject.validatorUrl = null

            // If oauth2RedirectUrl isn't specified, use the built-in default
            if (!configObject.hasOwnProperty("oauth2RedirectUrl"))
                configObject.oauth2RedirectUrl = window.location.href.replace("index.html", "oauth2-redirect.html").split('#')[0];

            // Apply mandatory parameters
            configObject.dom_id = "#swagger-ui";
            configObject.presets = [SwaggerUIBundle.presets.apis, SwaggerUIStandalonePreset, HideEmptyTagsPlugin];
            configObject.layout = "StandaloneLayout";

            // Begin Swagger UI call region

            const ui = SwaggerUIBundle(configObject);

            ui.initOAuth(oauthConfigObject);

            // End Swagger UI call region

            window.ui = ui
        }</script>
</body>
</html>