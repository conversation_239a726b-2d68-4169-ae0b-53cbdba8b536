{"@t":"2025-04-10T01:30:49.2964034Z","@mt":"Now listening on: {address}","address":"https://localhost:54186","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:30:49.3443112Z","@mt":"Now listening on: {address}","address":"http://localhost:54187","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:30:49.4333200Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:30:49.4338329Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:30:49.4340070Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:04.8346526Z","@mt":"Now listening on: {address}","address":"https://localhost:55103","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:04.8697030Z","@mt":"Now listening on: {address}","address":"http://localhost:55104","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:04.9395194Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:04.9466939Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:04.9467738Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:13.8040119Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":0,"ExecutionTimeMs":4093.548,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:13.8223729Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:19.9784965Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":1,"ExecutionTimeMs":4092.4944,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:19.9810118Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:24.9707533Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":2,"ExecutionTimeMs":4090.0898,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:24.9722761Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:32:35.1772032Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Error","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":3,"ExecutionTimeMs":4070.0163,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:38:54.3454050Z","@mt":"Now listening on: {address}","address":"https://localhost:61286","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:38:54.3711270Z","@mt":"Now listening on: {address}","address":"http://localhost:61287","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:38:54.4161736Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:38:54.4163943Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:38:54.4164553Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:03.3342420Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":0,"ExecutionTimeMs":4096.007,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:03.3716673Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:08.4542029Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":1,"ExecutionTimeMs":4094.9255,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:08.4579018Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:15.2863102Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":2,"ExecutionTimeMs":4063.3325,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:15.2881278Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:39:22.2615593Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Error","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":3,"ExecutionTimeMs":4090.9528,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:53:54.3618083Z","@mt":"Now listening on: {address}","address":"https://localhost:55904","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:53:54.3975671Z","@mt":"Now listening on: {address}","address":"http://localhost:55905","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:53:54.4526977Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:53:54.4529864Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:53:54.4530902Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:03.3390872Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":0,"ExecutionTimeMs":4114.2482,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:03.3572473Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:10.0225238Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":1,"ExecutionTimeMs":4089.1987,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:10.0236258Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:15.1999311Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":2,"ExecutionTimeMs":4092.5037,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:15.2007975Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T01:54:21.3912974Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Error","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":3,"ExecutionTimeMs":4075.55,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:06.8246576Z","@mt":"Now listening on: {address}","address":"https://localhost:56684","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:06.9036027Z","@mt":"Now listening on: {address}","address":"http://localhost:56685","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:07.0491255Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:07.0493806Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:07.0494492Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:20.5382913Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":0,"ExecutionTimeMs":4106.3968,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:20.5758976Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:25.7154777Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":1,"ExecutionTimeMs":4108.0714,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:25.7179722Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:31.7307800Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":2,"ExecutionTimeMs":4068.8293,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:31.7323714Z","@mt":"Resilience event occurred. EventName: '{EventName}', Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}'","@l":"Warning","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","EventName":"OnRetry","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","EventId":{"Name":"ResilienceEvent"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:34:41.0768094Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","@l":"Error","@x":"System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:9411)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellation(CancellationToken cancellationToken)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.Metrics.MetricsHandler.SendAsyncWithMetrics(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpMessageHandlerStage.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at System.Net.Http.SocketsHttpHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.Send(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.SendCore(HttpRequestMessage requestMessage, CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Http.Resilience.ResilienceHandler.<>c.<Send>b__4_0(ResilienceContext context, ValueTuple`2 state)\r\n   at Polly.ResiliencePipeline.<>c__32`2.<Execute>b__32_0(ResilienceContext context, ValueTuple`2 state)","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":"由于目标计算机积极拒绝，无法连接。 (localhost:9411)","Handled":true,"Attempt":3,"ExecutionTimeMs":4084.3617,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:36:11.3191037Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":46.8047,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:36:12.7324690Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":50,\"temperatureF\":121,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@tr":"0983574de70478fb450ddfbd72b0f53b","@sp":"22c43950608f9209","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"22c43950608f9209","TraceId":"0983574de70478fb450ddfbd72b0f53b","ParentId":"0000000000000000","ConnectionId":"0HNBNR0NPMO1N","RequestId":"0HNBNR0NPMO1N:0000000F","RequestPath":"/WeatherForecast2","ActionId":"3a2f6c23-ad8e-4aa3-8a12-3ea4ce2d2d99","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:36:12.7342058Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":50,\"temperatureF\":121,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@l":"Error","@tr":"0983574de70478fb450ddfbd72b0f53b","@sp":"22c43950608f9209","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"22c43950608f9209","TraceId":"0983574de70478fb450ddfbd72b0f53b","ParentId":"0000000000000000","ConnectionId":"0HNBNR0NPMO1N","RequestId":"0HNBNR0NPMO1N:0000000F","RequestPath":"/WeatherForecast2","ActionId":"3a2f6c23-ad8e-4aa3-8a12-3ea4ce2d2d99","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T02:36:16.3932102Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":46.9836,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:38:21.0637818Z","@mt":"Now listening on: {address}","address":"https://localhost:63123","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:38:21.1118462Z","@mt":"Now listening on: {address}","address":"http://localhost:63124","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:38:21.2173603Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:38:21.2177272Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:38:21.2178995Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:41:47.4537019Z","@mt":"Now listening on: {address}","address":"https://localhost:49377","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:41:47.4973183Z","@mt":"Now listening on: {address}","address":"http://localhost:49378","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:41:47.5779214Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:41:47.5781658Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:41:47.5783319Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:41:57.2432229Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":48.1612,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:43:55.6198358Z","@mt":"Now listening on: {address}","address":"https://localhost:51789","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:43:55.6689467Z","@mt":"Now listening on: {address}","address":"http://localhost:51790","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:43:55.7550549Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:43:55.7565547Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:43:55.7893745Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:44:00.3194028Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":57.922,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:44:20.4162465Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":10.1783,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:44:22.5525427Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-20,\"temperatureF\":-3,\"summary\":\"Balmy\",\"test\":\"123\"}条","@tr":"75ee365ae86e752518f54c5c0295cb78","@sp":"05ae7315fc26151f","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"05ae7315fc26151f","TraceId":"75ee365ae86e752518f54c5c0295cb78","ParentId":"0000000000000000","ConnectionId":"0HNBNUANIP9CF","RequestId":"0HNBNUANIP9CF:0000000D","RequestPath":"/WeatherForecast2","ActionId":"2f3d1431-534e-4bdc-bd28-6881366e0405","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:44:22.5538025Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-20,\"temperatureF\":-3,\"summary\":\"Balmy\",\"test\":\"123\"}条","@l":"Error","@tr":"75ee365ae86e752518f54c5c0295cb78","@sp":"05ae7315fc26151f","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"05ae7315fc26151f","TraceId":"75ee365ae86e752518f54c5c0295cb78","ParentId":"0000000000000000","ConnectionId":"0HNBNUANIP9CF","RequestId":"0HNBNUANIP9CF:0000000D","RequestPath":"/WeatherForecast2","ActionId":"2f3d1431-534e-4bdc-bd28-6881366e0405","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:44:25.4866111Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":45.3786,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:24.7677542Z","@mt":"Now listening on: {address}","address":"https://localhost:56891","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:24.8154997Z","@mt":"Now listening on: {address}","address":"http://localhost:56892","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:24.9193379Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:24.9197069Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:24.9198350Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:29.4997081Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":29.7945,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:34.5948605Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":52.576,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:35.9187293Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":23,\"temperatureF\":73,\"summary\":\"Scorching\",\"test\":\"123\"}条","@tr":"dace15292d622037b55d29d0cf626b2c","@sp":"fbb93b01428f5baf","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"fbb93b01428f5baf","TraceId":"dace15292d622037b55d29d0cf626b2c","ParentId":"0000000000000000","ConnectionId":"0HNBNUDPL6ANE","RequestId":"0HNBNUDPL6ANE:0000000D","RequestPath":"/WeatherForecast2","ActionId":"69fccef4-71d7-428d-8b2c-0c4d2c4e5db4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:35.9198348Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":23,\"temperatureF\":73,\"summary\":\"Scorching\",\"test\":\"123\"}条","@l":"Error","@tr":"dace15292d622037b55d29d0cf626b2c","@sp":"fbb93b01428f5baf","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"fbb93b01428f5baf","TraceId":"dace15292d622037b55d29d0cf626b2c","ParentId":"0000000000000000","ConnectionId":"0HNBNUDPL6ANE","RequestId":"0HNBNUDPL6ANE:0000000D","RequestPath":"/WeatherForecast2","ActionId":"69fccef4-71d7-428d-8b2c-0c4d2c4e5db4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:49:39.6543080Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":43.7013,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:50:43.5620297Z","@mt":"Now listening on: {address}","address":"https://localhost:58054","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:50:43.6153718Z","@mt":"Now listening on: {address}","address":"http://localhost:58055","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:50:43.7439048Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:50:43.7442965Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:50:43.7444755Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:50:48.2156244Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":35.4932,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:51:53.3715999Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":13.093,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:51:55.2497557Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-17,\"temperatureF\":2,\"summary\":\"Cool\",\"test\":\"123\"}条","@tr":"f744add565dfc96292fd34eee9b6e8ac","@sp":"8add49308099f429","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"8add49308099f429","TraceId":"f744add565dfc96292fd34eee9b6e8ac","ParentId":"0000000000000000","ConnectionId":"0HNBNUEH61SSP","RequestId":"0HNBNUEH61SSP:0000000D","RequestPath":"/WeatherForecast2","ActionId":"db9ff65b-0bcd-4ae8-ae70-c6dba8a52d51","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:51:55.2508310Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-17,\"temperatureF\":2,\"summary\":\"Cool\",\"test\":\"123\"}条","@l":"Error","@tr":"f744add565dfc96292fd34eee9b6e8ac","@sp":"8add49308099f429","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"8add49308099f429","TraceId":"f744add565dfc96292fd34eee9b6e8ac","ParentId":"0000000000000000","ConnectionId":"0HNBNUEH61SSP","RequestId":"0HNBNUEH61SSP:0000000D","RequestPath":"/WeatherForecast2","ActionId":"db9ff65b-0bcd-4ae8-ae70-c6dba8a52d51","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T05:51:58.4498046Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":47.6542,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T07:50:12.5136679Z","@mt":"Now listening on: {address}","address":"https://localhost:60956","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T07:50:12.5500309Z","@mt":"Now listening on: {address}","address":"http://localhost:60957","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T07:50:12.6394984Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T07:50:12.6398282Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T07:50:12.6399795Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T07:50:17.3882335Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":43.7829,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T07:50:39.8037364Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-14,\"temperatureF\":7,\"summary\":\"Bracing\",\"test\":\"123\"}条","@tr":"e9eae937942948892c0d23cffc007d6b","@sp":"5ba416de89e2faa4","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"5ba416de89e2faa4","TraceId":"e9eae937942948892c0d23cffc007d6b","ParentId":"0000000000000000","ConnectionId":"0HNBO0H9PS94J","RequestId":"0HNBO0H9PS94J:00000001","RequestPath":"/WeatherForecast2","ActionId":"e38cd5fa-afd9-4841-88f3-0c23ef695f0e","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T07:50:39.8047028Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-14,\"temperatureF\":7,\"summary\":\"Bracing\",\"test\":\"123\"}条","@l":"Error","@tr":"e9eae937942948892c0d23cffc007d6b","@sp":"5ba416de89e2faa4","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"5ba416de89e2faa4","TraceId":"e9eae937942948892c0d23cffc007d6b","ParentId":"0000000000000000","ConnectionId":"0HNBO0H9PS94J","RequestId":"0HNBO0H9PS94J:00000001","RequestPath":"/WeatherForecast2","ActionId":"e38cd5fa-afd9-4841-88f3-0c23ef695f0e","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T07:50:42.4777524Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":28.9401,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T07:52:27.6846765Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":8.1111,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:00.1484521Z","@mt":"Now listening on: {address}","address":"https://localhost:56892","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:00.1980681Z","@mt":"Now listening on: {address}","address":"http://localhost:56893","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:00.3244293Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:00.3248214Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:00.3249487Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:19.9943975Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":52.8268,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:40.0844885Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":9.1036,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:43.9538851Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":39,\"temperatureF\":102,\"summary\":\"Bracing\",\"test\":\"123\"}条","@tr":"********************************","@sp":"975835cf06a09fd1","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":39,\"temperatureF\":102,\"summary\":\"Bracing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"975835cf06a09fd1","TraceId":"********************************","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000001","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:43.9554627Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":39,\"temperatureF\":102,\"summary\":\"Bracing\",\"test\":\"123\"}条","@l":"Error","@tr":"********************************","@sp":"975835cf06a09fd1","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"975835cf06a09fd1","TraceId":"********************************","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000001","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:45.1607096Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":47.3278,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:46.2323756Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-7,\"temperatureF\":20,\"summary\":\"Chilly\",\"test\":\"123\"}条","@tr":"2700ae099de937a90b364e15cfb51d75","@sp":"9426f858a37ef59c","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-7,\"temperatureF\":20,\"summary\":\"Chilly\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"9426f858a37ef59c","TraceId":"2700ae099de937a90b364e15cfb51d75","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000003","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:46.2325856Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-7,\"temperatureF\":20,\"summary\":\"Chilly\",\"test\":\"123\"}条","@l":"Error","@tr":"2700ae099de937a90b364e15cfb51d75","@sp":"9426f858a37ef59c","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"9426f858a37ef59c","TraceId":"2700ae099de937a90b364e15cfb51d75","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000003","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:46.4106972Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-9,\"temperatureF\":16,\"summary\":\"Balmy\",\"test\":\"123\"}条","@tr":"f7c610921ee459a080d3d4ec325bd2ea","@sp":"5c2a61a6d6f46454","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-9,\"temperatureF\":16,\"summary\":\"Balmy\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"5c2a61a6d6f46454","TraceId":"f7c610921ee459a080d3d4ec325bd2ea","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000005","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:46.4109716Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-9,\"temperatureF\":16,\"summary\":\"Balmy\",\"test\":\"123\"}条","@l":"Error","@tr":"f7c610921ee459a080d3d4ec325bd2ea","@sp":"5c2a61a6d6f46454","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"5c2a61a6d6f46454","TraceId":"f7c610921ee459a080d3d4ec325bd2ea","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000005","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:46.6555305Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":11,\"temperatureF\":51,\"summary\":\"Freezing\",\"test\":\"123\"}条","@tr":"57d0c044456c873e5cbf32d0c9bd4fba","@sp":"29899f2756b7da5d","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":11,\"temperatureF\":51,\"summary\":\"Freezing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"29899f2756b7da5d","TraceId":"57d0c044456c873e5cbf32d0c9bd4fba","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000007","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:46.6558653Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":11,\"temperatureF\":51,\"summary\":\"Freezing\",\"test\":\"123\"}条","@l":"Error","@tr":"57d0c044456c873e5cbf32d0c9bd4fba","@sp":"29899f2756b7da5d","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"29899f2756b7da5d","TraceId":"57d0c044456c873e5cbf32d0c9bd4fba","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000007","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:46.8748762Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":44,\"temperatureF\":111,\"summary\":\"Balmy\",\"test\":\"123\"}条","@tr":"f1bae05c1e0571796db5b319b0e4a4a8","@sp":"e7ebbaa5860d60f0","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":44,\"temperatureF\":111,\"summary\":\"Balmy\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"e7ebbaa5860d60f0","TraceId":"f1bae05c1e0571796db5b319b0e4a4a8","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000009","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:46.8752545Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":44,\"temperatureF\":111,\"summary\":\"Balmy\",\"test\":\"123\"}条","@l":"Error","@tr":"f1bae05c1e0571796db5b319b0e4a4a8","@sp":"e7ebbaa5860d60f0","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"e7ebbaa5860d60f0","TraceId":"f1bae05c1e0571796db5b319b0e4a4a8","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000009","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:47.0809715Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":26,\"temperatureF\":78,\"summary\":\"Balmy\",\"test\":\"123\"}条","@tr":"774c69dedbecab01cd6fbc8ee955d6c0","@sp":"1dcb04f27364facf","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":26,\"temperatureF\":78,\"summary\":\"Balmy\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"1dcb04f27364facf","TraceId":"774c69dedbecab01cd6fbc8ee955d6c0","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000000B","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:47.0812019Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":26,\"temperatureF\":78,\"summary\":\"Balmy\",\"test\":\"123\"}条","@l":"Error","@tr":"774c69dedbecab01cd6fbc8ee955d6c0","@sp":"1dcb04f27364facf","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"1dcb04f27364facf","TraceId":"774c69dedbecab01cd6fbc8ee955d6c0","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000000B","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:47.2731739Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":11,\"temperatureF\":51,\"summary\":\"Scorching\",\"test\":\"123\"}条","@tr":"51e12706068261069bdfae5b14c2057c","@sp":"c87a79ebac919a3a","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":11,\"temperatureF\":51,\"summary\":\"Scorching\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"c87a79ebac919a3a","TraceId":"51e12706068261069bdfae5b14c2057c","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000000D","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:47.3173870Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":11,\"temperatureF\":51,\"summary\":\"Scorching\",\"test\":\"123\"}条","@l":"Error","@tr":"51e12706068261069bdfae5b14c2057c","@sp":"c87a79ebac919a3a","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"c87a79ebac919a3a","TraceId":"51e12706068261069bdfae5b14c2057c","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000000D","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:47.4720783Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":16,\"temperatureF\":60,\"summary\":\"Chilly\",\"test\":\"123\"}条","@tr":"1e30c470e65cdb49dd2c6e08fe1d568c","@sp":"004853ea161be4e4","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":16,\"temperatureF\":60,\"summary\":\"Chilly\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"004853ea161be4e4","TraceId":"1e30c470e65cdb49dd2c6e08fe1d568c","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000000F","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:47.4723596Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":16,\"temperatureF\":60,\"summary\":\"Chilly\",\"test\":\"123\"}条","@l":"Error","@tr":"1e30c470e65cdb49dd2c6e08fe1d568c","@sp":"004853ea161be4e4","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"004853ea161be4e4","TraceId":"1e30c470e65cdb49dd2c6e08fe1d568c","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000000F","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:47.6510472Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":1,\"temperatureF\":33,\"summary\":\"Freezing\",\"test\":\"123\"}条","@tr":"23fcfd5ea8ec29d1cf5035f57f5f35c3","@sp":"ed13647fbb3a2b29","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":1,\"temperatureF\":33,\"summary\":\"Freezing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"ed13647fbb3a2b29","TraceId":"23fcfd5ea8ec29d1cf5035f57f5f35c3","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000011","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:47.6513540Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":1,\"temperatureF\":33,\"summary\":\"Freezing\",\"test\":\"123\"}条","@l":"Error","@tr":"23fcfd5ea8ec29d1cf5035f57f5f35c3","@sp":"ed13647fbb3a2b29","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"ed13647fbb3a2b29","TraceId":"23fcfd5ea8ec29d1cf5035f57f5f35c3","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000011","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:47.8316172Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":33,\"temperatureF\":91,\"summary\":\"Chilly\",\"test\":\"123\"}条","@tr":"998979031c8263a4369cbc183bd922e9","@sp":"4b2b64caa5c6d43a","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":33,\"temperatureF\":91,\"summary\":\"Chilly\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"4b2b64caa5c6d43a","TraceId":"998979031c8263a4369cbc183bd922e9","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000013","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:47.8319522Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":33,\"temperatureF\":91,\"summary\":\"Chilly\",\"test\":\"123\"}条","@l":"Error","@tr":"998979031c8263a4369cbc183bd922e9","@sp":"4b2b64caa5c6d43a","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"4b2b64caa5c6d43a","TraceId":"998979031c8263a4369cbc183bd922e9","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000013","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:48.0143576Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":22,\"temperatureF\":71,\"summary\":\"Bracing\",\"test\":\"123\"}条","@tr":"8694344e372d38dc6434715ee3f9c609","@sp":"1feda477343b0685","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":22,\"temperatureF\":71,\"summary\":\"Bracing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"1feda477343b0685","TraceId":"8694344e372d38dc6434715ee3f9c609","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000015","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:48.0147209Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":22,\"temperatureF\":71,\"summary\":\"Bracing\",\"test\":\"123\"}条","@l":"Error","@tr":"8694344e372d38dc6434715ee3f9c609","@sp":"1feda477343b0685","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"1feda477343b0685","TraceId":"8694344e372d38dc6434715ee3f9c609","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000015","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:48.1840275Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-19,\"temperatureF\":-2,\"summary\":\"Cool\",\"test\":\"123\"}条","@tr":"09fd9b9ac195e84bc1bd764356942b9e","@sp":"bb0398ff04f47afe","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-19,\"temperatureF\":-2,\"summary\":\"Cool\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"bb0398ff04f47afe","TraceId":"09fd9b9ac195e84bc1bd764356942b9e","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000017","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:48.1842582Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-19,\"temperatureF\":-2,\"summary\":\"Cool\",\"test\":\"123\"}条","@l":"Error","@tr":"09fd9b9ac195e84bc1bd764356942b9e","@sp":"bb0398ff04f47afe","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"bb0398ff04f47afe","TraceId":"09fd9b9ac195e84bc1bd764356942b9e","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000017","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:48.3690039Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":17,\"temperatureF\":62,\"summary\":\"Chilly\",\"test\":\"123\"}条","@tr":"5638bc9e00c97c0558abb26fbfd56e9c","@sp":"b18455babb021e5f","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":17,\"temperatureF\":62,\"summary\":\"Chilly\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"b18455babb021e5f","TraceId":"5638bc9e00c97c0558abb26fbfd56e9c","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000019","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:48.3693862Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":17,\"temperatureF\":62,\"summary\":\"Chilly\",\"test\":\"123\"}条","@l":"Error","@tr":"5638bc9e00c97c0558abb26fbfd56e9c","@sp":"b18455babb021e5f","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"b18455babb021e5f","TraceId":"5638bc9e00c97c0558abb26fbfd56e9c","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000019","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:48.5364758Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":20,\"temperatureF\":67,\"summary\":\"Balmy\",\"test\":\"123\"}条","@tr":"40062ba13d9586be60db52d8948600aa","@sp":"42dc7df32893e2a3","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":20,\"temperatureF\":67,\"summary\":\"Balmy\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"42dc7df32893e2a3","TraceId":"40062ba13d9586be60db52d8948600aa","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000001B","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:48.5366758Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":20,\"temperatureF\":67,\"summary\":\"Balmy\",\"test\":\"123\"}条","@l":"Error","@tr":"40062ba13d9586be60db52d8948600aa","@sp":"42dc7df32893e2a3","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"42dc7df32893e2a3","TraceId":"40062ba13d9586be60db52d8948600aa","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000001B","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:48.7194805Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":28,\"temperatureF\":82,\"summary\":\"Freezing\",\"test\":\"123\"}条","@tr":"88ccc0e3eeab5d97b137add5d97b0e40","@sp":"396fa998522a1766","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":28,\"temperatureF\":82,\"summary\":\"Freezing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"396fa998522a1766","TraceId":"88ccc0e3eeab5d97b137add5d97b0e40","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000001D","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:48.7196083Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":28,\"temperatureF\":82,\"summary\":\"Freezing\",\"test\":\"123\"}条","@l":"Error","@tr":"88ccc0e3eeab5d97b137add5d97b0e40","@sp":"396fa998522a1766","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"396fa998522a1766","TraceId":"88ccc0e3eeab5d97b137add5d97b0e40","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000001D","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:48.9082775Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":35,\"temperatureF\":94,\"summary\":\"Freezing\",\"test\":\"123\"}条","@tr":"74aab12a5258310f43944eeb6d3b54a1","@sp":"785d7dda30da82d7","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":35,\"temperatureF\":94,\"summary\":\"Freezing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"785d7dda30da82d7","TraceId":"74aab12a5258310f43944eeb6d3b54a1","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000001F","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:48.9084785Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":35,\"temperatureF\":94,\"summary\":\"Freezing\",\"test\":\"123\"}条","@l":"Error","@tr":"74aab12a5258310f43944eeb6d3b54a1","@sp":"785d7dda30da82d7","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"785d7dda30da82d7","TraceId":"74aab12a5258310f43944eeb6d3b54a1","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000001F","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:49.0956222Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":41,\"temperatureF\":105,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@tr":"003935d5d87e9921b09165c2ab272776","@sp":"2530bdeb179089ca","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":41,\"temperatureF\":105,\"summary\":\"Sweltering\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"2530bdeb179089ca","TraceId":"003935d5d87e9921b09165c2ab272776","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000021","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:49.0957519Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":41,\"temperatureF\":105,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@l":"Error","@tr":"003935d5d87e9921b09165c2ab272776","@sp":"2530bdeb179089ca","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"2530bdeb179089ca","TraceId":"003935d5d87e9921b09165c2ab272776","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000021","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:49.2872942Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-3,\"temperatureF\":27,\"summary\":\"Hot\",\"test\":\"123\"}条","@tr":"91b87b22fc4aca60dd8e1e367ceb7aca","@sp":"93e15f54f6700652","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-3,\"temperatureF\":27,\"summary\":\"Hot\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"93e15f54f6700652","TraceId":"91b87b22fc4aca60dd8e1e367ceb7aca","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000023","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:49.2875872Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-3,\"temperatureF\":27,\"summary\":\"Hot\",\"test\":\"123\"}条","@l":"Error","@tr":"91b87b22fc4aca60dd8e1e367ceb7aca","@sp":"93e15f54f6700652","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"93e15f54f6700652","TraceId":"91b87b22fc4aca60dd8e1e367ceb7aca","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000023","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:49.4753044Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":39,\"temperatureF\":102,\"summary\":\"Hot\",\"test\":\"123\"}条","@tr":"a41b4fef55552f124b967890034d232d","@sp":"5d9f8c897b095243","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":39,\"temperatureF\":102,\"summary\":\"Hot\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"5d9f8c897b095243","TraceId":"a41b4fef55552f124b967890034d232d","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000025","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:49.4755114Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":39,\"temperatureF\":102,\"summary\":\"Hot\",\"test\":\"123\"}条","@l":"Error","@tr":"a41b4fef55552f124b967890034d232d","@sp":"5d9f8c897b095243","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"5d9f8c897b095243","TraceId":"a41b4fef55552f124b967890034d232d","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000025","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:49.6601335Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-10,\"temperatureF\":15,\"summary\":\"Cool\",\"test\":\"123\"}条","@tr":"7442bcd20e3b99cd9c6e9489a96adb56","@sp":"1be1257ed364db4e","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-10,\"temperatureF\":15,\"summary\":\"Cool\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"1be1257ed364db4e","TraceId":"7442bcd20e3b99cd9c6e9489a96adb56","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000027","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:49.6603108Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-10,\"temperatureF\":15,\"summary\":\"Cool\",\"test\":\"123\"}条","@l":"Error","@tr":"7442bcd20e3b99cd9c6e9489a96adb56","@sp":"1be1257ed364db4e","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"1be1257ed364db4e","TraceId":"7442bcd20e3b99cd9c6e9489a96adb56","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000027","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:49.8529897Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-6,\"temperatureF\":22,\"summary\":\"Balmy\",\"test\":\"123\"}条","@tr":"c20e0d279d46345fc2352f2e20eda146","@sp":"06312a07606ebdfb","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-6,\"temperatureF\":22,\"summary\":\"Balmy\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"06312a07606ebdfb","TraceId":"c20e0d279d46345fc2352f2e20eda146","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000029","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:49.8531652Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-6,\"temperatureF\":22,\"summary\":\"Balmy\",\"test\":\"123\"}条","@l":"Error","@tr":"c20e0d279d46345fc2352f2e20eda146","@sp":"06312a07606ebdfb","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"06312a07606ebdfb","TraceId":"c20e0d279d46345fc2352f2e20eda146","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000029","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:50.0282831Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-16,\"temperatureF\":4,\"summary\":\"Bracing\",\"test\":\"123\"}条","@tr":"15ba773114dbc165d6dcf023d1f9192c","@sp":"cb5ac0cf1b4bb95e","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-16,\"temperatureF\":4,\"summary\":\"Bracing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"cb5ac0cf1b4bb95e","TraceId":"15ba773114dbc165d6dcf023d1f9192c","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000002B","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:50.0284743Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-16,\"temperatureF\":4,\"summary\":\"Bracing\",\"test\":\"123\"}条","@l":"Error","@tr":"15ba773114dbc165d6dcf023d1f9192c","@sp":"cb5ac0cf1b4bb95e","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"cb5ac0cf1b4bb95e","TraceId":"15ba773114dbc165d6dcf023d1f9192c","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000002B","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":25,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:50.2072905Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":31,\"temperatureF\":87,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@tr":"6821555eb45a91a562c2cc91d49b927b","@sp":"e2fc5d75c5595adc","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":31,\"temperatureF\":87,\"summary\":\"Sweltering\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"e2fc5d75c5595adc","TraceId":"6821555eb45a91a562c2cc91d49b927b","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000002D","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:50.2074757Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":31,\"temperatureF\":87,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@l":"Error","@tr":"6821555eb45a91a562c2cc91d49b927b","@sp":"e2fc5d75c5595adc","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"e2fc5d75c5595adc","TraceId":"6821555eb45a91a562c2cc91d49b927b","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000002D","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:50.2215789Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":50.4546,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:50.3905124Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":45,\"temperatureF\":112,\"summary\":\"Warm\",\"test\":\"123\"}条","@tr":"61182e65988640546bcdc291081cb07a","@sp":"6e04b70db6e81659","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":45,\"temperatureF\":112,\"summary\":\"Warm\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"6e04b70db6e81659","TraceId":"61182e65988640546bcdc291081cb07a","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000002F","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:50.3908426Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":45,\"temperatureF\":112,\"summary\":\"Warm\",\"test\":\"123\"}条","@l":"Error","@tr":"61182e65988640546bcdc291081cb07a","@sp":"6e04b70db6e81659","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"6e04b70db6e81659","TraceId":"61182e65988640546bcdc291081cb07a","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:0000002F","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:50.5790211Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-4,\"temperatureF\":25,\"summary\":\"Cool\",\"test\":\"123\"}条","@tr":"ccf2b1392a7a659b5d76b1cd52d4652e","@sp":"50587acd89e3cca0","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-4,\"temperatureF\":25,\"summary\":\"Cool\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"50587acd89e3cca0","TraceId":"ccf2b1392a7a659b5d76b1cd52d4652e","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000031","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:50.5792019Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-4,\"temperatureF\":25,\"summary\":\"Cool\",\"test\":\"123\"}条","@l":"Error","@tr":"ccf2b1392a7a659b5d76b1cd52d4652e","@sp":"50587acd89e3cca0","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"50587acd89e3cca0","TraceId":"ccf2b1392a7a659b5d76b1cd52d4652e","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000031","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:50.7469354Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":23,\"temperatureF\":73,\"summary\":\"Hot\",\"test\":\"123\"}条","@tr":"c0293f1469babf9390222b879f3b9ada","@sp":"f833c17c472d4852","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":23,\"temperatureF\":73,\"summary\":\"Hot\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"f833c17c472d4852","TraceId":"c0293f1469babf9390222b879f3b9ada","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000033","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:50.7471045Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":23,\"temperatureF\":73,\"summary\":\"Hot\",\"test\":\"123\"}条","@l":"Error","@tr":"c0293f1469babf9390222b879f3b9ada","@sp":"f833c17c472d4852","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"f833c17c472d4852","TraceId":"c0293f1469babf9390222b879f3b9ada","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000033","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:50.9279442Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":34,\"temperatureF\":93,\"summary\":\"Cool\",\"test\":\"123\"}条","@tr":"7cef9749697833629e8990baaf741ef1","@sp":"95287df3f1ab814b","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":34,\"temperatureF\":93,\"summary\":\"Cool\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"95287df3f1ab814b","TraceId":"7cef9749697833629e8990baaf741ef1","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000035","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:50.9281476Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":34,\"temperatureF\":93,\"summary\":\"Cool\",\"test\":\"123\"}条","@l":"Error","@tr":"7cef9749697833629e8990baaf741ef1","@sp":"95287df3f1ab814b","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"95287df3f1ab814b","TraceId":"7cef9749697833629e8990baaf741ef1","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8P","RequestId":"0HNBO1CMGNC8P:00000035","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:51.1392747Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":45,\"temperatureF\":112,\"summary\":\"Mild\",\"test\":\"123\"}条","@tr":"ccd4854b1d3973e778cd39d6277a5f67","@sp":"1264fa6cc5efed1a","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":45,\"temperatureF\":112,\"summary\":\"Mild\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"1264fa6cc5efed1a","TraceId":"ccd4854b1d3973e778cd39d6277a5f67","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8Q","RequestId":"0HNBO1CMGNC8Q:00000001","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:51.1394964Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":45,\"temperatureF\":112,\"summary\":\"Mild\",\"test\":\"123\"}条","@l":"Error","@tr":"ccd4854b1d3973e778cd39d6277a5f67","@sp":"1264fa6cc5efed1a","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"1264fa6cc5efed1a","TraceId":"ccd4854b1d3973e778cd39d6277a5f67","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8Q","RequestId":"0HNBO1CMGNC8Q:00000001","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:51.2727039Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":33,\"temperatureF\":91,\"summary\":\"Hot\",\"test\":\"123\"}条","@tr":"390b408d9b065c62033864fba71f758c","@sp":"9b197d437b678294","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":33,\"temperatureF\":91,\"summary\":\"Hot\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"9b197d437b678294","TraceId":"390b408d9b065c62033864fba71f758c","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8Q","RequestId":"0HNBO1CMGNC8Q:00000003","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:51.2728895Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":33,\"temperatureF\":91,\"summary\":\"Hot\",\"test\":\"123\"}条","@l":"Error","@tr":"390b408d9b065c62033864fba71f758c","@sp":"9b197d437b678294","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"9b197d437b678294","TraceId":"390b408d9b065c62033864fba71f758c","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8Q","RequestId":"0HNBO1CMGNC8Q:00000003","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":26,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:51.4463651Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":9,\"temperatureF\":48,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@tr":"a3eb3c631f44bdb28220be3bcbff9689","@sp":"c143c670fef6a6be","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":9,\"temperatureF\":48,\"summary\":\"Sweltering\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"c143c670fef6a6be","TraceId":"a3eb3c631f44bdb28220be3bcbff9689","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8Q","RequestId":"0HNBO1CMGNC8Q:00000005","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:51.4466229Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":9,\"temperatureF\":48,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@l":"Error","@tr":"a3eb3c631f44bdb28220be3bcbff9689","@sp":"c143c670fef6a6be","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"c143c670fef6a6be","TraceId":"a3eb3c631f44bdb28220be3bcbff9689","ParentId":"0000000000000000","ConnectionId":"0HNBO1CMGNC8Q","RequestId":"0HNBO1CMGNC8Q:00000005","RequestPath":"/WeatherForecast2","ActionId":"f1e30651-6799-4559-9cf3-2702dad702c3","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":8,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:39:55.2803772Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":44.9702,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:41:58.7418831Z","@mt":"Now listening on: {address}","address":"https://localhost:59239","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:41:58.7987181Z","@mt":"Now listening on: {address}","address":"http://localhost:59240","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:41:58.9084845Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:41:58.9087826Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:41:58.9088780Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:42:03.4656485Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":42.8626,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:42:18.5432601Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":22.7079,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:42:23.6025546Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":43.6037,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:44:51.6594456Z","@mt":"Now listening on: {address}","address":"https://localhost:61709","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:44:51.7155967Z","@mt":"Now listening on: {address}","address":"http://localhost:61710","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:44:51.8058785Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:44:51.8062328Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:44:51.8064291Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:44:56.3771878Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":46.4027,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:06.4771016Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":47.3982,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:08.9645801Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-16,\"temperatureF\":4,\"summary\":\"Hot\",\"test\":\"123\"}条","@tr":"dda93c03089aa83ae154cf36db07918e","@sp":"a5fb8ed97df19a9a","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-16,\"temperatureF\":4,\"summary\":\"Hot\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"a5fb8ed97df19a9a","TraceId":"dda93c03089aa83ae154cf36db07918e","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000001","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:08.9662459Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-16,\"temperatureF\":4,\"summary\":\"Hot\",\"test\":\"123\"}条","@l":"Error","@tr":"dda93c03089aa83ae154cf36db07918e","@sp":"a5fb8ed97df19a9a","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"a5fb8ed97df19a9a","TraceId":"dda93c03089aa83ae154cf36db07918e","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000001","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:10.3110643Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-7,\"temperatureF\":20,\"summary\":\"Bracing\",\"test\":\"123\"}条","@tr":"390ab56c4b2a3994404eac7adb9976a8","@sp":"92f9f95dc53dc9ac","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-7,\"temperatureF\":20,\"summary\":\"Bracing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"92f9f95dc53dc9ac","TraceId":"390ab56c4b2a3994404eac7adb9976a8","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000003","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:10.3113296Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-7,\"temperatureF\":20,\"summary\":\"Bracing\",\"test\":\"123\"}条","@l":"Error","@tr":"390ab56c4b2a3994404eac7adb9976a8","@sp":"92f9f95dc53dc9ac","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"92f9f95dc53dc9ac","TraceId":"390ab56c4b2a3994404eac7adb9976a8","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000003","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:11.3381362Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":23,\"temperatureF\":73,\"summary\":\"Warm\",\"test\":\"123\"}条","@tr":"a85a2404af7b7e6b755069147e50d2c0","@sp":"2fd6630870693031","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":23,\"temperatureF\":73,\"summary\":\"Warm\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"2fd6630870693031","TraceId":"a85a2404af7b7e6b755069147e50d2c0","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000005","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:11.3385056Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":23,\"temperatureF\":73,\"summary\":\"Warm\",\"test\":\"123\"}条","@l":"Error","@tr":"a85a2404af7b7e6b755069147e50d2c0","@sp":"2fd6630870693031","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"2fd6630870693031","TraceId":"a85a2404af7b7e6b755069147e50d2c0","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000005","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:11.5457690Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":49.6838,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:11.9526696Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":15,\"temperatureF\":58,\"summary\":\"Chilly\",\"test\":\"123\"}条","@tr":"25c5d836d63de2f0527c6f229eaa9816","@sp":"a8a310e78fd35861","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":15,\"temperatureF\":58,\"summary\":\"Chilly\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"a8a310e78fd35861","TraceId":"25c5d836d63de2f0527c6f229eaa9816","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000007","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:11.9529069Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":15,\"temperatureF\":58,\"summary\":\"Chilly\",\"test\":\"123\"}条","@l":"Error","@tr":"25c5d836d63de2f0527c6f229eaa9816","@sp":"a8a310e78fd35861","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"a8a310e78fd35861","TraceId":"25c5d836d63de2f0527c6f229eaa9816","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000007","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:12.4124767Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":28,\"temperatureF\":82,\"summary\":\"Cool\",\"test\":\"123\"}条","@tr":"42eac01a851b69b596de33c1b29793a9","@sp":"d0b49bd80cbf8dae","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":28,\"temperatureF\":82,\"summary\":\"Cool\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"d0b49bd80cbf8dae","TraceId":"42eac01a851b69b596de33c1b29793a9","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000009","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:12.4127386Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":28,\"temperatureF\":82,\"summary\":\"Cool\",\"test\":\"123\"}条","@l":"Error","@tr":"42eac01a851b69b596de33c1b29793a9","@sp":"d0b49bd80cbf8dae","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"d0b49bd80cbf8dae","TraceId":"42eac01a851b69b596de33c1b29793a9","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000009","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:12.8523188Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-16,\"temperatureF\":4,\"summary\":\"Hot\",\"test\":\"123\"}条","@tr":"98b7c4754a0576f6080eb642940fa608","@sp":"ea11a48f3294f3f3","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-16,\"temperatureF\":4,\"summary\":\"Hot\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"ea11a48f3294f3f3","TraceId":"98b7c4754a0576f6080eb642940fa608","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:0000000B","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:12.8525754Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-16,\"temperatureF\":4,\"summary\":\"Hot\",\"test\":\"123\"}条","@l":"Error","@tr":"98b7c4754a0576f6080eb642940fa608","@sp":"ea11a48f3294f3f3","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"ea11a48f3294f3f3","TraceId":"98b7c4754a0576f6080eb642940fa608","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:0000000B","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:13.4483944Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-8,\"temperatureF\":18,\"summary\":\"Mild\",\"test\":\"123\"}条","@tr":"6a2e8226506d58ceccc10787398fb4d5","@sp":"835036ed349a0042","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-8,\"temperatureF\":18,\"summary\":\"Mild\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"835036ed349a0042","TraceId":"6a2e8226506d58ceccc10787398fb4d5","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:0000000D","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:13.4488046Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-8,\"temperatureF\":18,\"summary\":\"Mild\",\"test\":\"123\"}条","@l":"Error","@tr":"6a2e8226506d58ceccc10787398fb4d5","@sp":"835036ed349a0042","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"835036ed349a0042","TraceId":"6a2e8226506d58ceccc10787398fb4d5","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:0000000D","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:13.6942549Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":4,\"temperatureF\":39,\"summary\":\"Freezing\",\"test\":\"123\"}条","@tr":"dc8dc11fcb4586525922377365d473ac","@sp":"87b70b122d3871ff","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":4,\"temperatureF\":39,\"summary\":\"Freezing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"87b70b122d3871ff","TraceId":"dc8dc11fcb4586525922377365d473ac","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:0000000F","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:13.6945185Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":4,\"temperatureF\":39,\"summary\":\"Freezing\",\"test\":\"123\"}条","@l":"Error","@tr":"dc8dc11fcb4586525922377365d473ac","@sp":"87b70b122d3871ff","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"87b70b122d3871ff","TraceId":"dc8dc11fcb4586525922377365d473ac","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:0000000F","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:13.8938041Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":32,\"temperatureF\":89,\"summary\":\"Hot\",\"test\":\"123\"}条","@tr":"9f44d3e4c54bef76c2bb3bf3a9fe07c9","@sp":"7060271bec6c7397","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":32,\"temperatureF\":89,\"summary\":\"Hot\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"7060271bec6c7397","TraceId":"9f44d3e4c54bef76c2bb3bf3a9fe07c9","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000011","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:13.8940636Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":32,\"temperatureF\":89,\"summary\":\"Hot\",\"test\":\"123\"}条","@l":"Error","@tr":"9f44d3e4c54bef76c2bb3bf3a9fe07c9","@sp":"7060271bec6c7397","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"7060271bec6c7397","TraceId":"9f44d3e4c54bef76c2bb3bf3a9fe07c9","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000011","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:14.0964647Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":20,\"temperatureF\":67,\"summary\":\"Cool\",\"test\":\"123\"}条","@tr":"19081680f6f5c440ded437d553c9fca8","@sp":"c07b608598034414","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":20,\"temperatureF\":67,\"summary\":\"Cool\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"c07b608598034414","TraceId":"19081680f6f5c440ded437d553c9fca8","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000013","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:14.0967580Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":20,\"temperatureF\":67,\"summary\":\"Cool\",\"test\":\"123\"}条","@l":"Error","@tr":"19081680f6f5c440ded437d553c9fca8","@sp":"c07b608598034414","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"c07b608598034414","TraceId":"19081680f6f5c440ded437d553c9fca8","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000013","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:14.2854724Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":20,\"temperatureF\":67,\"summary\":\"Warm\",\"test\":\"123\"}条","@tr":"f859fd794f10e62d70c886c181e1e208","@sp":"5e9a4e90d1f90e22","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":20,\"temperatureF\":67,\"summary\":\"Warm\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"5e9a4e90d1f90e22","TraceId":"f859fd794f10e62d70c886c181e1e208","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000015","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:14.2857635Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":20,\"temperatureF\":67,\"summary\":\"Warm\",\"test\":\"123\"}条","@l":"Error","@tr":"f859fd794f10e62d70c886c181e1e208","@sp":"5e9a4e90d1f90e22","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"5e9a4e90d1f90e22","TraceId":"f859fd794f10e62d70c886c181e1e208","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000015","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:14.4807657Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-15,\"temperatureF\":6,\"summary\":\"Bracing\",\"test\":\"123\"}条","@tr":"f6a6527231cd67b886ef0ff62711eb39","@sp":"22a87de4672feabe","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-15,\"temperatureF\":6,\"summary\":\"Bracing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"22a87de4672feabe","TraceId":"f6a6527231cd67b886ef0ff62711eb39","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000017","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:14.4809938Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-15,\"temperatureF\":6,\"summary\":\"Bracing\",\"test\":\"123\"}条","@l":"Error","@tr":"f6a6527231cd67b886ef0ff62711eb39","@sp":"22a87de4672feabe","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"22a87de4672feabe","TraceId":"f6a6527231cd67b886ef0ff62711eb39","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUQ","RequestId":"0HNBO1FQR9RUQ:00000017","RequestPath":"/WeatherForecast2","ActionId":"1d685056-742b-43fd-bbab-3ea56b02a1b4","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T08:45:16.6062774Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":44.5122,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:04:43.7656237Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":7.373,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:04:48.8253838Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":43.3802,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:04:53.8756192Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":45.1681,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:13.9333384Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":6.3119,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:14.7873375Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":6,\"temperatureF\":42,\"summary\":\"Hot\",\"test\":\"123\"}条","@tr":"d70d321eb330968727679e672a46a8df","@sp":"ef966980c9f926a7","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":6,\"temperatureF\":42,\"summary\":\"Hot\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"ef966980c9f926a7","TraceId":"d70d321eb330968727679e672a46a8df","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUV","RequestId":"0HNBO1FQR9RUV:00000001","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:14.7875079Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":6,\"temperatureF\":42,\"summary\":\"Hot\",\"test\":\"123\"}条","@l":"Error","@tr":"d70d321eb330968727679e672a46a8df","@sp":"ef966980c9f926a7","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"ef966980c9f926a7","TraceId":"d70d321eb330968727679e672a46a8df","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUV","RequestId":"0HNBO1FQR9RUV:00000001","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:15.7796852Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":22,\"temperatureF\":71,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@tr":"7d11789c52f101f76013eb1eee6fa3ad","@sp":"2f5d69535b644328","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":22,\"temperatureF\":71,\"summary\":\"Sweltering\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"2f5d69535b644328","TraceId":"7d11789c52f101f76013eb1eee6fa3ad","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUV","RequestId":"0HNBO1FQR9RUV:00000003","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":32,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:15.7800375Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":22,\"temperatureF\":71,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@l":"Error","@tr":"7d11789c52f101f76013eb1eee6fa3ad","@sp":"2f5d69535b644328","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"2f5d69535b644328","TraceId":"7d11789c52f101f76013eb1eee6fa3ad","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUV","RequestId":"0HNBO1FQR9RUV:00000003","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":32,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:16.5792964Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-6,\"temperatureF\":22,\"summary\":\"Freezing\",\"test\":\"123\"}条","@tr":"08120c857336dd33325f56a59b3e9ce0","@sp":"dbe2b0d7fb2cd64c","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-6,\"temperatureF\":22,\"summary\":\"Freezing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"dbe2b0d7fb2cd64c","TraceId":"08120c857336dd33325f56a59b3e9ce0","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUV","RequestId":"0HNBO1FQR9RUV:00000005","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:16.5794224Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-6,\"temperatureF\":22,\"summary\":\"Freezing\",\"test\":\"123\"}条","@l":"Error","@tr":"08120c857336dd33325f56a59b3e9ce0","@sp":"dbe2b0d7fb2cd64c","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"dbe2b0d7fb2cd64c","TraceId":"08120c857336dd33325f56a59b3e9ce0","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUV","RequestId":"0HNBO1FQR9RUV:00000005","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:17.0883691Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":22,\"temperatureF\":71,\"summary\":\"Bracing\",\"test\":\"123\"}条","@tr":"968c3c8d290313dad68e8b443b19dd73","@sp":"be7a73dd8a448a4d","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":22,\"temperatureF\":71,\"summary\":\"Bracing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"be7a73dd8a448a4d","TraceId":"968c3c8d290313dad68e8b443b19dd73","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUV","RequestId":"0HNBO1FQR9RUV:00000007","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":32,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:17.0885145Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":22,\"temperatureF\":71,\"summary\":\"Bracing\",\"test\":\"123\"}条","@l":"Error","@tr":"968c3c8d290313dad68e8b443b19dd73","@sp":"be7a73dd8a448a4d","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"be7a73dd8a448a4d","TraceId":"968c3c8d290313dad68e8b443b19dd73","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUV","RequestId":"0HNBO1FQR9RUV:00000007","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":32,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:17.5859022Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":42,\"temperatureF\":107,\"summary\":\"Scorching\",\"test\":\"123\"}条","@tr":"7f491c20a1bf1858134c2051831d47fb","@sp":"e0a1f9ac82563f81","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":42,\"temperatureF\":107,\"summary\":\"Scorching\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"e0a1f9ac82563f81","TraceId":"7f491c20a1bf1858134c2051831d47fb","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUV","RequestId":"0HNBO1FQR9RUV:00000009","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:17.5861435Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":42,\"temperatureF\":107,\"summary\":\"Scorching\",\"test\":\"123\"}条","@l":"Error","@tr":"7f491c20a1bf1858134c2051831d47fb","@sp":"e0a1f9ac82563f81","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"e0a1f9ac82563f81","TraceId":"7f491c20a1bf1858134c2051831d47fb","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUV","RequestId":"0HNBO1FQR9RUV:00000009","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:17.8757612Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":52,\"temperatureF\":125,\"summary\":\"Freezing\",\"test\":\"123\"}条","@tr":"06d7e08e01a5b0a109f1ec5dae58c2db","@sp":"d44e9732565e2e91","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":52,\"temperatureF\":125,\"summary\":\"Freezing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"d44e9732565e2e91","TraceId":"06d7e08e01a5b0a109f1ec5dae58c2db","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUV","RequestId":"0HNBO1FQR9RUV:0000000B","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":30,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:17.8758972Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":52,\"temperatureF\":125,\"summary\":\"Freezing\",\"test\":\"123\"}条","@l":"Error","@tr":"06d7e08e01a5b0a109f1ec5dae58c2db","@sp":"d44e9732565e2e91","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"d44e9732565e2e91","TraceId":"06d7e08e01a5b0a109f1ec5dae58c2db","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RUV","RequestId":"0HNBO1FQR9RUV:0000000B","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":30,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:18.3188982Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":38,\"temperatureF\":100,\"summary\":\"Scorching\",\"test\":\"123\"}条","@tr":"bd8a72792487b9bcca8d1135cc6160bd","@sp":"c803db39dc2d7bdc","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":38,\"temperatureF\":100,\"summary\":\"Scorching\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"c803db39dc2d7bdc","TraceId":"bd8a72792487b9bcca8d1135cc6160bd","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RV0","RequestId":"0HNBO1FQR9RV0:00000001","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":30,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:18.3190273Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":38,\"temperatureF\":100,\"summary\":\"Scorching\",\"test\":\"123\"}条","@l":"Error","@tr":"bd8a72792487b9bcca8d1135cc6160bd","@sp":"c803db39dc2d7bdc","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"c803db39dc2d7bdc","TraceId":"bd8a72792487b9bcca8d1135cc6160bd","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RV0","RequestId":"0HNBO1FQR9RV0:00000001","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":30,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:18.5546281Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":20,\"temperatureF\":67,\"summary\":\"Chilly\",\"test\":\"123\"}条","@tr":"70be0a2398fc8303c19b64ab4fe52d99","@sp":"28ba9661ceccd2e3","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":20,\"temperatureF\":67,\"summary\":\"Chilly\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"28ba9661ceccd2e3","TraceId":"70be0a2398fc8303c19b64ab4fe52d99","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RV0","RequestId":"0HNBO1FQR9RV0:00000003","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":30,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:18.5550096Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":20,\"temperatureF\":67,\"summary\":\"Chilly\",\"test\":\"123\"}条","@l":"Error","@tr":"70be0a2398fc8303c19b64ab4fe52d99","@sp":"28ba9661ceccd2e3","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"28ba9661ceccd2e3","TraceId":"70be0a2398fc8303c19b64ab4fe52d99","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RV0","RequestId":"0HNBO1FQR9RV0:00000003","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":30,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:18.7366061Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":34,\"temperatureF\":93,\"summary\":\"Freezing\",\"test\":\"123\"}条","@tr":"f20f105945bba39de24d9436f93d7443","@sp":"82762ae9b3fe8f02","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":34,\"temperatureF\":93,\"summary\":\"Freezing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"82762ae9b3fe8f02","TraceId":"f20f105945bba39de24d9436f93d7443","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RV0","RequestId":"0HNBO1FQR9RV0:00000005","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:18.7367574Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":34,\"temperatureF\":93,\"summary\":\"Freezing\",\"test\":\"123\"}条","@l":"Error","@tr":"f20f105945bba39de24d9436f93d7443","@sp":"82762ae9b3fe8f02","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"82762ae9b3fe8f02","TraceId":"f20f105945bba39de24d9436f93d7443","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RV0","RequestId":"0HNBO1FQR9RV0:00000005","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:18.9279438Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":41,\"temperatureF\":105,\"summary\":\"Mild\",\"test\":\"123\"}条","@tr":"98acbd9ed63e7bbab0959d3a3efcbd9f","@sp":"4d90e99d789f5d26","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":41,\"temperatureF\":105,\"summary\":\"Mild\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"4d90e99d789f5d26","TraceId":"98acbd9ed63e7bbab0959d3a3efcbd9f","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RV0","RequestId":"0HNBO1FQR9RV0:00000007","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":30,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:18.9280543Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":41,\"temperatureF\":105,\"summary\":\"Mild\",\"test\":\"123\"}条","@l":"Error","@tr":"98acbd9ed63e7bbab0959d3a3efcbd9f","@sp":"4d90e99d789f5d26","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"4d90e99d789f5d26","TraceId":"98acbd9ed63e7bbab0959d3a3efcbd9f","ParentId":"0000000000000000","ConnectionId":"0HNBO1FQR9RV0","RequestId":"0HNBO1FQR9RV0:00000007","RequestPath":"/WeatherForecast2","ActionId":"253d177a-740d-49bc-be56-37254c010a01","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":30,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:05:18.9950042Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":48.5369,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:46.4768281Z","@mt":"Now listening on: {address}","address":"https://localhost:62400","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:46.5302743Z","@mt":"Now listening on: {address}","address":"http://localhost:62401","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:46.6399064Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:46.6464481Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:46.6551024Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:51.5134025Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":51.4559,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:52.9241130Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":13,\"temperatureF\":55,\"summary\":\"Freezing\",\"test\":\"123\"}条","@tr":"723299a54f3ae7af74e8caa784fb2163","@sp":"68444304cd57a4ee","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":13,\"temperatureF\":55,\"summary\":\"Freezing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"68444304cd57a4ee","TraceId":"723299a54f3ae7af74e8caa784fb2163","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHE","RequestId":"0HNBO264G3IHE:00000001","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:52.9326798Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":13,\"temperatureF\":55,\"summary\":\"Freezing\",\"test\":\"123\"}条","@l":"Error","@tr":"723299a54f3ae7af74e8caa784fb2163","@sp":"68444304cd57a4ee","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"68444304cd57a4ee","TraceId":"723299a54f3ae7af74e8caa784fb2163","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHE","RequestId":"0HNBO264G3IHE:00000001","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:24:56.6424428Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":54.2757,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:25:51.9808277Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-14,\"temperatureF\":7,\"summary\":\"Bracing\",\"test\":\"123\"}条","@tr":"14fe1b520625a1dba8259444310c0670","@sp":"60f3162f197e4bcc","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-14,\"temperatureF\":7,\"summary\":\"Bracing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"60f3162f197e4bcc","TraceId":"14fe1b520625a1dba8259444310c0670","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHF","RequestId":"0HNBO264G3IHF:00000001","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:25:51.9839985Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-14,\"temperatureF\":7,\"summary\":\"Bracing\",\"test\":\"123\"}条","@l":"Error","@tr":"14fe1b520625a1dba8259444310c0670","@sp":"60f3162f197e4bcc","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"60f3162f197e4bcc","TraceId":"14fe1b520625a1dba8259444310c0670","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHF","RequestId":"0HNBO264G3IHF:00000001","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:25:53.7779935Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-7,\"temperatureF\":20,\"summary\":\"Warm\",\"test\":\"123\"}条","@tr":"32ff487382d9edd189df02f0d8aabf1b","@sp":"b9dcaa30c98acbd3","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-7,\"temperatureF\":20,\"summary\":\"Warm\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"b9dcaa30c98acbd3","TraceId":"32ff487382d9edd189df02f0d8aabf1b","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHF","RequestId":"0HNBO264G3IHF:00000003","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:25:53.7810459Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-7,\"temperatureF\":20,\"summary\":\"Warm\",\"test\":\"123\"}条","@l":"Error","@tr":"32ff487382d9edd189df02f0d8aabf1b","@sp":"b9dcaa30c98acbd3","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"b9dcaa30c98acbd3","TraceId":"32ff487382d9edd189df02f0d8aabf1b","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHF","RequestId":"0HNBO264G3IHF:00000003","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:25:56.7932706Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":9.7574,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:25:56.9343084Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":0,\"temperatureF\":32,\"summary\":\"Bracing\",\"test\":\"123\"}条","@tr":"b9bec246b0a537b0ea103372559a175d","@sp":"6048aa1a0c906536","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":0,\"temperatureF\":32,\"summary\":\"Bracing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"6048aa1a0c906536","TraceId":"b9bec246b0a537b0ea103372559a175d","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHG","RequestId":"0HNBO264G3IHG:00000001","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:25:56.9368987Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":0,\"temperatureF\":32,\"summary\":\"Bracing\",\"test\":\"123\"}条","@l":"Error","@tr":"b9bec246b0a537b0ea103372559a175d","@sp":"6048aa1a0c906536","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"6048aa1a0c906536","TraceId":"b9bec246b0a537b0ea103372559a175d","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHG","RequestId":"0HNBO264G3IHG:00000001","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:25:58.2018363Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":30,\"temperatureF\":85,\"summary\":\"Scorching\",\"test\":\"123\"}条","@tr":"6a98c6d7ecbf279b61fc64053e9104ad","@sp":"f78a7990da38ebeb","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":30,\"temperatureF\":85,\"summary\":\"Scorching\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"f78a7990da38ebeb","TraceId":"6a98c6d7ecbf279b61fc64053e9104ad","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHG","RequestId":"0HNBO264G3IHG:00000003","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":37,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:25:58.2045055Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":30,\"temperatureF\":85,\"summary\":\"Scorching\",\"test\":\"123\"}条","@l":"Error","@tr":"6a98c6d7ecbf279b61fc64053e9104ad","@sp":"f78a7990da38ebeb","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"f78a7990da38ebeb","TraceId":"6a98c6d7ecbf279b61fc64053e9104ad","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHG","RequestId":"0HNBO264G3IHG:00000003","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":37,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:25:59.3602311Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":21,\"temperatureF\":69,\"summary\":\"Scorching\",\"test\":\"123\"}条","@tr":"5c541f13618d101014ccf8e2bc5c0612","@sp":"144a6cf58c146e16","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":21,\"temperatureF\":69,\"summary\":\"Scorching\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"144a6cf58c146e16","TraceId":"5c541f13618d101014ccf8e2bc5c0612","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHG","RequestId":"0HNBO264G3IHG:00000005","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":37,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:25:59.3638209Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":21,\"temperatureF\":69,\"summary\":\"Scorching\",\"test\":\"123\"}条","@l":"Error","@tr":"5c541f13618d101014ccf8e2bc5c0612","@sp":"144a6cf58c146e16","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"144a6cf58c146e16","TraceId":"5c541f13618d101014ccf8e2bc5c0612","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHG","RequestId":"0HNBO264G3IHG:00000005","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":37,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:26:01.9073509Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":50.424,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:16.0288188Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":21,\"temperatureF\":69,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@tr":"9f3285cd914cfb1b9e2bafddf6723c2f","@sp":"2548df7213d90cfb","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":21,\"temperatureF\":69,\"summary\":\"Sweltering\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"2548df7213d90cfb","TraceId":"9f3285cd914cfb1b9e2bafddf6723c2f","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000001","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:16.0313970Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":21,\"temperatureF\":69,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@l":"Error","@tr":"9f3285cd914cfb1b9e2bafddf6723c2f","@sp":"2548df7213d90cfb","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"2548df7213d90cfb","TraceId":"9f3285cd914cfb1b9e2bafddf6723c2f","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000001","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:16.1338192Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":49,\"temperatureF\":120,\"summary\":\"Warm\",\"test\":\"123\"}条","@tr":"370ac413763ba6d6ce0813659df76a1b","@sp":"0ab08e900240434b","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":49,\"temperatureF\":120,\"summary\":\"Warm\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"0ab08e900240434b","TraceId":"370ac413763ba6d6ce0813659df76a1b","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000003","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":38,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:16.1363507Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":49,\"temperatureF\":120,\"summary\":\"Warm\",\"test\":\"123\"}条","@l":"Error","@tr":"370ac413763ba6d6ce0813659df76a1b","@sp":"0ab08e900240434b","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"0ab08e900240434b","TraceId":"370ac413763ba6d6ce0813659df76a1b","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000003","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":38,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:16.3136451Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":8,\"temperatureF\":46,\"summary\":\"Bracing\",\"test\":\"123\"}条","@tr":"43379a73b060eadf115166295283986f","@sp":"d71a2638c7f7c39e","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":8,\"temperatureF\":46,\"summary\":\"Bracing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"d71a2638c7f7c39e","TraceId":"43379a73b060eadf115166295283986f","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000005","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:16.3161248Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":8,\"temperatureF\":46,\"summary\":\"Bracing\",\"test\":\"123\"}条","@l":"Error","@tr":"43379a73b060eadf115166295283986f","@sp":"d71a2638c7f7c39e","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"d71a2638c7f7c39e","TraceId":"43379a73b060eadf115166295283986f","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000005","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:16.5108352Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":1,\"temperatureF\":33,\"summary\":\"Bracing\",\"test\":\"123\"}条","@tr":"d6564f6a554cb6c3ad38a78e65664c7b","@sp":"b9455b50b9865436","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":1,\"temperatureF\":33,\"summary\":\"Bracing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"b9455b50b9865436","TraceId":"d6564f6a554cb6c3ad38a78e65664c7b","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000007","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:16.5140984Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":1,\"temperatureF\":33,\"summary\":\"Bracing\",\"test\":\"123\"}条","@l":"Error","@tr":"d6564f6a554cb6c3ad38a78e65664c7b","@sp":"b9455b50b9865436","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"b9455b50b9865436","TraceId":"d6564f6a554cb6c3ad38a78e65664c7b","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000007","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:16.6785529Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":36,\"temperatureF\":96,\"summary\":\"Bracing\",\"test\":\"123\"}条","@tr":"70204b3ceee22a33a2d940d470aa8adf","@sp":"5b71a8a6f772afe1","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":36,\"temperatureF\":96,\"summary\":\"Bracing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"5b71a8a6f772afe1","TraceId":"70204b3ceee22a33a2d940d470aa8adf","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000009","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:16.6814869Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":36,\"temperatureF\":96,\"summary\":\"Bracing\",\"test\":\"123\"}条","@l":"Error","@tr":"70204b3ceee22a33a2d940d470aa8adf","@sp":"5b71a8a6f772afe1","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"5b71a8a6f772afe1","TraceId":"70204b3ceee22a33a2d940d470aa8adf","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000009","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:16.8628833Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-6,\"temperatureF\":22,\"summary\":\"Scorching\",\"test\":\"123\"}条","@tr":"4929f6433cbfeb164caab8954baf537d","@sp":"d56d88cb1a359348","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-6,\"temperatureF\":22,\"summary\":\"Scorching\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"d56d88cb1a359348","TraceId":"4929f6433cbfeb164caab8954baf537d","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:0000000B","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":38,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:16.8655567Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-6,\"temperatureF\":22,\"summary\":\"Scorching\",\"test\":\"123\"}条","@l":"Error","@tr":"4929f6433cbfeb164caab8954baf537d","@sp":"d56d88cb1a359348","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"d56d88cb1a359348","TraceId":"4929f6433cbfeb164caab8954baf537d","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:0000000B","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":38,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:17.0470602Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":24,\"temperatureF\":75,\"summary\":\"Chilly\",\"test\":\"123\"}条","@tr":"982133909e30c9199964584c71b1cb14","@sp":"16a0d1b5a76c6f1b","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":24,\"temperatureF\":75,\"summary\":\"Chilly\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"16a0d1b5a76c6f1b","TraceId":"982133909e30c9199964584c71b1cb14","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:0000000D","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:17.0498803Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":24,\"temperatureF\":75,\"summary\":\"Chilly\",\"test\":\"123\"}条","@l":"Error","@tr":"982133909e30c9199964584c71b1cb14","@sp":"16a0d1b5a76c6f1b","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"16a0d1b5a76c6f1b","TraceId":"982133909e30c9199964584c71b1cb14","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:0000000D","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:17.1669363Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":16.9212,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:17.2418906Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":27,\"temperatureF\":80,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@tr":"911763caf02b3a93807709612ca91676","@sp":"c5a732da2301c8d3","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":27,\"temperatureF\":80,\"summary\":\"Sweltering\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"c5a732da2301c8d3","TraceId":"911763caf02b3a93807709612ca91676","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:0000000F","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:17.2464967Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":27,\"temperatureF\":80,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@l":"Error","@tr":"911763caf02b3a93807709612ca91676","@sp":"c5a732da2301c8d3","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"c5a732da2301c8d3","TraceId":"911763caf02b3a93807709612ca91676","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:0000000F","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:17.3992936Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":11,\"temperatureF\":51,\"summary\":\"Chilly\",\"test\":\"123\"}条","@tr":"f000ddd1dae624f402f8ebd985408b84","@sp":"012625c32200778a","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":11,\"temperatureF\":51,\"summary\":\"Chilly\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"012625c32200778a","TraceId":"f000ddd1dae624f402f8ebd985408b84","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000011","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:17.4022588Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":11,\"temperatureF\":51,\"summary\":\"Chilly\",\"test\":\"123\"}条","@l":"Error","@tr":"f000ddd1dae624f402f8ebd985408b84","@sp":"012625c32200778a","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"012625c32200778a","TraceId":"f000ddd1dae624f402f8ebd985408b84","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000011","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:17.5818707Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-17,\"temperatureF\":2,\"summary\":\"Chilly\",\"test\":\"123\"}条","@tr":"5a9c7eb2f898a82f2c4e55852af8ec41","@sp":"34de9b15bdc667ed","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-17,\"temperatureF\":2,\"summary\":\"Chilly\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"34de9b15bdc667ed","TraceId":"5a9c7eb2f898a82f2c4e55852af8ec41","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000013","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":37,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:17.5844076Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-17,\"temperatureF\":2,\"summary\":\"Chilly\",\"test\":\"123\"}条","@l":"Error","@tr":"5a9c7eb2f898a82f2c4e55852af8ec41","@sp":"34de9b15bdc667ed","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"34de9b15bdc667ed","TraceId":"5a9c7eb2f898a82f2c4e55852af8ec41","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000013","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":37,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:17.7734963Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":38,\"temperatureF\":100,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@tr":"80768f194385a390cb12d1f9fc0dcd07","@sp":"6e562177af37d422","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":38,\"temperatureF\":100,\"summary\":\"Sweltering\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"6e562177af37d422","TraceId":"80768f194385a390cb12d1f9fc0dcd07","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000015","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:17.7762732Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":38,\"temperatureF\":100,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@l":"Error","@tr":"80768f194385a390cb12d1f9fc0dcd07","@sp":"6e562177af37d422","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"6e562177af37d422","TraceId":"80768f194385a390cb12d1f9fc0dcd07","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000015","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:17.9839227Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":41,\"temperatureF\":105,\"summary\":\"Mild\",\"test\":\"123\"}条","@tr":"265104401ae349386be166a0ae4abc31","@sp":"bcb4455ca785cca7","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":41,\"temperatureF\":105,\"summary\":\"Mild\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"bcb4455ca785cca7","TraceId":"265104401ae349386be166a0ae4abc31","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000017","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:17.9864758Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":41,\"temperatureF\":105,\"summary\":\"Mild\",\"test\":\"123\"}条","@l":"Error","@tr":"265104401ae349386be166a0ae4abc31","@sp":"bcb4455ca785cca7","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"bcb4455ca785cca7","TraceId":"265104401ae349386be166a0ae4abc31","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000017","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:18.2055358Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-4,\"temperatureF\":25,\"summary\":\"Chilly\",\"test\":\"123\"}条","@tr":"a7fd36d044116fd2a995340f565347fc","@sp":"f9af80de047c34dd","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-4,\"temperatureF\":25,\"summary\":\"Chilly\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"f9af80de047c34dd","TraceId":"a7fd36d044116fd2a995340f565347fc","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000019","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:18.2081497Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-4,\"temperatureF\":25,\"summary\":\"Chilly\",\"test\":\"123\"}条","@l":"Error","@tr":"a7fd36d044116fd2a995340f565347fc","@sp":"f9af80de047c34dd","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"f9af80de047c34dd","TraceId":"a7fd36d044116fd2a995340f565347fc","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000019","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:18.4067569Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-2,\"temperatureF\":29,\"summary\":\"Warm\",\"test\":\"123\"}条","@tr":"fb42a047afa35d4ffeaa09979796c3a3","@sp":"8888e2640ea73e01","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-2,\"temperatureF\":29,\"summary\":\"Warm\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"8888e2640ea73e01","TraceId":"fb42a047afa35d4ffeaa09979796c3a3","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:0000001B","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:18.4093199Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-2,\"temperatureF\":29,\"summary\":\"Warm\",\"test\":\"123\"}条","@l":"Error","@tr":"fb42a047afa35d4ffeaa09979796c3a3","@sp":"8888e2640ea73e01","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"8888e2640ea73e01","TraceId":"fb42a047afa35d4ffeaa09979796c3a3","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:0000001B","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:18.6058727Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":52,\"temperatureF\":125,\"summary\":\"Scorching\",\"test\":\"123\"}条","@tr":"7e64e4c3d5049e6cd113c81592b7aa5e","@sp":"a230d251038b8e95","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":52,\"temperatureF\":125,\"summary\":\"Scorching\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"a230d251038b8e95","TraceId":"7e64e4c3d5049e6cd113c81592b7aa5e","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:0000001D","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:18.6082345Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":52,\"temperatureF\":125,\"summary\":\"Scorching\",\"test\":\"123\"}条","@l":"Error","@tr":"7e64e4c3d5049e6cd113c81592b7aa5e","@sp":"a230d251038b8e95","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"a230d251038b8e95","TraceId":"7e64e4c3d5049e6cd113c81592b7aa5e","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:0000001D","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:18.8176921Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-15,\"temperatureF\":6,\"summary\":\"Chilly\",\"test\":\"123\"}条","@tr":"d0566170a76d24aacafae00ac9c2e4eb","@sp":"4e3c682463d80191","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-15,\"temperatureF\":6,\"summary\":\"Chilly\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"4e3c682463d80191","TraceId":"d0566170a76d24aacafae00ac9c2e4eb","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:0000001F","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":37,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:18.8204958Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-15,\"temperatureF\":6,\"summary\":\"Chilly\",\"test\":\"123\"}条","@l":"Error","@tr":"d0566170a76d24aacafae00ac9c2e4eb","@sp":"4e3c682463d80191","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"4e3c682463d80191","TraceId":"d0566170a76d24aacafae00ac9c2e4eb","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:0000001F","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":37,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:19.0747842Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":6,\"temperatureF\":42,\"summary\":\"Scorching\",\"test\":\"123\"}条","@tr":"397da3754d7ebdf8b5fd0c37a338130e","@sp":"8562a458ec96a45e","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":6,\"temperatureF\":42,\"summary\":\"Scorching\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"8562a458ec96a45e","TraceId":"397da3754d7ebdf8b5fd0c37a338130e","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000021","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":37,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:19.0772978Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":6,\"temperatureF\":42,\"summary\":\"Scorching\",\"test\":\"123\"}条","@l":"Error","@tr":"397da3754d7ebdf8b5fd0c37a338130e","@sp":"8562a458ec96a45e","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"8562a458ec96a45e","TraceId":"397da3754d7ebdf8b5fd0c37a338130e","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000021","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":37,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:19.2708624Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-13,\"temperatureF\":9,\"summary\":\"Freezing\",\"test\":\"123\"}条","@tr":"880bab77678553c8fb83802b631ed866","@sp":"a9b508b785ae5f4a","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-13,\"temperatureF\":9,\"summary\":\"Freezing\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"a9b508b785ae5f4a","TraceId":"880bab77678553c8fb83802b631ed866","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000023","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:19.2728246Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-13,\"temperatureF\":9,\"summary\":\"Freezing\",\"test\":\"123\"}条","@l":"Error","@tr":"880bab77678553c8fb83802b631ed866","@sp":"a9b508b785ae5f4a","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"a9b508b785ae5f4a","TraceId":"880bab77678553c8fb83802b631ed866","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000023","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:19.4822741Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-3,\"temperatureF\":27,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@tr":"446acdb2299a187babbd91a4aa97c341","@sp":"a18ed3bdc752d802","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-3,\"temperatureF\":27,\"summary\":\"Sweltering\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"a18ed3bdc752d802","TraceId":"446acdb2299a187babbd91a4aa97c341","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000025","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:19.4854766Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-3,\"temperatureF\":27,\"summary\":\"Sweltering\",\"test\":\"123\"}条","@l":"Error","@tr":"446acdb2299a187babbd91a4aa97c341","@sp":"a18ed3bdc752d802","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"a18ed3bdc752d802","TraceId":"446acdb2299a187babbd91a4aa97c341","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000025","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:19.6873180Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":10,\"temperatureF\":49,\"summary\":\"Balmy\",\"test\":\"123\"}条","@tr":"4b5afcc0fa491c91a547669d737f3659","@sp":"1458efea8f51ee71","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":10,\"temperatureF\":49,\"summary\":\"Balmy\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"1458efea8f51ee71","TraceId":"4b5afcc0fa491c91a547669d737f3659","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000027","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:19.6893081Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":10,\"temperatureF\":49,\"summary\":\"Balmy\",\"test\":\"123\"}条","@l":"Error","@tr":"4b5afcc0fa491c91a547669d737f3659","@sp":"1458efea8f51ee71","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"1458efea8f51ee71","TraceId":"4b5afcc0fa491c91a547669d737f3659","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000027","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":33,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:19.8727973Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-7,\"temperatureF\":20,\"summary\":\"Chilly\",\"test\":\"123\"}条","@tr":"2613b64237555e8ed15fa7fda44dbe5f","@sp":"eab7da1ea9826276","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-7,\"temperatureF\":20,\"summary\":\"Chilly\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"eab7da1ea9826276","TraceId":"2613b64237555e8ed15fa7fda44dbe5f","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000029","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:19.8750341Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-7,\"temperatureF\":20,\"summary\":\"Chilly\",\"test\":\"123\"}条","@l":"Error","@tr":"2613b64237555e8ed15fa7fda44dbe5f","@sp":"eab7da1ea9826276","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"eab7da1ea9826276","TraceId":"2613b64237555e8ed15fa7fda44dbe5f","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHH","RequestId":"0HNBO264G3IHH:00000029","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:20.1069705Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-4,\"temperatureF\":25,\"summary\":\"Scorching\",\"test\":\"123\"}条","@tr":"5503353cf66a9f6d84c45ec8aa348e91","@sp":"aadd4bc9083e01c6","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":-4,\"temperatureF\":25,\"summary\":\"Scorching\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"aadd4bc9083e01c6","TraceId":"5503353cf66a9f6d84c45ec8aa348e91","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHI","RequestId":"0HNBO264G3IHI:00000001","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:20.1091873Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":-4,\"temperatureF\":25,\"summary\":\"Scorching\",\"test\":\"123\"}条","@l":"Error","@tr":"5503353cf66a9f6d84c45ec8aa348e91","@sp":"aadd4bc9083e01c6","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"aadd4bc9083e01c6","TraceId":"5503353cf66a9f6d84c45ec8aa348e91","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHI","RequestId":"0HNBO264G3IHI:00000001","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:20.2832175Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":32,\"temperatureF\":89,\"summary\":\"Warm\",\"test\":\"123\"}条","@tr":"b632e1b53d3f5f136ce6e6bb666ab949","@sp":"4dbd056e6afec9cc","\"date\"":"{\"date\":\"2025-04-10\",\"temperatureC\":32,\"temperatureF\":89,\"summary\":\"Warm\",\"test\":\"123\"}","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"4dbd056e6afec9cc","TraceId":"b632e1b53d3f5f136ce6e6bb666ab949","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHI","RequestId":"0HNBO264G3IHI:00000003","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:20.2855260Z","@mt":"Hello, Seq：我是第{\"date\":\"2025-04-10\",\"temperatureC\":32,\"temperatureF\":89,\"summary\":\"Warm\",\"test\":\"123\"}条","@l":"Error","@tr":"b632e1b53d3f5f136ce6e6bb666ab949","@sp":"4dbd056e6afec9cc","SourceContext":"SSIC.HostServer.Controllers.WeatherForecast2Controller","SpanId":"4dbd056e6afec9cc","TraceId":"b632e1b53d3f5f136ce6e6bb666ab949","ParentId":"0000000000000000","ConnectionId":"0HNBO264G3IHI","RequestId":"0HNBO264G3IHI:00000003","RequestPath":"/WeatherForecast2","ActionId":"828545bd-8480-417b-9925-e69ffad5ad65","ActionName":"SSIC.HostServer.Controllers.WeatherForecast2Controller.Get (SSIC.HostServer)","MachineName":"DESKTOP-O211UJ1","ThreadId":31,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:27:22.5554373Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":57.6934,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":24,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:55:49.6871671Z","@mt":"Now listening on: {address}","address":"https://localhost:56797","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:55:49.7201587Z","@mt":"Now listening on: {address}","address":"http://localhost:56799","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:55:49.7898270Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:55:49.7901868Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:55:49.7903985Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T09:55:54.5404367Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":42.5019,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T10:14:19.7896798Z","@mt":"Now listening on: {address}","address":"https://localhost:56268","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T10:14:19.8398678Z","@mt":"Now listening on: {address}","address":"http://localhost:56269","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T10:14:19.9261813Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T10:14:19.9264609Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T10:14:19.9265708Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-10T10:14:29.5424487Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":43.1701,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
