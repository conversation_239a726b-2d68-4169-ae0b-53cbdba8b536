using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace SSIC.Modules.Sys.Controllers
{
    /// <summary>
    /// 系统配置控制器
    /// </summary>
    // [Authorize] // 暂时注释掉用于测试路由
    public class SystemConfigController : ControllerBase
    {
        private readonly ILogger<SystemConfigController> _logger;

        // 模拟系统配置数据
        private static readonly Dictionary<string, SystemConfig> _configs = new()
        {
            { "system.name", new SystemConfig { Key = "system.name", Value = "SSIC管理系统", Description = "系统名称", Category = "基础设置" } },
            { "system.version", new SystemConfig { Key = "system.version", Value = "1.0.0", Description = "系统版本", Category = "基础设置" } },
            { "system.logo", new SystemConfig { Key = "system.logo", Value = "/images/logo.png", Description = "系统Logo", Category = "界面设置" } },
            { "system.theme", new SystemConfig { Key = "system.theme", Value = "light", Description = "系统主题", Category = "界面设置" } },
            { "email.smtp.host", new SystemConfig { Key = "email.smtp.host", Value = "smtp.example.com", Description = "SMTP服务器", Category = "邮件设置" } },
            { "email.smtp.port", new SystemConfig { Key = "email.smtp.port", Value = "587", Description = "SMTP端口", Category = "邮件设置" } }
        };

        public SystemConfigController(ILogger<SystemConfigController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 获取所有系统配置
        /// </summary>
        /// <param name="category">配置分类（可选）</param>
        /// <returns>配置列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetConfigs([FromQuery] string? category = null)
        {
            _logger.LogInformation("获取系统配置，分类: {Category}", category ?? "全部");
            
            var configs = _configs.Values.AsEnumerable();
            
            if (!string.IsNullOrEmpty(category))
            {
                configs = configs.Where(c => c.Category.Equals(category, StringComparison.OrdinalIgnoreCase));
            }

            var result = configs.ToList();
            return Ok(new { success = true, data = result, total = result.Count });
        }

        /// <summary>
        /// 根据键获取配置
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>配置信息</returns>
        [HttpGet("{key}")]
        public async Task<IActionResult> GetConfig(string key)
        {
            _logger.LogInformation("获取系统配置: {Key}", key);
            
            if (_configs.TryGetValue(key, out var config))
            {
                return Ok(new { success = true, data = config });
            }

            return NotFound(new { message = "配置不存在" });
        }

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新结果</returns>
        [HttpPut("{key}")]
        public async Task<IActionResult> UpdateConfig(string key, [FromBody] UpdateConfigRequest request)
        {
            _logger.LogInformation("更新系统配置: {Key}", key);
            
            if (_configs.TryGetValue(key, out var config))
            {
                config.Value = request.Value;
                config.Description = request.Description ?? config.Description;
                config.UpdatedAt = DateTime.Now;

                return Ok(new { success = true, data = config, message = "配置更新成功" });
            }

            return NotFound(new { message = "配置不存在" });
        }

        /// <summary>
        /// 创建新配置
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> CreateConfig([FromBody] CreateConfigRequest request)
        {
            _logger.LogInformation("创建系统配置: {Key}", request.Key);
            
            if (string.IsNullOrEmpty(request.Key))
            {
                return BadRequest(new { message = "配置键不能为空" });
            }

            if (_configs.ContainsKey(request.Key))
            {
                return BadRequest(new { message = "配置键已存在" });
            }

            var newConfig = new SystemConfig
            {
                Key = request.Key,
                Value = request.Value,
                Description = request.Description,
                Category = request.Category,
                CreatedAt = DateTime.Now
            };

            _configs[request.Key] = newConfig;

            return CreatedAtAction(nameof(GetConfig), new { key = request.Key }, 
                new { success = true, data = newConfig, message = "配置创建成功" });
        }

        /// <summary>
        /// 删除配置
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{key}")]
        public async Task<IActionResult> DeleteConfig(string key)
        {
            _logger.LogInformation("删除系统配置: {Key}", key);
            
            if (_configs.Remove(key))
            {
                return Ok(new { success = true, message = "配置删除成功" });
            }

            return NotFound(new { message = "配置不存在" });
        }

        /// <summary>
        /// 获取配置分类
        /// </summary>
        /// <returns>分类列表</returns>
        [HttpGet("categories")]
        public async Task<IActionResult> GetCategories()
        {
            _logger.LogInformation("获取配置分类");
            
            var categories = _configs.Values
                .Select(c => c.Category)
                .Distinct()
                .OrderBy(c => c)
                .ToList();

            return Ok(new { success = true, data = categories });
        }

        /// <summary>
        /// 批量更新配置
        /// </summary>
        /// <param name="requests">批量更新请求</param>
        /// <returns>更新结果</returns>
        [HttpPut("batch")]
        public async Task<IActionResult> BatchUpdateConfigs([FromBody] List<BatchUpdateConfigRequest> requests)
        {
            _logger.LogInformation("批量更新系统配置，数量: {Count}", requests.Count);
            
            var updatedConfigs = new List<SystemConfig>();
            var errors = new List<string>();

            foreach (var request in requests)
            {
                if (_configs.TryGetValue(request.Key, out var config))
                {
                    config.Value = request.Value;
                    config.UpdatedAt = DateTime.Now;
                    updatedConfigs.Add(config);
                }
                else
                {
                    errors.Add($"配置键 '{request.Key}' 不存在");
                }
            }

            return Ok(new { 
                success = errors.Count == 0, 
                data = updatedConfigs, 
                errors = errors,
                message = $"成功更新 {updatedConfigs.Count} 个配置" + (errors.Count > 0 ? $"，{errors.Count} 个失败" : "")
            });
        }
    }

    #region 模型定义

    /// <summary>
    /// 系统配置模型
    /// </summary>
    public class SystemConfig
    {
        /// <summary>
        /// 配置键
        /// </summary>
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 配置值
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 配置分类
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// 创建配置请求模型
    /// </summary>
    public class CreateConfigRequest
    {
        /// <summary>
        /// 配置键
        /// </summary>
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 配置值
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 配置分类
        /// </summary>
        public string Category { get; set; } = string.Empty;
    }

    /// <summary>
    /// 更新配置请求模型
    /// </summary>
    public class UpdateConfigRequest
    {
        /// <summary>
        /// 配置值
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 配置描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 批量更新配置请求模型
    /// </summary>
    public class BatchUpdateConfigRequest
    {
        /// <summary>
        /// 配置键
        /// </summary>
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 配置值
        /// </summary>
        public string Value { get; set; } = string.Empty;
    }

    #endregion
}
