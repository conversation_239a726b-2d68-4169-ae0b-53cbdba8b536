﻿using SSIC.Infrastructure.ConfigurableOptions;

namespace SSIC.Infrastructure.OptionsEntity
{
    /// <summary>
    /// 跨域配置设置
    /// </summary>
    public class CorsOptions : IConfigurableOptions
    {
        /// <summary>
        /// 策略名称
        /// </summary>
        public string PolicyName { get; set; }

        /// <summary>
        /// 允许来源域名，没有配置则允许所有来源
        /// </summary>
        public string[] WithOrigins { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool? IsUse { get; set; } = false;
    }
}