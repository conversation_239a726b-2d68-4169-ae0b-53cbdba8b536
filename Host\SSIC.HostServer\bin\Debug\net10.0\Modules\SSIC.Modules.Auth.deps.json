{"runtimeTarget": {"name": ".NETCoreApp,Version=v10.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v10.0": {"SSIC.Modules.Auth/1.0.0": {"dependencies": {"Aspire.Seq": "9.2.0", "SSIC.Entity": "1.0.0", "SSIC.Infrastructure": "1.0.0"}, "runtime": {"SSIC.Modules.Auth.dll": {}}}, "Aspire.Seq/9.2.0": {"dependencies": {"Google.Protobuf": "3.30.1", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.11.2"}, "runtime": {"lib/net8.0/Aspire.Seq.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.200.25.20902"}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Memory.Data": "1.0.2"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}}, "BouncyCastle.Cryptography/2.3.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.3.1.17862"}}}, "Dapr.AspNetCore/1.15.3": {"dependencies": {"Dapr.Client": "1.15.3", "Dapr.Common": "1.15.3", "Google.Api.CommonProtos": "2.2.0", "Google.Protobuf": "3.30.1", "Grpc.Net.Client": "2.70.0"}, "runtime": {"lib/net9.0/Dapr.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "1.15.3.0"}}}, "Dapr.Client/1.15.3": {"dependencies": {"Dapr.Common": "1.15.3", "Dapr.Protos": "1.15.3", "Google.Api.CommonProtos": "2.2.0", "Google.Protobuf": "3.30.1", "Grpc.Net.Client": "2.70.0"}, "runtime": {"lib/net9.0/Dapr.Client.dll": {"assemblyVersion": "*******", "fileVersion": "1.15.3.0"}}}, "Dapr.Common/1.15.3": {"dependencies": {"Google.Api.CommonProtos": "2.2.0", "Google.Protobuf": "3.30.1", "Grpc.Net.Client": "2.70.0"}, "runtime": {"lib/net9.0/Dapr.Common.dll": {"assemblyVersion": "*******", "fileVersion": "1.15.3.0"}}}, "Dapr.Protos/1.15.3": {"dependencies": {"Google.Api.CommonProtos": "2.2.0", "Google.Protobuf": "3.30.1", "Grpc.Net.Client": "2.70.0"}, "runtime": {"lib/net9.0/Dapr.Protos.dll": {"assemblyVersion": "*******", "fileVersion": "1.15.3.0"}}}, "DM.DmProvider/8.3.1.28188": {"runtime": {"lib/net8.0/DM.DmProvider.dll": {"assemblyVersion": "8.3.1.28188", "fileVersion": "8.3.1.28188"}}, "resources": {"lib/net8.0/en/DM.DmProvider.resources.dll": {"locale": "en"}, "lib/net8.0/zh-CN/DM.DmProvider.resources.dll": {"locale": "zh-CN"}, "lib/net8.0/zh-HK/DM.DmProvider.resources.dll": {"locale": "zh-HK"}, "lib/net8.0/zh-TW/DM.DmProvider.resources.dll": {"locale": "zh-TW"}}}, "FreeRedis/1.3.6": {"runtime": {"lib/netstandard2.0/FreeRedis.dll": {"assemblyVersion": "1.3.6.0", "fileVersion": "1.3.6.0"}}}, "FreeScheduler/2.0.33": {"dependencies": {"FreeRedis": "1.3.6", "FreeSql.DbContext": "3.5.202", "IdleBus": "1.5.3", "Newtonsoft.Json": "13.0.1", "WorkQueue": "1.3.0"}, "runtime": {"lib/net8.0/FreeScheduler.dll": {"assemblyVersion": "2.0.33.0", "fileVersion": "2.0.33.0"}}}, "FreeSql/3.5.202": {"runtime": {"lib/netstandard2.1/FreeSql.dll": {"assemblyVersion": "3.5.202.0", "fileVersion": "3.5.202.0"}}}, "FreeSql.All/3.5.202": {"dependencies": {"FreeSql.Provider.Dameng": "3.5.202", "FreeSql.Provider.MsAccess": "3.5.202", "FreeSql.Provider.MySql": "3.5.202", "FreeSql.Provider.Odbc": "3.5.202", "FreeSql.Provider.Oracle": "3.5.202", "FreeSql.Provider.PostgreSQL": "3.5.202", "FreeSql.Provider.SqlServer": "3.5.202", "FreeSql.Provider.Sqlite": "3.5.202", "FreeSql.Repository": "3.5.202"}, "runtime": {"lib/netstandard2.1/FreeSql.All.dll": {"assemblyVersion": "3.5.202.0", "fileVersion": "3.5.202.0"}}}, "FreeSql.Cloud/2.0.1": {"dependencies": {"FreeScheduler": "2.0.33", "FreeSql.DbContext": "3.5.202", "IdleBus": "1.5.3", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/FreeSql.Cloud.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "FreeSql.DbContext/3.5.202": {"dependencies": {"FreeSql": "3.5.202"}, "runtime": {"lib/net9.0/FreeSql.DbContext.dll": {"assemblyVersion": "3.5.202.0", "fileVersion": "3.5.202.0"}}}, "FreeSql.Provider.Dameng/3.5.202": {"dependencies": {"DM.DmProvider": "8.3.1.28188", "FreeSql": "3.5.202"}, "runtime": {"lib/net6.0/FreeSql.Provider.Dameng.dll": {"assemblyVersion": "3.5.202.0", "fileVersion": "3.5.202.0"}}}, "FreeSql.Provider.MsAccess/3.5.202": {"dependencies": {"FreeSql": "3.5.202", "System.Data.OleDb": "6.0.0"}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.MsAccess.dll": {"assemblyVersion": "3.5.202.0", "fileVersion": "3.5.202.0"}}}, "FreeSql.Provider.MySql/3.5.202": {"dependencies": {"FreeSql": "3.5.202", "MySql.Data": "9.1.0"}, "runtime": {"lib/net9.0/FreeSql.Provider.MySql.dll": {"assemblyVersion": "3.5.202.0", "fileVersion": "3.5.202.0"}}}, "FreeSql.Provider.Odbc/3.5.202": {"dependencies": {"FreeSql": "3.5.202", "System.Data.Odbc": "8.0.0"}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.Odbc.dll": {"assemblyVersion": "3.5.202.0", "fileVersion": "3.5.202.0"}}}, "FreeSql.Provider.Oracle/3.5.202": {"dependencies": {"FreeSql": "3.5.202", "Oracle.ManagedDataAccess.Core": "23.6.1"}, "runtime": {"lib/net9.0/FreeSql.Provider.Oracle.dll": {"assemblyVersion": "3.5.202.0", "fileVersion": "3.5.202.0"}}}, "FreeSql.Provider.PostgreSQL/3.5.202": {"dependencies": {"FreeSql": "3.5.202", "Newtonsoft.Json": "13.0.1", "Npgsql.LegacyPostgis": "5.0.11", "Npgsql.NetTopologySuite": "5.0.11"}, "runtime": {"lib/net9.0/FreeSql.Provider.PostgreSQL.dll": {"assemblyVersion": "3.5.202.0", "fileVersion": "3.5.202.0"}}}, "FreeSql.Provider.Sqlite/3.5.202": {"dependencies": {"FreeSql": "3.5.202", "System.Data.SQLite.Core": "*********"}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.Sqlite.dll": {"assemblyVersion": "3.5.202.0", "fileVersion": "3.5.202.0"}}}, "FreeSql.Provider.SqlServer/3.5.202": {"dependencies": {"FreeSql": "3.5.202", "Microsoft.Data.SqlClient": "6.0.1"}, "runtime": {"lib/net9.0/FreeSql.Provider.SqlServer.dll": {"assemblyVersion": "3.5.202.0", "fileVersion": "3.5.202.0"}}}, "FreeSql.Repository/3.5.202": {"dependencies": {"FreeSql.DbContext": "3.5.202"}, "runtime": {"lib/net9.0/FreeSql.Repository.dll": {"assemblyVersion": "3.5.202.0", "fileVersion": "3.5.202.0"}}}, "Google.Api.CommonProtos/2.2.0": {"dependencies": {"Google.Protobuf": "3.30.1"}, "runtime": {"lib/netstandard2.0/Google.Api.CommonProtos.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "Google.Protobuf/3.30.1": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.30.1.0", "fileVersion": "3.30.1.0"}}}, "Grpc.Core.Api/2.70.0": {"runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.70.0.0"}}}, "Grpc.Net.Client/2.70.0": {"dependencies": {"Grpc.Net.Common": "2.70.0"}, "runtime": {"lib/net8.0/Grpc.Net.Client.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.70.0.0"}}}, "Grpc.Net.Common/2.70.0": {"dependencies": {"Grpc.Core.Api": "2.70.0"}, "runtime": {"lib/net8.0/Grpc.Net.Common.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.70.0.0"}}}, "IdleBus/1.5.3": {"runtime": {"lib/netstandard2.0/IdleBus.dll": {"assemblyVersion": "1.5.3.0", "fileVersion": "1.5.3.0"}}}, "K4os.Compression.LZ4/1.3.8": {"runtime": {"lib/net6.0/K4os.Compression.LZ4.dll": {"assemblyVersion": "1.3.8.0", "fileVersion": "1.3.8.0"}}}, "K4os.Compression.LZ4.Streams/1.3.8": {"dependencies": {"K4os.Compression.LZ4": "1.3.8", "K4os.Hash.xxHash": "1.0.8"}, "runtime": {"lib/net6.0/K4os.Compression.LZ4.Streams.dll": {"assemblyVersion": "1.3.8.0", "fileVersion": "1.3.8.0"}}}, "K4os.Hash.xxHash/1.0.8": {"runtime": {"lib/net6.0/K4os.Hash.xxHash.dll": {"assemblyVersion": "1.0.8.0", "fileVersion": "1.0.8.0"}}}, "Mapster/7.4.2-pre02": {"dependencies": {"Mapster.Core": "1.2.3-pre02"}, "runtime": {"lib/net9.0/Mapster.dll": {"assemblyVersion": "7.4.2.0", "fileVersion": "7.4.2.0"}}}, "Mapster.Core/1.2.3-pre02": {"runtime": {"lib/net9.0/Mapster.Core.dll": {"assemblyVersion": "1.2.3.0", "fileVersion": "1.2.3.0"}}}, "Masa.BuildingBlocks.Configuration/1.2.0-preview.6": {"runtime": {"lib/net8.0/Masa.BuildingBlocks.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.BuildingBlocks.Data/1.2.0-preview.6": {"dependencies": {"Masa.BuildingBlocks.Data.Contracts": "1.2.0-preview.6", "Masa.Utils.Caching.Memory": "1.2.0-preview.6", "Masa.Utils.Extensions.DotNet": "1.2.0-preview.6"}, "runtime": {"lib/net8.0/Masa.BuildingBlocks.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.BuildingBlocks.Data.Contracts/1.2.0-preview.6": {"dependencies": {"Masa.Utils.Models.Config": "1.2.0-preview.6"}, "runtime": {"lib/net8.0/Masa.BuildingBlocks.Data.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.BuildingBlocks.Development.DaprStarter/1.2.0-preview.6": {"dependencies": {"Masa.BuildingBlocks.Data": "1.2.0-preview.6", "Masa.BuildingBlocks.Exceptions": "1.2.0-preview.6"}, "runtime": {"lib/net8.0/Masa.BuildingBlocks.Development.DaprStarter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.BuildingBlocks.Exceptions/1.2.0-preview.6": {"dependencies": {"Masa.BuildingBlocks.Data": "1.2.0-preview.6", "Masa.BuildingBlocks.Globalization.I18n": "1.2.0-preview.6", "Masa.Utils.Extensions.DotNet": "1.2.0-preview.6"}, "runtime": {"lib/net8.0/Masa.BuildingBlocks.Exceptions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.BuildingBlocks.Globalization.I18n/1.2.0-preview.6": {"dependencies": {"Masa.BuildingBlocks.Data": "1.2.0-preview.6"}, "runtime": {"lib/net8.0/Masa.BuildingBlocks.Globalization.I18n.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.Contrib.Development.DaprStarter/1.2.0-preview.6": {"dependencies": {"Masa.BuildingBlocks.Configuration": "1.2.0-preview.6", "Masa.BuildingBlocks.Development.DaprStarter": "1.2.0-preview.6", "Masa.Utils.Extensions.DotNet": "1.2.0-preview.6"}, "runtime": {"lib/net8.0/Masa.Contrib.Development.DaprStarter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.Contrib.Development.DaprStarter.AspNetCore/1.2.0-preview.6": {"dependencies": {"Masa.BuildingBlocks.Exceptions": "1.2.0-preview.6", "Masa.Contrib.Development.DaprStarter": "1.2.0-preview.6"}, "runtime": {"lib/net8.0/Masa.Contrib.Development.DaprStarter.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.Utils.Caching.Memory/1.2.0-preview.6": {"runtime": {"lib/net8.0/Masa.Utils.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.Utils.Extensions.DotNet/1.2.0-preview.6": {"runtime": {"lib/net8.0/Masa.Utils.Extensions.DotNet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Masa.Utils.Models.Config/1.2.0-preview.6": {"runtime": {"lib/net8.0/Masa.Utils.Models.Config.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/10.0.0-preview.3.25172.1": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net10.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17201"}}}, "Microsoft.AspNetCore.OpenApi/9.0.4": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "9.0.4.0", "fileVersion": "9.0.425.16403"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.Bcl.Cryptography/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Bcl.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Data.SqlClient/6.0.1": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Bcl.Cryptography": "9.0.0", "Microsoft.Data.SqlClient.SNI.runtime": "6.0.2", "Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Security.Cryptography.Pkcs": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.1.25023.1"}}, "resources": {"lib/net9.0/cs/Microsoft.Data.SqlClient.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.Data.SqlClient.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.Data.SqlClient.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.1.25023.1"}, "runtimes/win/lib/net9.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.1.25023.1"}}}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.DependencyValidation.Analyzers/0.11.0": {}, "Microsoft.Extensions.AmbientMetadata.Application/9.4.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.20705"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Compliance.Abstractions/9.4.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.20705"}}}, "Microsoft.Extensions.Configuration/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Primitives": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Configuration.Abstractions/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Primitives": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Configuration.Binder/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Configuration.CommandLine/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Configuration.FileExtensions/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.FileProviders.Physical": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Primitives": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Configuration.Json/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.FileExtensions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Configuration.UserSecrets/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.Json": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.FileProviders.Physical": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.DependencyInjection/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/10.0.0-preview.3.25171.5": {"runtime": {"lib/net10.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.4.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.20705"}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Diagnostics/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Diagnostics.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Options": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.4.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.20705"}}}, "Microsoft.Extensions.FileProviders.Abstractions/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Primitives": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.FileProviders.Physical/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.FileSystemGlobbing": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Primitives": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.FileSystemGlobbing/10.0.0-preview.3.25171.5": {"runtime": {"lib/net10.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Hosting/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.Binder": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.CommandLine": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.EnvironmentVariables": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.FileExtensions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.Json": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.UserSecrets": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.DependencyInjection": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Diagnostics": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.FileProviders.Physical": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Hosting.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.Configuration": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.Console": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.Debug": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.EventLog": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.EventSource": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Options": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Hosting.Abstractions/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Diagnostics.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.FileProviders.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Http.Diagnostics/9.4.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "9.4.0", "Microsoft.Extensions.Telemetry": "9.4.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.20705"}}}, "Microsoft.Extensions.Http.Resilience/9.4.0": {"dependencies": {"Microsoft.Extensions.Http.Diagnostics": "9.4.0", "Microsoft.Extensions.Resilience": "9.4.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.20705"}}}, "Microsoft.Extensions.Logging/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Options": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Logging.Abstractions/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Logging.Configuration/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Configuration": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.Binder": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Options": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Logging.Console/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.Configuration": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Options": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Logging.Debug/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Logging.EventLog/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Options": "10.0.0-preview.3.25171.5", "System.Diagnostics.EventLog": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Logging.EventSource/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Logging.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Options": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Primitives": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Options/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Primitives": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/10.0.0-preview.3.25171.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.Binder": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Options": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Primitives": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net10.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Primitives/10.0.0-preview.3.25171.5": {"runtime": {"lib/net10.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "Microsoft.Extensions.Resilience/9.4.0": {"dependencies": {"Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.4.0", "Microsoft.Extensions.Telemetry.Abstractions": "9.4.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.20705"}}}, "Microsoft.Extensions.ServiceDiscovery/9.2.0": {"dependencies": {"Microsoft.Extensions.ServiceDiscovery.Abstractions": "9.2.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.200.25.20902"}}}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.2.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.200.25.20902"}}}, "Microsoft.Extensions.Telemetry/9.4.0": {"dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.4.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.4.0", "Microsoft.Extensions.Telemetry.Abstractions": "9.4.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.20705"}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.4.0": {"dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.4.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.400.25.20705"}}}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Logging/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Tokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MiniProfiler.AspNetCore/4.5.4": {"dependencies": {"MiniProfiler.Shared": "4.5.4"}, "runtime": {"lib/net8.0/MiniProfiler.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "4.5.4.47516"}}}, "MiniProfiler.AspNetCore.Mvc/4.5.4": {"dependencies": {"MiniProfiler.AspNetCore": "4.5.4"}, "runtime": {"lib/net8.0/MiniProfiler.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "4.5.4.47516"}}}, "MiniProfiler.Shared/4.5.4": {"runtime": {"lib/net8.0/MiniProfiler.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "4.5.4.47516"}}}, "MySql.Data/9.1.0": {"dependencies": {"BouncyCastle.Cryptography": "2.3.1", "Google.Protobuf": "3.30.1", "K4os.Compression.LZ4.Streams": "1.3.8", "System.Configuration.ConfigurationManager": "9.0.0", "System.Security.Permissions": "8.0.0", "ZstdSharp.Port": "0.8.0"}, "runtime": {"lib/net8.0/MySql.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "runtimeTargets": {"runtimes/win-x64/native/comerr64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/gssapi64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/k5sprt64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/krb5_64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/krbcc64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}}}, "NetTopologySuite/2.0.0": {"runtime": {"lib/netstandard2.0/NetTopologySuite.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "NetTopologySuite.IO.PostGis/2.1.0": {"dependencies": {"NetTopologySuite": "2.0.0"}, "runtime": {"lib/netstandard2.1/NetTopologySuite.IO.PostGis.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.1.25517"}}}, "Npgsql/9.0.3": {"runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.3.0"}}}, "Npgsql.LegacyPostgis/5.0.11": {"dependencies": {"Npgsql": "9.0.3"}, "runtime": {"lib/netstandard2.0/Npgsql.LegacyPostgis.dll": {"assemblyVersion": "5.0.11.0", "fileVersion": "5.0.11.0"}}}, "Npgsql.NetTopologySuite/5.0.11": {"dependencies": {"NetTopologySuite.IO.PostGis": "2.1.0", "Npgsql": "9.0.3"}, "runtime": {"lib/netstandard2.0/Npgsql.NetTopologySuite.dll": {"assemblyVersion": "5.0.11.0", "fileVersion": "5.0.11.0"}}}, "OpenTelemetry/1.11.2": {"dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.11.2"}, "runtime": {"lib/net9.0/OpenTelemetry.dll": {"assemblyVersion": "*******", "fileVersion": "1.11.2.1586"}}}, "OpenTelemetry.Api/1.11.2": {"runtime": {"lib/net9.0/OpenTelemetry.Api.dll": {"assemblyVersion": "*******", "fileVersion": "1.11.2.1586"}}}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.11.2": {"dependencies": {"OpenTelemetry.Api": "1.11.2"}, "runtime": {"lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "1.11.2.1586"}}}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.11.2": {"dependencies": {"OpenTelemetry": "1.11.2"}, "runtime": {"lib/net9.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll": {"assemblyVersion": "*******", "fileVersion": "1.11.2.1586"}}}, "OpenTelemetry.Exporter.Zipkin/1.11.2": {"dependencies": {"OpenTelemetry": "1.11.2"}, "runtime": {"lib/net9.0/OpenTelemetry.Exporter.Zipkin.dll": {"assemblyVersion": "*******", "fileVersion": "1.11.2.1586"}}}, "OpenTelemetry.Extensions.Hosting/1.11.2": {"dependencies": {"OpenTelemetry": "1.11.2"}, "runtime": {"lib/net9.0/OpenTelemetry.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "1.11.2.1586"}}}, "OpenTelemetry.Instrumentation.AspNetCore/1.11.1": {"dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.11.2"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.dll": {"assemblyVersion": "1.11.1.401", "fileVersion": "1.11.1.401"}}}, "OpenTelemetry.Instrumentation.GrpcCore/1.0.0-beta.6": {"dependencies": {"Google.Protobuf": "3.30.1", "Grpc.Core.Api": "2.70.0", "OpenTelemetry.Api": "1.11.2"}, "runtime": {"lib/netstandard2.0/OpenTelemetry.Instrumentation.GrpcCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "OpenTelemetry.Instrumentation.GrpcNetClient/1.11.0-beta.2": {"dependencies": {"OpenTelemetry": "1.11.2"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.GrpcNetClient.dll": {"assemblyVersion": "1.11.0.402", "fileVersion": "1.11.0.402"}}}, "OpenTelemetry.Instrumentation.Http/1.11.1": {"dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.11.2"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Http.dll": {"assemblyVersion": "1.11.1.407", "fileVersion": "1.11.1.407"}}}, "OpenTelemetry.Instrumentation.Process/1.11.0-beta.2": {"dependencies": {"OpenTelemetry.Api": "1.11.2"}, "runtime": {"lib/netstandard2.0/OpenTelemetry.Instrumentation.Process.dll": {"assemblyVersion": "1.11.0.408", "fileVersion": "1.11.0.408"}}}, "OpenTelemetry.Instrumentation.Runtime/1.11.1": {"dependencies": {"OpenTelemetry.Api": "1.11.2"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Runtime.dll": {"assemblyVersion": "1.11.1.410", "fileVersion": "1.11.1.410"}}}, "Oracle.ManagedDataAccess.Core/23.6.1": {"dependencies": {"System.Diagnostics.PerformanceCounter": "8.0.0", "System.DirectoryServices.Protocols": "8.0.0"}, "runtime": {"lib/net8.0/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "23.1.0.0", "fileVersion": "23.1.0.0"}}}, "Polly.Core/8.4.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.Extensions/8.4.2": {"dependencies": {"Polly.Core": "8.4.2"}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.RateLimiting/8.4.2": {"dependencies": {"Polly.Core": "8.4.2"}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Scalar.AspNetCore/2.1.16": {"runtime": {"lib/net9.0/Scalar.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Serilog/4.2.1-dev-02352": {"runtime": {"lib/net9.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.AspNetCore/9.0.0": {"dependencies": {"Serilog": "4.2.1-dev-02352", "Serilog.Extensions.Hosting": "9.0.1-dev-02307", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Settings.Configuration": "9.0.1-dev-02317", "Serilog.Sinks.Console": "6.0.1-dev-00953", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"lib/net9.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Environment/3.0.1": {"dependencies": {"Serilog": "4.2.1-dev-02352"}, "runtime": {"lib/net8.0/Serilog.Enrichers.Environment.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Thread/4.0.0": {"dependencies": {"Serilog": "4.2.1-dev-02352"}, "runtime": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Expressions/5.1.0-dev-02301": {"dependencies": {"Serilog": "4.2.1-dev-02352"}, "runtime": {"lib/net9.0/Serilog.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/9.0.1-dev-02307": {"dependencies": {"Serilog": "4.2.1-dev-02352", "Serilog.Extensions.Logging": "9.0.1"}, "runtime": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/9.0.1": {"dependencies": {"Serilog": "4.2.1-dev-02352"}, "runtime": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/3.0.0": {"dependencies": {"Serilog": "4.2.1-dev-02352"}, "runtime": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/9.0.1-dev-02317": {"dependencies": {"Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.2.1-dev-02352"}, "runtime": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/6.0.1-dev-00953": {"dependencies": {"Serilog": "4.2.1-dev-02352"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/3.0.0": {"dependencies": {"Serilog": "4.2.1-dev-02352"}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.2.1-dev-02352"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Serilog.Sinks.OpenTelemetry/4.2.0-dev-02302": {"dependencies": {"Google.Protobuf": "3.30.1", "Grpc.Net.Client": "2.70.0", "Serilog": "4.2.1-dev-02352"}, "runtime": {"lib/net9.0/Serilog.Sinks.OpenTelemetry.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Seq/9.0.0": {"dependencies": {"Serilog": "4.2.1-dev-02352", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"lib/net6.0/Serilog.Sinks.Seq.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SixLabors.ImageSharp/3.1.7": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.7.0"}}}, "Stub.System.Data.SQLite.Core.NetStandard/*********": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "runtimeTargets": {"runtimes/linux-x64/native/SQLite.Interop.dll": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/SQLite.Interop.dll": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/SQLite.Interop.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x86/native/SQLite.Interop.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*********"}}}, "Swashbuckle.AspNetCore/8.1.1": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "8.1.1", "Swashbuckle.AspNetCore.SwaggerGen": "8.1.1", "Swashbuckle.AspNetCore.SwaggerUI": "8.1.1"}}, "Swashbuckle.AspNetCore.Swagger/8.1.1": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.1274"}}}, "Swashbuckle.AspNetCore.SwaggerGen/8.1.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "8.1.1"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.1274"}}}, "Swashbuckle.AspNetCore.SwaggerUI/8.1.1": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.1.1274"}}}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.Configuration.ConfigurationManager/9.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Data.Odbc/8.0.0": {"runtime": {"lib/net8.0/System.Data.Odbc.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/freebsd/lib/net8.0/System.Data.Odbc.dll": {"rid": "freebsd", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/illumos/lib/net8.0/System.Data.Odbc.dll": {"rid": "illumos", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/ios/lib/net8.0/System.Data.Odbc.dll": {"rid": "ios", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/linux/lib/net8.0/System.Data.Odbc.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/osx/lib/net8.0/System.Data.Odbc.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/solaris/lib/net8.0/System.Data.Odbc.dll": {"rid": "solaris", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/tvos/lib/net8.0/System.Data.Odbc.dll": {"rid": "tvos", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/win/lib/net8.0/System.Data.Odbc.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Data.OleDb/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0", "System.Diagnostics.PerformanceCounter": "8.0.0"}, "runtime": {"lib/net6.0/System.Data.OleDb.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Data.OleDb.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Data.SQLite.Core/*********": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "*********"}}, "System.Diagnostics.EventLog/10.0.0-preview.3.25171.5": {"runtime": {"lib/net10.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}, "runtimeTargets": {"runtimes/win/lib/net10.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "System.Diagnostics.PerformanceCounter/8.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0"}, "runtime": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.DirectoryServices.Protocols/8.0.0": {"runtime": {"lib/net8.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/linux/lib/net8.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/osx/lib/net8.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "runtimes/win/lib/net8.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "System.Memory.Data/1.0.2": {"runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Security.Cryptography.Pkcs/9.0.0": {}, "System.Security.Cryptography.ProtectedData/9.0.0": {"runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Security.Permissions/8.0.0": {"dependencies": {"System.Windows.Extensions": "8.0.0"}, "runtime": {"lib/net8.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Windows.Extensions/8.0.0": {"runtime": {"lib/net8.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "WorkQueue/1.3.0": {"runtime": {"lib/netstandard2.0/WorkQueue.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.3.0.0"}}}, "ZstdSharp.Port/0.8.0": {"runtime": {"lib/net8.0/ZstdSharp.dll": {"assemblyVersion": "0.8.0.0", "fileVersion": "0.8.0.0"}}}, "SSIC.Entity/1.0.0": {"dependencies": {"FreeSql": "3.5.202", "Microsoft.DependencyValidation.Analyzers": "0.11.0"}, "runtime": {"SSIC.Entity.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SSIC.Infrastructure/1.0.0": {"dependencies": {"Dapr.AspNetCore": "1.15.3", "FreeRedis": "1.3.6", "FreeScheduler": "2.0.33", "FreeSql.All": "3.5.202", "FreeSql.Cloud": "2.0.1", "Mapster": "7.4.2-pre02", "Masa.Contrib.Development.DaprStarter.AspNetCore": "1.2.0-preview.6", "Microsoft.AspNetCore.Authentication.JwtBearer": "10.0.0-preview.3.25172.1", "Microsoft.AspNetCore.OpenApi": "9.0.4", "Microsoft.Data.SqlClient": "6.0.1", "Microsoft.DependencyValidation.Analyzers": "0.11.0", "Microsoft.Extensions.Configuration": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Configuration.Json": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.DependencyInjection": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Hosting": "10.0.0-preview.3.25171.5", "Microsoft.Extensions.Http.Resilience": "9.4.0", "Microsoft.Extensions.ServiceDiscovery": "9.2.0", "MiniProfiler.AspNetCore": "4.5.4", "MiniProfiler.AspNetCore.Mvc": "4.5.4", "Npgsql": "9.0.3", "OpenTelemetry": "1.11.2", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.11.2", "OpenTelemetry.Exporter.Zipkin": "1.11.2", "OpenTelemetry.Extensions.Hosting": "1.11.2", "OpenTelemetry.Instrumentation.AspNetCore": "1.11.1", "OpenTelemetry.Instrumentation.GrpcCore": "1.0.0-beta.6", "OpenTelemetry.Instrumentation.GrpcNetClient": "1.11.0-beta.2", "OpenTelemetry.Instrumentation.Http": "1.11.1", "OpenTelemetry.Instrumentation.Process": "1.11.0-beta.2", "OpenTelemetry.Instrumentation.Runtime": "1.11.1", "Scalar.AspNetCore": "2.1.16", "Serilog": "4.2.1-dev-02352", "Serilog.AspNetCore": "9.0.0", "Serilog.Enrichers.Environment": "3.0.1", "Serilog.Enrichers.Thread": "4.0.0", "Serilog.Expressions": "5.1.0-dev-02301", "Serilog.Extensions.Hosting": "9.0.1-dev-02307", "Serilog.Extensions.Logging": "9.0.1", "Serilog.Settings.Configuration": "9.0.1-dev-02317", "Serilog.Sinks.Console": "6.0.1-dev-00953", "Serilog.Sinks.OpenTelemetry": "4.2.0-dev-02302", "Serilog.Sinks.Seq": "9.0.0", "SixLabors.ImageSharp": "3.1.7", "Swashbuckle.AspNetCore": "8.1.1"}, "runtime": {"SSIC.Infrastructure.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"SSIC.Modules.Auth/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Aspire.Seq/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gtE8kcIhayRV21uP4MwWw4kdMJbQgWwkCWjQQo6l6NxLDd6fC3InQ5zbxkHmcbHJu1e+9pV9i5jQfWCZrH5Wdw==", "path": "aspire.seq/9.2.0", "hashPath": "aspire.seq.9.2.0.nupkg.sha512"}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "BouncyCastle.Cryptography/2.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-buwoISwecYke3CmgG1AQSg+sNZjJeIb93vTAtJiHZX35hP/teYMxsfg0NDXGUKjGx6BKBTNKc77O2M3vKvlXZQ==", "path": "bouncycastle.cryptography/2.3.1", "hashPath": "bouncycastle.cryptography.2.3.1.nupkg.sha512"}, "Dapr.AspNetCore/1.15.3": {"type": "package", "serviceable": true, "sha512": "sha512-ib3/DiXuufZ1S9aOuK2iSbG1T+7LIjKYUHlbwkMCSeLvmcjw9a3NG8Bfl5hafhouZvy1zWytqCU92W3zOKrPqg==", "path": "dapr.aspnetcore/1.15.3", "hashPath": "dapr.aspnetcore.1.15.3.nupkg.sha512"}, "Dapr.Client/1.15.3": {"type": "package", "serviceable": true, "sha512": "sha512-fywkkZuihZThBg4NGg5GKQWIFzQ1VwMjgMxkvLb3gU3u1o8RadnEtRG2kCk5ASjAure9j01ZGgUNhcK6rGlMZQ==", "path": "dapr.client/1.15.3", "hashPath": "dapr.client.1.15.3.nupkg.sha512"}, "Dapr.Common/1.15.3": {"type": "package", "serviceable": true, "sha512": "sha512-i1DHd059p0dfihRWaosO7kZmPwvKPUtklVgd/oNo1PjCht1fKrYEanDDulPpw3k91FXhZYtokstnpXCzqm/8JQ==", "path": "dapr.common/1.15.3", "hashPath": "dapr.common.1.15.3.nupkg.sha512"}, "Dapr.Protos/1.15.3": {"type": "package", "serviceable": true, "sha512": "sha512-jhiwl6yGQx9F5Hms5y4HQVHPcW6u/IrcH9y/eICVUCBrwuMPOrq8GYWnNWqOXl0RrlwnUB0M2qVag+A5bkc0xA==", "path": "dapr.protos/1.15.3", "hashPath": "dapr.protos.1.15.3.nupkg.sha512"}, "DM.DmProvider/8.3.1.28188": {"type": "package", "serviceable": true, "sha512": "sha512-yd8bw6ClaoP5vAAPQ6BHiRqFqB41lKXyvzgKu05YfFQkiDzK/RQQBj7jmgWkeTFt1cxJvebHv//xBGfnHa2gnw==", "path": "dm.d<PERSON><PERSON><PERSON>/8.3.1.28188", "hashPath": "dm.dmprovider.8.3.1.28188.nupkg.sha512"}, "FreeRedis/1.3.6": {"type": "package", "serviceable": true, "sha512": "sha512-gzg3M5OFJkggG97igYPeLYFEkQw3R1x1I/0cWXisdxQYJe5IdJGxLHJzDhNy+V7bTiBLeLP/92oLX+cXfGYU3w==", "path": "freeredis/1.3.6", "hashPath": "freeredis.1.3.6.nupkg.sha512"}, "FreeScheduler/2.0.33": {"type": "package", "serviceable": true, "sha512": "sha512-CocKxr1l7xl1VjTESwOwtyHeqB0yRuxadrr1qzzd59ANoFYGpqq2KwV5JkyOxTAi+BDrjOmasphDAe1DT9YbEg==", "path": "freescheduler/2.0.33", "hashPath": "freescheduler.2.0.33.nupkg.sha512"}, "FreeSql/3.5.202": {"type": "package", "serviceable": true, "sha512": "sha512-ZlQjx+01HWIyvX72d0EwwodHRRI08N5ejiTa9zG66/mBTGSXOIVGpNCW4KA3WS9AO3FlDxDE2JRcQQ1uei8PYA==", "path": "freesql/3.5.202", "hashPath": "freesql.3.5.202.nupkg.sha512"}, "FreeSql.All/3.5.202": {"type": "package", "serviceable": true, "sha512": "sha512-0nVeSaLY6EKeTTz+T13iApOSksZN35YaKVFEYQsSEe7w2FlXhEWT0ReSXlHoBoFwv2nlsNR5t26We7LH+2g9SQ==", "path": "freesql.all/3.5.202", "hashPath": "freesql.all.3.5.202.nupkg.sha512"}, "FreeSql.Cloud/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Mof1EcdfQLPGnerhbDaDiWQ1maRBSXC3Pm0DvGxsPvqpMm6Zxeh1Lgvh8+ubBjFzXLZATgqgb1c5S+VX1QOjBA==", "path": "freesql.cloud/2.0.1", "hashPath": "freesql.cloud.2.0.1.nupkg.sha512"}, "FreeSql.DbContext/3.5.202": {"type": "package", "serviceable": true, "sha512": "sha512-vYtgI6zQheo+136wWhrOk0HILMJTe34sdE4jfHAn8HoEDb/Lj27UEy5BIr89g5qo+aEzPIpmhn42HZilJ2v+1g==", "path": "freesql.dbcontext/3.5.202", "hashPath": "freesql.dbcontext.3.5.202.nupkg.sha512"}, "FreeSql.Provider.Dameng/3.5.202": {"type": "package", "serviceable": true, "sha512": "sha512-OASIuh4q0Cai0+z8gsa1X7XSbtbSMLRNQcN37kE3R/JDHoMPTPqnza0P0PFxROp2cAUNjeRzdLTlFkhVoxafuA==", "path": "freesql.provider.dameng/3.5.202", "hashPath": "freesql.provider.dameng.3.5.202.nupkg.sha512"}, "FreeSql.Provider.MsAccess/3.5.202": {"type": "package", "serviceable": true, "sha512": "sha512-pUk4iZsKpX+HJ8l+8l8ihZXGR9PnJHhFkBLpMZnY5l1VdjJo5BHT5pls/QEeVR069sgrntwTJnfEQyXd4AAjOA==", "path": "freesql.provider.msaccess/3.5.202", "hashPath": "freesql.provider.msaccess.3.5.202.nupkg.sha512"}, "FreeSql.Provider.MySql/3.5.202": {"type": "package", "serviceable": true, "sha512": "sha512-4fHTY4JQfv3A4tpZAQ8+Q7erqwkj06oqONDRa6Lob1MqpY3IJ1ytG2bzVXTrae0cdDNhiCjwCwgoQxU0tjStkQ==", "path": "freesql.provider.mysql/3.5.202", "hashPath": "freesql.provider.mysql.3.5.202.nupkg.sha512"}, "FreeSql.Provider.Odbc/3.5.202": {"type": "package", "serviceable": true, "sha512": "sha512-fHUWjLVIJmyUpaFutAYo6hCDxzmVJZjiBohWGg/wkyyEdL4BzeHu7PaG7M0S8QALqdFnscU/JvUoqJO8Na1pkg==", "path": "freesql.provider.odbc/3.5.202", "hashPath": "freesql.provider.odbc.3.5.202.nupkg.sha512"}, "FreeSql.Provider.Oracle/3.5.202": {"type": "package", "serviceable": true, "sha512": "sha512-251FaD08GZsh5BbDTKnXY1GePo/qhWs8m+SqGogN3hggFp9UfwH+QbmSQ51uf60CgiGnwZcYKMJxPdK+JdDPzA==", "path": "freesql.provider.oracle/3.5.202", "hashPath": "freesql.provider.oracle.3.5.202.nupkg.sha512"}, "FreeSql.Provider.PostgreSQL/3.5.202": {"type": "package", "serviceable": true, "sha512": "sha512-Jewh5dHREeJAbarSqVxPjaOXRnC2guPtpuaIe34RZ364fT1HhZqfgY1uxhCBIub72jPUcHXq4S2A8DOrC14BuQ==", "path": "freesql.provider.postgresql/3.5.202", "hashPath": "freesql.provider.postgresql.3.5.202.nupkg.sha512"}, "FreeSql.Provider.Sqlite/3.5.202": {"type": "package", "serviceable": true, "sha512": "sha512-B/1FqmNZFJqHPO469eSQSfnETeD0NzWqEiOPRiKmgjtplbH9xF4tBwCzZulZeZ9XEPXfeMqo9oR6TE+L5zV74Q==", "path": "freesql.provider.sqlite/3.5.202", "hashPath": "freesql.provider.sqlite.3.5.202.nupkg.sha512"}, "FreeSql.Provider.SqlServer/3.5.202": {"type": "package", "serviceable": true, "sha512": "sha512-0PQGdg64gBypdameJgdJp0PH24ai020+2A9yjDiI53X4osjy0c24uuc2u/bEbdzhpQs9Zcl1PWJ4rihqBq8FuA==", "path": "freesql.provider.sqlserver/3.5.202", "hashPath": "freesql.provider.sqlserver.3.5.202.nupkg.sha512"}, "FreeSql.Repository/3.5.202": {"type": "package", "serviceable": true, "sha512": "sha512-NJ5PVMGAUjl90lk14ylsOPocFCu9KfH4xWnYZDfMiU9Vs1o4kYRhxfXzhWHhHcfvNloaXV60LZtmZNVAg5uoXQ==", "path": "freesql.repository/3.5.202", "hashPath": "freesql.repository.3.5.202.nupkg.sha512"}, "Google.Api.CommonProtos/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-mxXNYkHYL7T6NG8cdbodxpixQZfpozBkZj2g+jGCB/sO13RqfUvh9YHd8G2QI5HP24brl+nILJReC3/gzgsrrg==", "path": "google.api.commonprotos/2.2.0", "hashPath": "google.api.commonprotos.2.2.0.nupkg.sha512"}, "Google.Protobuf/3.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-HeWXDQBabQn/sCGicbeLJ0HMunknfC4FdLrOQOsaMJHcpqx3HVIpyyJqTrqJlWnza870twhOb+rBcaTiC/TlNA==", "path": "google.protobuf/3.30.1", "hashPath": "google.protobuf.3.30.1.nupkg.sha512"}, "Grpc.Core.Api/2.70.0": {"type": "package", "serviceable": true, "sha512": "sha512-66UotvWcSIq41oiQhLWcQACyKPM4umxXNiht5DQTLZJfNwEswWOcS7Z0xIEHyNIBE7ZpjotH22bEjTkvhPxmVw==", "path": "grpc.core.api/2.70.0", "hashPath": "grpc.core.api.2.70.0.nupkg.sha512"}, "Grpc.Net.Client/2.70.0": {"type": "package", "serviceable": true, "sha512": "sha512-xNv0FFCVJa5S1beUtye82WFCxKThuE1jbN8DO1x1Rj8VSIWXLBUmfSID5a1fGzsU2R/EMfwPoWclJ2RMfQuGXw==", "path": "grpc.net.client/2.70.0", "hashPath": "grpc.net.client.2.70.0.nupkg.sha512"}, "Grpc.Net.Common/2.70.0": {"type": "package", "serviceable": true, "sha512": "sha512-rBdEUMyCwa+iB8mqC6JKyPbj3SBHHkReJj/yy/XKJI63GcG6w9DJMMGTVcYHqq4Ci2W4m0HT4jt2pFfFscar8g==", "path": "grpc.net.common/2.70.0", "hashPath": "grpc.net.common.2.70.0.nupkg.sha512"}, "IdleBus/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-jMXWcNXGsUrES2QYyrygKx4YFNsGX0NrYHcMVK5lNH4L8UB8RltLb/SXMPLjVzh+usXXRXcEJTqLNZgy+rq/xw==", "path": "idlebus/1.5.3", "hashPath": "idlebus.1.5.3.nupkg.sha512"}, "K4os.Compression.LZ4/1.3.8": {"type": "package", "serviceable": true, "sha512": "sha512-LhwlPa7c1zs1OV2XadMtAWdImjLIsqFJPoRcIWAadSRn0Ri1DepK65UbWLPmt4riLqx2d40xjXRk0ogpqNtK7g==", "path": "k4os.compression.lz4/1.3.8", "hashPath": "k4os.compression.lz4.1.3.8.nupkg.sha512"}, "K4os.Compression.LZ4.Streams/1.3.8": {"type": "package", "serviceable": true, "sha512": "sha512-P15qr8dZAeo9GvYbUIPEYFQ0MEJ0i5iqr37wsYeRC3la2uCldOoeCa6to0CZ1taiwxIV+Mk8NGuZi+4iWivK9w==", "path": "k4os.compression.lz4.streams/1.3.8", "hashPath": "k4os.compression.lz4.streams.1.3.8.nupkg.sha512"}, "K4os.Hash.xxHash/1.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Wp2F7BamQ2Q/7Hk834nV9vRQapgcr8kgv9Jvfm8J3D0IhDqZMMl+a2yxUq5ltJitvXvQfB8W6K4F4fCbw/P6YQ==", "path": "k4os.hash.xxhash/1.0.8", "hashPath": "k4os.hash.xxhash.1.0.8.nupkg.sha512"}, "Mapster/7.4.2-pre02": {"type": "package", "serviceable": true, "sha512": "sha512-ry6YPGImX4aL0+Jj0rn46vqYOxzeyKOdlr6LUPvby1NLc68E5T2gRhDVWxvFexgZGOIW/U8NM87kygE4xl7Qvw==", "path": "mapster/7.4.2-pre02", "hashPath": "mapster.7.4.2-pre02.nupkg.sha512"}, "Mapster.Core/1.2.3-pre02": {"type": "package", "serviceable": true, "sha512": "sha512-FDVPfGvCz1RRb1PYeoddiJftxo+ANAifiaqkZbH2kijZT2GcoR1w6byLQ8Czc9YBuNZqHHV4LKeXZA+eoFuCTw==", "path": "mapster.core/1.2.3-pre02", "hashPath": "mapster.core.1.2.3-pre02.nupkg.sha512"}, "Masa.BuildingBlocks.Configuration/1.2.0-preview.6": {"type": "package", "serviceable": true, "sha512": "sha512-5h6ipi7b6/zXqYvGiFM0XMpqcccKk3sH/HCV8Ac9rp9g5pvh6/n2RSlVdVQ/xqh5eT/x0I0fvOVs0B/DDI7yng==", "path": "masa.buildingblocks.configuration/1.2.0-preview.6", "hashPath": "masa.buildingblocks.configuration.1.2.0-preview.6.nupkg.sha512"}, "Masa.BuildingBlocks.Data/1.2.0-preview.6": {"type": "package", "serviceable": true, "sha512": "sha512-1bxvLtH5CsQ41G47XqRMxeOlAqJDO/WsZT9eFy3QmJEHODCSY7c1i4vm6/1Bcsk9bUd/YroHW7pVFql4xRU1yA==", "path": "masa.buildingblocks.data/1.2.0-preview.6", "hashPath": "masa.buildingblocks.data.1.2.0-preview.6.nupkg.sha512"}, "Masa.BuildingBlocks.Data.Contracts/1.2.0-preview.6": {"type": "package", "serviceable": true, "sha512": "sha512-d5T4S1Ha5NhQOLu4ucqndKzyDpD3g9URfogNfHlUA8Lk5til/aqntxzCip0KU+bHqjyZQeT6DwJFl6hKbFKoNA==", "path": "masa.buildingblocks.data.contracts/1.2.0-preview.6", "hashPath": "masa.buildingblocks.data.contracts.1.2.0-preview.6.nupkg.sha512"}, "Masa.BuildingBlocks.Development.DaprStarter/1.2.0-preview.6": {"type": "package", "serviceable": true, "sha512": "sha512-kVAzWmW1laDVVov3C8pfO4iuERm6ZpMU07DdhVbuRSHIuMZjdCUQn1J+xKXFfHcQtjSZUZ1XGjHfjfTy5VguPw==", "path": "masa.buildingblocks.development.daprstarter/1.2.0-preview.6", "hashPath": "masa.buildingblocks.development.daprstarter.1.2.0-preview.6.nupkg.sha512"}, "Masa.BuildingBlocks.Exceptions/1.2.0-preview.6": {"type": "package", "serviceable": true, "sha512": "sha512-JFE8WE0y/Bbg46q/Dyqu45xBLyCiBUX0ysRI768kQqw2dvCx4A5HBAde6kz1UAaXAyZDzKxzgfOPEqWKKOFFsg==", "path": "masa.buildingblocks.exceptions/1.2.0-preview.6", "hashPath": "masa.buildingblocks.exceptions.1.2.0-preview.6.nupkg.sha512"}, "Masa.BuildingBlocks.Globalization.I18n/1.2.0-preview.6": {"type": "package", "serviceable": true, "sha512": "sha512-q99h9C8BoFNfy4dMxBvOkP2Hmw9ojiMN+xswUdjWQtI0N4Fhq6vzRTwakOrfJ62sKVeYWLvugnRbcfdX3IWbFg==", "path": "masa.buildingblocks.globalization.i18n/1.2.0-preview.6", "hashPath": "masa.buildingblocks.globalization.i18n.1.2.0-preview.6.nupkg.sha512"}, "Masa.Contrib.Development.DaprStarter/1.2.0-preview.6": {"type": "package", "serviceable": true, "sha512": "sha512-uSB4rvW55fZbj6NDFc23MYzj347GoZY2960/dNWYfDQsFwpaYJuk+Ohu8G2OtI09A1cDqOPOVt5MMMrlA15VQg==", "path": "masa.contrib.development.daprstarter/1.2.0-preview.6", "hashPath": "masa.contrib.development.daprstarter.1.2.0-preview.6.nupkg.sha512"}, "Masa.Contrib.Development.DaprStarter.AspNetCore/1.2.0-preview.6": {"type": "package", "serviceable": true, "sha512": "sha512-zkGph8iYJvRTqgjYXndOulBCPeVG0DBuCef0EF/o9Sq7Cv4J4xZTURWeG7xrYAL/r+ptgosDtFAXfqt853wvUw==", "path": "masa.contrib.development.daprstarter.aspnetcore/1.2.0-preview.6", "hashPath": "masa.contrib.development.daprstarter.aspnetcore.1.2.0-preview.6.nupkg.sha512"}, "Masa.Utils.Caching.Memory/1.2.0-preview.6": {"type": "package", "serviceable": true, "sha512": "sha512-f15HxzZfVBS/Ef+eDQMGp1c5OI0P4IIE+48xjm3bl4stZwX6zBWyqzTP5tygp9pCnpiuaU1mTgNIo0g26+Kfeg==", "path": "masa.utils.caching.memory/1.2.0-preview.6", "hashPath": "masa.utils.caching.memory.1.2.0-preview.6.nupkg.sha512"}, "Masa.Utils.Extensions.DotNet/1.2.0-preview.6": {"type": "package", "serviceable": true, "sha512": "sha512-AJ<PERSON>+RaxPSFZkB+2zv3qihQMrYIHznv93MsAHSytFDog80gCAJWPmXkTMpZtpYfm0gb0DyBzITpdZ+z2L7SidRA==", "path": "masa.utils.extensions.dotnet/1.2.0-preview.6", "hashPath": "masa.utils.extensions.dotnet.1.2.0-preview.6.nupkg.sha512"}, "Masa.Utils.Models.Config/1.2.0-preview.6": {"type": "package", "serviceable": true, "sha512": "sha512-9P+1BIrA4YufGm83MBDxT01tA0QoFb/+jkuWlG2ILXjRAWhajGQPKB3E/afx4dJuxTuJPrxBV58l6yc1kuJ86w==", "path": "masa.utils.models.config/1.2.0-preview.6", "hashPath": "masa.utils.models.config.1.2.0-preview.6.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/10.0.0-preview.3.25172.1": {"type": "package", "serviceable": true, "sha512": "sha512-6B<PERSON>akHueKMQ6w5b5uFL8h7hSJS70ov0iL2q+Y/0noO2tclK2ZRKJCJZX9iNw3KMHHXjxDbTHIM1QRA9mUwhO5A==", "path": "microsoft.aspnetcore.authentication.jwtbearer/10.0.0-preview.3.25172.1", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.10.0.0-preview.3.25172.1.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-GfZWPbZz1aAtEO3wGCkpeyRc0gzr/+VRHnUgY/YjqVPDlHbeKWCXw3IxKarQdo9myC2O1QBf652Mo50QqbXYRg==", "path": "microsoft.aspnetcore.openapi/9.0.4", "hashPath": "microsoft.aspnetcore.openapi.9.0.4.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.Bcl.Cryptography/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tjfuEv+QOznFL1bEPa7svmjpbNvDIrwdinMNy/HhrToQQpONW4hdp0Sans55Rcy9KB3z60duBeey89JY1VQOvg==", "path": "microsoft.bcl.cryptography/9.0.0", "hashPath": "microsoft.bcl.cryptography.9.0.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-v7HxnYYXGGCJilxeQ4Pdks+popVuGajBpHmau0RU4ACIcbfs5qCNUnCogGpZ+CJ//8Qafhxq7vc5a8L9d6O8Eg==", "path": "microsoft.data.sqlclient/6.0.1", "hashPath": "microsoft.data.sqlclient.6.0.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-f+pRODTWX7Y67jXO3T5S2dIPZ9qMJNySjlZT/TKmWVNWe19N8jcWmHaqHnnchaq3gxEKv1SWVY5EFzOD06l41w==", "path": "microsoft.data.sqlclient.sni.runtime/6.0.2", "hashPath": "microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512"}, "Microsoft.DependencyValidation.Analyzers/0.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-3xBVhMsM4o9cDpsvmlv78T1FG+cTH8i6bjl1Irk6hoIwEc6Ekoz4mKAR6Hn5M/2G2ZsHNrYBNwGkgF7aiw74UA==", "path": "microsoft.dependencyvalidation.analyzers/0.11.0", "hashPath": "microsoft.dependencyvalidation.analyzers.0.11.0.nupkg.sha512"}, "Microsoft.Extensions.AmbientMetadata.Application/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-zcjQBbp4whM4nxWmMg4P8JxTikB0dVxjKWHrGiz+6WVBgRctq9tSZgpbl/ywz06N+ZLzlooeWQOYgbpEOXOUYQ==", "path": "microsoft.extensions.ambientmetadata.application/9.4.0", "hashPath": "microsoft.extensions.ambientmetadata.application.9.4.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Compliance.Abstractions/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-jDYE/o2Y/s9Tz896whtMEEUgCHPLSlzRXPRD6v2J1lJxnuuRcWIZ6ASJi6jSI4e9hbkpFTx8LtsL4CNB6bYbaQ==", "path": "microsoft.extensions.compliance.abstractions/9.4.0", "hashPath": "microsoft.extensions.compliance.abstractions.9.4.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-Rnhi/o4Rw9edwOfyJoR5PA6052UFhGpdkWs5mrgYskRXXrof+CCN6BOpl2RPLCRbTQWsFcZKm5arubjmFMNGVg==", "path": "microsoft.extensions.configuration/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.configuration.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-J9Scx8s52LsDCVuIYWJ7M/rRHyYAzF1D9skARHrZkKFrhDxksOdhXmrGJWnSJce2CqQhVx+n6fZtItmyhXpyWQ==", "path": "microsoft.extensions.configuration.abstractions/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.configuration.abstractions.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-5E0EgvEqwt8hy8Tg+XhOfTvYZ5c1F8zCavOZMQuW6PBfO/BZWdo5xVQcBIBAJRLKemr+fOSwBrk5gGWpSBk/sg==", "path": "microsoft.extensions.configuration.binder/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.configuration.binder.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-DKM1IOddj8pulnLVgEr4RrdcTHg4B9F046xoREjrxVWrxfVbd5DTfxj6QVIi1BUySshp/TM4NnaBBWIXrObAdw==", "path": "microsoft.extensions.configuration.commandline/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.configuration.commandline.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-rnePvgygRP4Q5as3BzXoxfR4hX/cnTNF5eroVRwb8I7sYu6JrgO85h7Y3M15jUltCb8yCRI7/yPberFukFxIyw==", "path": "microsoft.extensions.configuration.environmentvariables/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.configuration.environmentvariables.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-ltZ3XANHXesoBLZQQ3aKNfjVwP+4fThVvTJgAiuBee0HCnlQm3NQ3l61JcJexppj0dVYG54+mR2nrBGq9lQTXA==", "path": "microsoft.extensions.configuration.fileextensions/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.configuration.fileextensions.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-knqFdM+cblgKWgqZGBkzgADiO+2GfLsteztGyHvE6o47xGHP42ImBbanW3GknlhuLSuT404lT3GoW6kNhzfjqQ==", "path": "microsoft.extensions.configuration.json/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.configuration.json.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-j0FJdMAcmZN6k5r0tG6I8Uva9hLABFiuztxT2FJ1/9a1smnVJWUJtsk+OiS1ZKZ1Y6Tpm0/sVtuyCdRXrzAkNA==", "path": "microsoft.extensions.configuration.usersecrets/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.configuration.usersecrets.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-hghZ1OOgfgnvRJfetnomCqpszI3ocxIFEfhNHmLXWZM+Ec3oM1FBKOEvRjhl6R4aghCzUCOHDLww+DiKiTV/yA==", "path": "microsoft.extensions.dependencyinjection/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.dependencyinjection.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-kYUb9+gyWtphwC5ZPApnhJ8kYLFfeMwt0qHgMchfagjCVFFuwp4iyn9tSYe/mDmlyMxfaZ8XmatJttmXxfQT5g==", "path": "microsoft.extensions.dependencyinjection.abstractions/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-dg2VeBxiwqAszJkMRQT1k1jefeTneN+M8v/EkWwebR9OvLXzykP9CVE2+bWMuPvXIdHM04/ffWgo5Bl+0jKi/Q==", "path": "microsoft.extensions.dependencyinjection.autoactivation/9.4.0", "hashPath": "microsoft.extensions.dependencyinjection.autoactivation.9.4.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "path": "microsoft.extensions.dependencymodel/9.0.0", "hashPath": "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-6X4NWytDZnYVX1Its8m2c8n383Zt3kBoYrMo4cvpGmCP6t5zNXUiQRDg69fD7PShE642/LPn5jZYVBKs4hpZxg==", "path": "microsoft.extensions.diagnostics/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.diagnostics.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-8dzQGZHA3PseG1h3nbY7kGjf87WuF+sZyfIonsxip0TMSpdJziuRilzHmL7IfksKHE/QUVCCx58plencvXRwJQ==", "path": "microsoft.extensions.diagnostics.abstractions/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.diagnostics.abstractions.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ht5TQmWfYh7cbZTPTTWAUkgQmJhDg1JINvXSnxutx1sfZL1gkk1OXoF0Uh2383FLk9cm4le/cWfdgDslFeVigA==", "path": "microsoft.extensions.diagnostics.exceptionsummarization/9.4.0", "hashPath": "microsoft.extensions.diagnostics.exceptionsummarization.9.4.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-qk6MBLdczml70Bfa/+RhZVi/8Ism+4dCze+y86vG5DzM4EM2dc2C5RWuILNJ6dOBx5pEGi85sx+qKoIggkNItg==", "path": "microsoft.extensions.fileproviders.abstractions/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.fileproviders.abstractions.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-7i6MvFzEpa/SXrED1BwSmCSS7JaW7TTsh8/9NOZrXi721mu0oZ7eKDxKhUFCXX0D7ZiUCGQgxT/yU1dcL2VaPA==", "path": "microsoft.extensions.fileproviders.physical/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.fileproviders.physical.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-/r6mj4dSfd39peS3ti6O8ndBeEgtzn+PfhQRXcRjcME/8QLcFsTGcf5s1Isk/acfZX2g7urZHx+EJJa1WrR3bQ==", "path": "microsoft.extensions.filesystemglobbing/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.filesystemglobbing.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Hosting/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-dXxGB7dDly/OS3Kx2m2kkTXmLEOVerIrpw5VUH1dVJ5GBkLhm9Sdi/gavIccOskDLUvLE02/ZhBrgGlojD6ueg==", "path": "microsoft.extensions.hosting/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.hosting.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-dJIQW8IZJIxb0Z+AQ/LUtpgSmGwMNGbuNI00tLEiY76UzAnTJ1h9FBGUpit8YWLZybtuOLe/OYDGAiPPgp4XBw==", "path": "microsoft.extensions.hosting.abstractions/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.hosting.abstractions.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Http.Diagnostics/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LaYz7FDkJ/uh5PYOTFvhQBndvgrtpjDSLiuDO2JGYcgA75uU7QXiMDHI/RmS4LzvJUkadPgXKxquTkhPotTw9Q==", "path": "microsoft.extensions.http.diagnostics/9.4.0", "hashPath": "microsoft.extensions.http.diagnostics.9.4.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Resilience/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-sml3XlyLrAnxYFTn96KaHzrzcnbkSZpxWzDffE9qs3QXloLepOa0nUjsgvhDg9InCNrd/KAuRLUzE6LCPa7r/g==", "path": "microsoft.extensions.http.resilience/9.4.0", "hashPath": "microsoft.extensions.http.resilience.9.4.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-PEOOUx9CdEf98gSPbMHJS9BDrE+Ce8vjF6PRHTGDKmX1qRZebR4qdfMDUFiaqLKrTYBSzeptPhrgYC0bSlw9Og==", "path": "microsoft.extensions.logging/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.logging.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-RelfzqJYDT+QGnda61isCoab/Xq8HWAK0tawwyCYm7r2voGs9r9igkxeFo43lAnFztVdOdslB+zQmJh0pZcXuw==", "path": "microsoft.extensions.logging.abstractions/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.logging.abstractions.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-Y0PFtW3f7dVALCx/uWEFosHIkCgOFvmNMdUZ6GTPubQHnWWY2A9A/QDf7S9wFy4i0GrFpNCBxcIk51eliyo6OQ==", "path": "microsoft.extensions.logging.configuration/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.logging.configuration.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-G3e8n/yM/nTcHbZooWbua0mYvQ5uZZKuLrotL9Fc5/XaVjsDNeOsihRXxDBbOwi3iC7d/swdA60fblhQq7B/tg==", "path": "microsoft.extensions.logging.console/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.logging.console.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-reCzUOgoPn2bUaHODgjPylrFfaLohT9+o5GPfIEGJEq9DK7lW3XJ/hS789G8ZMdYcdv+8+z81lj0xz+SZ5xE4g==", "path": "microsoft.extensions.logging.debug/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.logging.debug.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-V7t+O50scI/iepn++c2pwZM+W7aw23rbEsSDr8beSV+dR4H0WLxbF+H6/kunz287ae/xGGdMgHQf2FFVqGuguA==", "path": "microsoft.extensions.logging.eventlog/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.logging.eventlog.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-sqY8S1LJbFzfK1TDIHaPGqbIjXZHDJ0m+nZsiKPBJ/D3X9XVOh5ZO74XlqNKDkzJ6OgB+090qZmeWT87klAZ2w==", "path": "microsoft.extensions.logging.eventsource/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.logging.eventsource.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Options/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-4UwRpSTCzTHSc5D1Os5gdtzJZTP2tdMRAfidJy23munK5PVm1uFBRUlyqu5n1bWPY8n1F6lee7AixJkbMP2DcA==", "path": "microsoft.extensions.options/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.options.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-n3K8OmdhFn7M3c+6x8xPxEmeIErSwhRCBQK46QRjiyTJCuJ/EApUF5q4lXX5DmsyFfskya+M1c+GfOSFTizC6g==", "path": "microsoft.extensions.options.configurationextensions/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.options.configurationextensions.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Primitives/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-1U7p/PNQw7CGbbaAK1GTdVy/Fu85DC2h6DJHqvca12xtroVllU6KjboN590SxGxg2iz4sHM+joYpYSIYBrm0bQ==", "path": "microsoft.extensions.primitives/10.0.0-preview.3.25171.5", "hashPath": "microsoft.extensions.primitives.10.0.0-preview.3.25171.5.nupkg.sha512"}, "Microsoft.Extensions.Resilience/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xb3zb0x+2O8jGJIysLTqD7Pd9uWDepPX8kUuojE0wZkGK5FztT0nxEHvYcmbICmRRNlFFDEd0dLgBWBPOSHWoA==", "path": "microsoft.extensions.resilience/9.4.0", "hashPath": "microsoft.extensions.resilience.9.4.0.nupkg.sha512"}, "Microsoft.Extensions.ServiceDiscovery/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Q6GfOYRhYT3R0isqYu/5kF8d4Dc93H3Wsv5lKL8VGt6kd8uRxad2i6FODkLUkjFGq+c8CoKWMWaQ5mFRy349lw==", "path": "microsoft.extensions.servicediscovery/9.2.0", "hashPath": "microsoft.extensions.servicediscovery.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ChoY/eMUnlgmcyty2+aMbCLLpuh2o3IamCjIyek6F3gIUXiuajzgoREA4EihmCXXAOd9rK5Ll56hVbiOV6cxqw==", "path": "microsoft.extensions.servicediscovery.abstractions/9.2.0", "hashPath": "microsoft.extensions.servicediscovery.abstractions.9.2.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ax7GV4I2/BaiaAax+Yo1yjn2LYdw6FJP6gwCIcRMknu61iLZw8rZNx4tMz5JCGXj2ZO937NOwMcCrGN+wLB6NA==", "path": "microsoft.extensions.telemetry/9.4.0", "hashPath": "microsoft.extensions.telemetry.9.4.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry.Abstractions/9.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-QoXTK3gaW1f/1Ms2NDkKmAc+IW/yMsBTTqxphiGL4AfZNKoh8Ie7srDOaMuhInm5/Qq0xEAkIafC53v1xt7wMQ==", "path": "microsoft.extensions.telemetry.abstractions/9.4.0", "hashPath": "microsoft.extensions.telemetry.abstractions.9.4.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OtlIWcyX01olfdevPKZdIPfBEvbcioDyBiE/Z2lHsopsMD7twcKtlN9kMevHmI5IIPhFpfwCIiR6qHQz1WHUIw==", "path": "microsoft.identitymodel.abstractions/8.0.1", "hashPath": "microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s6++gF9x0rQApQzOBbSyp4jUaAlwm+DroKfL8gdOHxs83k8SJfUXhuc46rDB3rNXBQ1MVRxqKUrqFhO/M0E97g==", "path": "microsoft.identitymodel.jsonwebtokens/8.0.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UCPF2exZqBXe7v/6sGNiM6zCQOUXXQ9+v5VTb9gPB8ZSUPnX53BxlN78v2jsbIvK9Dq4GovQxo23x8JgWvm/Qg==", "path": "microsoft.identitymodel.logging/8.0.1", "hashPath": "microsoft.identitymodel.logging.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "path": "microsoft.identitymodel.protocols/8.0.1", "hashPath": "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kDimB6Dkd3nkW2oZPDkMkVHfQt3IDqO5gL0oa8WVy3OP4uE8Ij+8TXnqg9TOd9ufjsY3IDiGz7pCUbnfL18tjg==", "path": "microsoft.identitymodel.tokens/8.0.1", "hashPath": "microsoft.identitymodel.tokens.8.0.1.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "MiniProfiler.AspNetCore/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-meedJsjpYOeHPhE8H6t+dGQ9zLxcCQVpi4DXzmxmYAXywmTzlo6jv2IASUv5QijTU0CxsROln3FHd8RsTO8Z8A==", "path": "miniprofiler.aspnetcore/4.5.4", "hashPath": "miniprofiler.aspnetcore.4.5.4.nupkg.sha512"}, "MiniProfiler.AspNetCore.Mvc/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-+NqXyCy9aNdroPm6leW5+cpngtCnkCdoyOlJzvVN62uucSx+MYkx8jmKbgAt+aCP6aghADfHBExwrTIldHxapg==", "path": "miniprofiler.aspnetcore.mvc/4.5.4", "hashPath": "miniprofiler.aspnetcore.mvc.4.5.4.nupkg.sha512"}, "MiniProfiler.Shared/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-f8ckFm/xTS8C2Bn4BdVc94dNvg+tRfk0e4XFaETOqRi6r0PUOyn3Z9jTQCVpB3R1pP5WiRsEIrqqxux95BVpTA==", "path": "miniprofiler.shared/4.5.4", "hashPath": "miniprofiler.shared.4.5.4.nupkg.sha512"}, "MySql.Data/9.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-E4t/IQzcXg4nYGqrGkoGwwSWA1V2L+LKzVddPABAPcj2i6RESP2fcZQ4XFC0Wv+Cq4DlgR3DYhX/fGaZ3VxCPQ==", "path": "mysql.data/9.1.0", "hashPath": "mysql.data.9.1.0.nupkg.sha512"}, "NetTopologySuite/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3ajBClEI9wx2/DjjGmV52sHW1m52vLg8sdz1pJbTf5ySj1X90qehQs3v1DRwGo0F8UKj/Z2SjNhRN/6LroAkqg==", "path": "nettopologysuite/2.0.0", "hashPath": "nettopologysuite.2.0.0.nupkg.sha512"}, "NetTopologySuite.IO.PostGis/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3W8XTFz8iP6GQ5jDXK1/LANHiU+988k1kmmuPWNKcJLpmSg6CvFpbTpz+s4+LBzkAp64wHGOldSlkSuzYfrIKA==", "path": "nettopologysuite.io.postgis/2.1.0", "hashPath": "nettopologysuite.io.postgis.2.1.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Npgsql/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "path": "npgsql/9.0.3", "hashPath": "npgsql.9.0.3.nupkg.sha512"}, "Npgsql.LegacyPostgis/5.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-M26Ehq+08/OKpZTgaXymq9qK7WZgs6lcP379GpeA9LZt88rho8Kb1/fjVh8Ww2nh50XGwZ5DutkkNRlcXz5cxA==", "path": "npgsql.legacypostgis/5.0.11", "hashPath": "npgsql.legacypostgis.5.0.11.nupkg.sha512"}, "Npgsql.NetTopologySuite/5.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-lpCAHQitScbQXo2V9CVFjXg/hWvPgji3GHdAuvDqrlojBaKZ60Gnn1+h1ZMJIe9LvFJ2TvErdfI6WzARhn/AUQ==", "path": "npgsql.nettopologysuite/5.0.11", "hashPath": "npgsql.nettopologysuite.5.0.11.nupkg.sha512"}, "OpenTelemetry/1.11.2": {"type": "package", "serviceable": true, "sha512": "sha512-FwonkaCVW8M9DLTHmAeJ+znsQCeOVvF4vSBworyq6f55RJB62LFmK7h7SG2aNERTknxP5RoGSwGOBPcVEgC07w==", "path": "opentelemetry/1.11.2", "hashPath": "opentelemetry.1.11.2.nupkg.sha512"}, "OpenTelemetry.Api/1.11.2": {"type": "package", "serviceable": true, "sha512": "sha512-jgSd/FvtxPPc6nLaZnFj+bulHM2iQjy+NBCY5MbQjH6vkW/SfcXD9NMP3pKCmdF+SbZpgL+EoLQc+PmcnYYLlA==", "path": "opentelemetry.api/1.11.2", "hashPath": "opentelemetry.api.1.11.2.nupkg.sha512"}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.11.2": {"type": "package", "serviceable": true, "sha512": "sha512-Y1aag4WT9f3rF8jQWwub5DsFVXpM/5NQsfYg6lmsNQrtJ6TcRqQu2PubcHXeIX2N6TA7XF3ffQAgeJklsSLeoQ==", "path": "opentelemetry.api.providerbuilderextensions/1.11.2", "hashPath": "opentelemetry.api.providerbuilderextensions.1.11.2.nupkg.sha512"}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.11.2": {"type": "package", "serviceable": true, "sha512": "sha512-37dQ85HHU+szAJY9ePSSXl5TNQp7iZm4Q+Dr9gQ8kOyJiHk7xsQUSjZrFA0Mzu74Jmi2WWPZ4p13BCcHXzFolg==", "path": "opentelemetry.exporter.opentelemetryprotocol/1.11.2", "hashPath": "opentelemetry.exporter.opentelemetryprotocol.1.11.2.nupkg.sha512"}, "OpenTelemetry.Exporter.Zipkin/1.11.2": {"type": "package", "serviceable": true, "sha512": "sha512-Kv+pF7B3jGN3kHjPPclOZLrnZD4164Bv+Rp79/xPw0TMlpaXZzHnwTywHY+jKaP+vlWkVKXMBOzH4rb43aTqoA==", "path": "opentelemetry.exporter.zipkin/1.11.2", "hashPath": "opentelemetry.exporter.zipkin.1.11.2.nupkg.sha512"}, "OpenTelemetry.Extensions.Hosting/1.11.2": {"type": "package", "serviceable": true, "sha512": "sha512-X0SZcZM9nv7+/WreH3q5McgxeaLBwN3ohsH/R58uAKeiuieqDxoAVFyQSQaRkpkrqIZSTTab6NHDQXglIreG0Q==", "path": "opentelemetry.extensions.hosting/1.11.2", "hashPath": "opentelemetry.extensions.hosting.1.11.2.nupkg.sha512"}, "OpenTelemetry.Instrumentation.AspNetCore/1.11.1": {"type": "package", "serviceable": true, "sha512": "sha512-KAzLOcCGi6TviuJWx0+QSgjVCrEnWDayN3aFSHoKzINPx/Tfwd9MIePUUDXQ+xHzrwon8IfExIDT1+UyIf3hoA==", "path": "opentelemetry.instrumentation.aspnetcore/1.11.1", "hashPath": "opentelemetry.instrumentation.aspnetcore.1.11.1.nupkg.sha512"}, "OpenTelemetry.Instrumentation.GrpcCore/1.0.0-beta.6": {"type": "package", "serviceable": true, "sha512": "sha512-Dy2W1dAI/qXYAkJ2r2lTTRERNhxf88TVid9xcwP5+wl0f8thn2HD7kP5TFmq+2PcJv3bIDi6phKRM6Frih2c8Q==", "path": "opentelemetry.instrumentation.grpccore/1.0.0-beta.6", "hashPath": "opentelemetry.instrumentation.grpccore.1.0.0-beta.6.nupkg.sha512"}, "OpenTelemetry.Instrumentation.GrpcNetClient/1.11.0-beta.2": {"type": "package", "serviceable": true, "sha512": "sha512-S1AFYRy86/3a8r8sSE/uOYQZR6BxR+HXOGY8RHZA9nwf1x32U/5sBBQsaNSjtRQdzofxXLwAMLVz8VtbIhnXMQ==", "path": "opentelemetry.instrumentation.grpcnetclient/1.11.0-beta.2", "hashPath": "opentelemetry.instrumentation.grpcnetclient.1.11.0-beta.2.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Http/1.11.1": {"type": "package", "serviceable": true, "sha512": "sha512-hyZ3H8HxtQPvipBPdkN6tJWSCvY06XVyLbKlHuJlPrlV/lQJ/QuEBKYTXqcSa21LE/Th4HV+Choxi8eTu74xyw==", "path": "opentelemetry.instrumentation.http/1.11.1", "hashPath": "opentelemetry.instrumentation.http.1.11.1.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Process/1.11.0-beta.2": {"type": "package", "serviceable": true, "sha512": "sha512-XLLKYK9zRS3iT/GhkXRPKJUWzBxdqYEQ8zFyNxbnz3d5I8Wky/hqyIbWta9h6GmyXvqnJ2XT+pUYUf8gRukUwA==", "path": "opentelemetry.instrumentation.process/1.11.0-beta.2", "hashPath": "opentelemetry.instrumentation.process.1.11.0-beta.2.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Runtime/1.11.1": {"type": "package", "serviceable": true, "sha512": "sha512-fSAeBd8SEiWGAeCV9+p4y3QaUEn+1qOhp+VEOAEIQl2lh2j5rUM0sPRiI6PBhPjmWRn+Cm6heqXx5dsQTpBBXw==", "path": "opentelemetry.instrumentation.runtime/1.11.1", "hashPath": "opentelemetry.instrumentation.runtime.1.11.1.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/23.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-Oc8AX7xme05xrp4/aCxKBH4+bpWgMCFafXI7LbLO/7OBMJLZRXhMtejDgIb8aYvIVyV7vSdAy3LkCYcJorxn1A==", "path": "oracle.manageddataaccess.core/23.6.1", "hashPath": "oracle.manageddataaccess.core.23.6.1.nupkg.sha512"}, "Polly.Core/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "path": "polly.core/8.4.2", "hashPath": "polly.core.8.4.2.nupkg.sha512"}, "Polly.Extensions/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "path": "polly.extensions/8.4.2", "hashPath": "polly.extensions.8.4.2.nupkg.sha512"}, "Polly.RateLimiting/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "path": "polly.ratelimiting/8.4.2", "hashPath": "polly.ratelimiting.8.4.2.nupkg.sha512"}, "Scalar.AspNetCore/2.1.16": {"type": "package", "serviceable": true, "sha512": "sha512-+J7Vx0UnuCu9Y8G/LhsnMliQuwaPrGmdI7hq6VCZFa7m/nGvngfDnvTXkUayp16kdYZksqnRVL+ZxxZNJqHn9Q==", "path": "scalar.aspnetcore/2.1.16", "hashPath": "scalar.aspnetcore.2.1.16.nupkg.sha512"}, "Serilog/4.2.1-dev-02352": {"type": "package", "serviceable": true, "sha512": "sha512-ASzzh84muZz/1TcYTb6exfk8cjTTWxrBWOGX9t5FNv8VOvWRUflYLuwaSfFnyEw9sABabreRP16byMNTwwtoXg==", "path": "serilog/4.2.1-dev-02352", "hashPath": "serilog.4.2.1-dev-02352.nupkg.sha512"}, "Serilog.AspNetCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JslDajPlBsn3Pww1554flJFTqROvK9zz9jONNQgn0D8Lx2Trw8L0A8/n6zEQK1DAZWXrJwiVLw8cnTR3YFuYsg==", "path": "serilog.aspnetcore/9.0.0", "hashPath": "serilog.aspnetcore.9.0.0.nupkg.sha512"}, "Serilog.Enrichers.Environment/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9BqCE4C9FF+/rJb/CsQwe7oVf44xqkOvMwX//CUxvUR25lFL4tSS6iuxE5eW07quby1BAyAEP+vM6TWsnT3iqw==", "path": "serilog.enrichers.environment/3.0.1", "hashPath": "serilog.enrichers.environment.3.0.1.nupkg.sha512"}, "Serilog.Enrichers.Thread/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C7BK25a1rhUyr+Tp+1BYcVlBJq7M2VCHlIgnwoIUVJcicM9jYcvQK18+OeHiXw7uLPSjqWxJIp1EfaZ/RGmEwA==", "path": "serilog.enrichers.thread/4.0.0", "hashPath": "serilog.enrichers.thread.4.0.0.nupkg.sha512"}, "Serilog.Expressions/5.1.0-dev-02301": {"type": "package", "serviceable": true, "sha512": "sha512-hW7OKCr8m/5h84oTFeXSnnfqFozn05h9BfU+1kMLLFvSmJPjRhaxqVqR/zs2+g9kD8s0d9CSRCwhtZAshbyvUg==", "path": "serilog.expressions/5.1.0-dev-02301", "hashPath": "serilog.expressions.5.1.0-dev-02301.nupkg.sha512"}, "Serilog.Extensions.Hosting/9.0.1-dev-02307": {"type": "package", "serviceable": true, "sha512": "sha512-bBx2sEozyzXTv+nysstxgUxHOWbAx/viJ/SmM4ELLhjEHjj+KfGOpn2c0hn0Wb6x+9OZ3bVESsZmhtPLr4gLKw==", "path": "serilog.extensions.hosting/9.0.1-dev-02307", "hashPath": "serilog.extensions.hosting.9.0.1-dev-02307.nupkg.sha512"}, "Serilog.Extensions.Logging/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-IZo04/stVuOBhe0jzIe+6gVmiZ50i1cDljTnDyz6lqM7rbNhrHsPWg3IraJIvzZBihYPg5V9TVQYgRnm3xX8eA==", "path": "serilog.extensions.logging/9.0.1", "hashPath": "serilog.extensions.logging.9.0.1.nupkg.sha512"}, "Serilog.Formatting.Compact/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wQsv14w9cqlfB5FX2MZpNsTawckN4a8dryuNGbebB/3Nh1pXnROHZov3swtu3Nj5oNG7Ba+xdu7Et/ulAUPanQ==", "path": "serilog.formatting.compact/3.0.0", "hashPath": "serilog.formatting.compact.3.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/9.0.1-dev-02317": {"type": "package", "serviceable": true, "sha512": "sha512-1/PPRG1VvYCuFJL8Dc7lkpHNFRZq6n0cwy976CgK21qRwmAIR2GgEkzIc9LZw8TVlvSmoUhZRyeBoU7bB9TjIw==", "path": "serilog.settings.configuration/9.0.1-dev-02317", "hashPath": "serilog.settings.configuration.9.0.1-dev-02317.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.1-dev-00953": {"type": "package", "serviceable": true, "sha512": "sha512-Goi2B0Je0X0NvWYUi0SiU9MJNF2957Kfjmc6VPZ2hNl6Lmj9he6laxmDuQU/c0fBdAFnNiEUPPcHd/NJVyfbkA==", "path": "serilog.sinks.console/6.0.1-dev-00953", "hashPath": "serilog.sinks.console.6.0.1-dev-00953.nupkg.sha512"}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "path": "serilog.sinks.debug/3.0.0", "hashPath": "serilog.sinks.debug.3.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "Serilog.Sinks.OpenTelemetry/4.2.0-dev-02302": {"type": "package", "serviceable": true, "sha512": "sha512-gHUCGwgKBILRzFACMyHMOJiJil80WN3yvdpk4D5swYJW+N4RoArCfxjIu0k5bMWeQEOd1XxmT53MujzkvRr8vg==", "path": "serilog.sinks.opentelemetry/4.2.0-dev-02302", "hashPath": "serilog.sinks.opentelemetry.4.2.0-dev-02302.nupkg.sha512"}, "Serilog.Sinks.Seq/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aNU8A0K322q7+voPNmp1/qNPH+9QK8xvM1p72sMmCG0wGlshFzmtDW9QnVSoSYCj0MgQKcMOlgooovtBhRlNHw==", "path": "serilog.sinks.seq/9.0.0", "hashPath": "serilog.sinks.seq.9.0.0.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-9fIOOAsyLFid6qKypM2Iy0Z3Q9yoanV8VoYAHtI2sYGMNKzhvRTjgFDHonIiVe+ANtxIxM6SuqUzj0r91nItpA==", "path": "sixlabors.imagesharp/3.1.7", "hashPath": "sixlabors.imagesharp.3.1.7.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/*********": {"type": "package", "serviceable": true, "sha512": "sha512-WfrqQg6WL+r4H1sVKTenNj6ERLXUukUxqcjH1rqPqXadeIWccTVpydESieD7cZ/NWQVSKLYIHuoBX5du+BFhIQ==", "path": "stub.system.data.sqlite.core.netstandard/*********", "hashPath": "stub.system.data.sqlite.core.netstandard.*********.nupkg.sha512"}, "Swashbuckle.AspNetCore/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-HJHexmU0PiYevgTLvKjYkxEtclF2w4O7iTd3Ef3p6KeT0kcYLpkFVgCw6glpGS57h8769anv8G+NFi9Kge+/yw==", "path": "swashbuckle.aspnetcore/8.1.1", "hashPath": "swashbuckle.aspnetcore.8.1.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-h+8D5jQtnl6X4f2hJQwf0Khj0SnCQANzirCELjXJ6quJ4C1aNNCvJrAsQ+4fOKAMqJkvW48cKj79ftG+YoGcRg==", "path": "swashbuckle.aspnetcore.swagger/8.1.1", "hashPath": "swashbuckle.aspnetcore.swagger.8.1.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-2EuPzXSNleOOzYvziERWRLnk1Oz9i0Z1PimaUFy1SasBqeV/rG+eMfwFAMtTaf4W6gvVOzRcUCNRHvpBIIzr+A==", "path": "swashbuckle.aspnetcore.swaggergen/8.1.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.8.1.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-GDLX/MpK4oa2nYC1N/zN2UidQTtVKLPF6gkdEmGb0RITEwpJG9Gu8olKqPYnKqVeFn44JZoCS0M2LGRKXP8B/A==", "path": "swashbuckle.aspnetcore.swaggerui/8.1.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.8.1.1.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-PdkuMrwDhXoKFo/JxISIi9E8L+QGn9Iquj2OKDWHB6Y/HnUOuBouF7uS3R4Hw3FoNmwwMo6hWgazQdyHIIs27A==", "path": "system.configuration.configurationmanager/9.0.0", "hashPath": "system.configuration.configurationmanager.9.0.0.nupkg.sha512"}, "System.Data.Odbc/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c+GfnZt2/HyU+voKw2fctLZClcNjPZPWS+mnIhGvDknRMqL/fwWlREWPgA4csbp9ZkQIgB4qkufgdh/oh5Ubow==", "path": "system.data.odbc/8.0.0", "hashPath": "system.data.odbc.8.0.0.nupkg.sha512"}, "System.Data.OleDb/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LQ8PjTIF1LtrrlGiyiTVjAkQtTWKm9GSNnygIlWjhN9y88s7xhy6DUNDDkmQQ9f6ex7mA4k0Tl97lz/CklaiLg==", "path": "system.data.oledb/6.0.0", "hashPath": "system.data.oledb.6.0.0.nupkg.sha512"}, "System.Data.SQLite.Core/*********": {"type": "package", "serviceable": true, "sha512": "sha512-vADIqqgpxaC5xR6qOV8/KMZkQeSDCfmmWpVOtQx0oEr3Yjq2XdTxX7+jfE4+oO2xPovAbYiz6Q5cLRbSsDkq6Q==", "path": "system.data.sqlite.core/*********", "hashPath": "system.data.sqlite.core.*********.nupkg.sha512"}, "System.Diagnostics.EventLog/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-ABk8eOWyQ9Pgmz63EE9hmj0G0eLnWItwM9NV0OGMUOEdseVN1PeA1ryQUIQr5C1odDG2ip4oBTLGSrb5+Z2S3w==", "path": "system.diagnostics.eventlog/10.0.0-preview.3.25171.5", "hashPath": "system.diagnostics.eventlog.10.0.0-preview.3.25171.5.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lX6DXxtJqVGWw7N/QmVoiCyVQ+Q/Xp+jVXPr3gLK1jJExSn1qmAjJQeb8gnOYeeBTG3E3PmG1nu92eYj/TEjpg==", "path": "system.diagnostics.performancecounter/8.0.0", "hashPath": "system.diagnostics.performancecounter.8.0.0.nupkg.sha512"}, "System.DirectoryServices.Protocols/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-puwJxURHDrYLGTQdsHyeMS72ClTqYa4lDYz6LHSbkZEk5hq8H8JfsO4MyYhB5BMMxg93jsQzLUwrnCumj11UIg==", "path": "system.directoryservices.protocols/8.0.0", "hashPath": "system.directoryservices.protocols.8.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "path": "system.identitymodel.tokens.jwt/8.0.1", "hashPath": "system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-8tluJF8w9si+2yoHeL8rgVJS6lKvWomTDC8px65Z8MCzzdME5eaPtEQf4OfVGrAxB5fW93ncucy1+221O9EQaw==", "path": "system.security.cryptography.pkcs/9.0.0", "hashPath": "system.security.cryptography.pkcs.9.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>J<PERSON>+x/F6fmRQ7N6K8paasTw9PDZp4t7G76UjGNlSDgoHPF0h08vTzLYbLZpOLEJSg35d5wy2jCXGo84EN05DpQ==", "path": "system.security.cryptography.protecteddata/9.0.0", "hashPath": "system.security.cryptography.protecteddata.9.0.0.nupkg.sha512"}, "System.Security.Permissions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-v/BBylw7XevuAsHXoX9dDUUfmBIcUf7Lkz8K3ZXIKz3YRKpw8YftpSir4n4e/jDTKFoaK37AsC3xnk+GNFI1Ow==", "path": "system.security.permissions/8.0.0", "hashPath": "system.security.permissions.8.0.0.nupkg.sha512"}, "System.Windows.Extensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Obg3a90MkOw9mYKxrardLpY2u0axDMrSmy4JCdq2cYbelM2cUwmUir5Bomvd1yxmPL9h5LVHU1tuKBZpUjfASg==", "path": "system.windows.extensions/8.0.0", "hashPath": "system.windows.extensions.8.0.0.nupkg.sha512"}, "WorkQueue/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Gs01Z2+uwtU23WJN67uMdo1819YcFdT1LGeRnONXb0BV79lxbMwEdsieTQbTFpGDvRSXDB7SBMDO68zTnB9mMQ==", "path": "workqueue/1.3.0", "hashPath": "workqueue.1.3.0.nupkg.sha512"}, "ZstdSharp.Port/0.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z62eNBIu8E8YtbqlMy57tK3dV1+m2b9NhPeaYovB5exmLKvrGCqOhJTzrEUH5VyUWU6vwX3c1XHJGhW5HVs8dA==", "path": "zstdsharp.port/0.8.0", "hashPath": "zstdsharp.port.0.8.0.nupkg.sha512"}, "SSIC.Entity/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "SSIC.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}