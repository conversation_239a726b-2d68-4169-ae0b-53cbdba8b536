﻿using Serilog;
using SSIC.Infrastructure.BaseProvider;
using SSIC.Utilities;

namespace SSIC.DevTools.BuilderTools
{
    public partial class CreateProject 
    {
   

        public string createfile()
        {
            var ServerHost = Directory.GetCurrentDirectory();
            //设置实体库地址
            var HostPath = ServerHost.Substring(0, ServerHost.LastIndexOf("\\"));
            DirectoryInfo theFolder = new DirectoryInfo($"{HostPath}\\SSFB.Entity\\");
            //设置业务库路径
            var pathhost = $"{HostPath}\\SSFB.Business\\";
            //设置接口库路径
            var ControllerHost = $"{HostPath}\\SSFB.WebApi\\Controllers\\";
            DirectoryInfo[] dirInfo = theFolder.GetDirectories();//获取所在目录的文件夹
            //生成业务层
            var BusinessFiles = new List<string> {"{T}Service", "I{T}Service", "{T}Controller" };
            //读取模板路径
            var TemplatepHost = $"{HostPath}\\SSIC.Infrastructure\\Template\\";

            foreach (DirectoryInfo NextFolder in dirInfo.Where(n => n.Name.ToLower() != "entitybase" && n.Name.ToLower() != "tenantentity" && n.Name.ToLower() != "bin" && n.Name.ToLower() != "obj").ToList())
            {
                DirectoryInfo Fileitem = new DirectoryInfo(NextFolder.FullName);
                FileInfo[] filelist = Fileitem.GetFiles();//获取所在目录的文件
                var CreateFileList = new List<PathDic>();
                foreach (var file in filelist)
                {
                    if (file.Name.Contains(".cs"))
                    {
                        var FileName = file.Name.Replace(".cs", "");
                        foreach (var item in BusinessFiles)
                        {
                            var FileView = new PathDic
                            {
                                path = item == "{T}Controller" ? ControllerHost + NextFolder.Name + "\\" : pathhost + NextFolder.Name + "\\" + item.Replace("{T}", "") + "\\",
                                Filename = item.Replace("{T}", FileName) + ".cs",
                                Templatepath = TemplatepHost + item.Replace("{T}", "") + "s\\Base" + item.Replace("{T}", "") + ".html",
                                BusinessName = NextFolder.Name,
                                IsRepalce = true,
                                EntityName = FileName
                            };
                            var FileViewExpands = new PathDic
                            {
                                path = item == "{T}Controller" ? ControllerHost + NextFolder.Name + "\\Expands\\" : pathhost + NextFolder.Name + "\\" + item.Replace("{T}", "") + "\\Expands\\",
                                Filename = item.Replace("{T}", FileName) + ".cs",
                                Templatepath = TemplatepHost + item.Replace("{T}", "") + "s\\Base" + item.Replace("{T}", "") + "Expands.html",
                                BusinessName = NextFolder.Name,
                                IsRepalce = false,
                                EntityName = FileName
                            };
                            CreateFileList.Add(FileView);
                            CreateFileList.Add(FileViewExpands);
                        }
                    }
                }
                foreach (var item in CreateFileList)
                {
                    LoadPathFile(item.path, item.Templatepath, item.BusinessName, item.EntityName, item.Filename, item.IsRepalce);
                }
                Log.Information($"生成{NextFolder.Name}模板成功!");
            }

            return "";
        }

        /// <summary>
        ///读取Html文件生成文件
        /// </summary>
        /// <param name="path">文件地址</param>
        /// <param name="Templatepath">模板路径</param>
        /// <param name="BusinessName">业务模块</param>
        /// <param name="Filename">文件名</param>
        /// <param name="IsRepalce">是否替换</param>
        private void LoadPathFile(string path, string Templatepath, string BusinessName, string EntityName, string Filename, bool IsRepalce = false)
        {
            if (IsRepalce)
            {
                var domainContent = FileHelper.ReadFile(Templatepath)
                    .Replace("{BusinessName}", BusinessName)
                    .Replace("{EntityName}", EntityName);
                FileHelper.WriteFile(path, Filename, domainContent);
            }
            else
            {
                if (!File.Exists(path + Filename))
                {
                    var domainContent = FileHelper.ReadFile(Templatepath)
                        .Replace("{BusinessName}", BusinessName)
                        .Replace("{EntityName}", EntityName);
                    FileHelper.WriteFile(path, Filename, domainContent);
                }
            }
        }
    }

    public class PathDic
    {
        public string path { get; set; }
        public string Templatepath { get; set; }
        public string BusinessName { get; set; }
        public string Filename { get; set; }
        public bool IsRepalce { get; set; }
        public string EntityName { get; set; }
    }
}
