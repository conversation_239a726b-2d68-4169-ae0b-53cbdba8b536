﻿/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果要增加方法请在当前目录下Expands文件夹{EntityName}Controller编写
 */
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SSFB.Core.BaseController;
using SSFB.Core.BaseProvider;
using SSFB.Entity.{BusinessName};
using SSFB.Business.{BusinessName}.IService;
namespace SSFB.WebApi.Controllers.{BusinessName}
{
    [Route("api/{EntityName}")]
    [ApiController]
    public partial class {EntityName}Controller : BaseController<{EntityName}, I{EntityName}Service>
    {

    }
    }