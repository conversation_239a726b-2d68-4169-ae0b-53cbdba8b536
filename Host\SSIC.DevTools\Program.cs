﻿// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the MIT license.

using Dapr;
using Dapr.Client;
using Google.Api;
using Microsoft.AspNetCore.Authorization;
using Scalar.AspNetCore;
using SSIC.Infrastructure.Authentication;
using SSIC.Infrastructure.Orm;
using StackExchange.Profiling.Storage;

var builder = WebApplication.CreateBuilder(args);

builder.AddServiceDefaults();

// Add services to the container.
builder.Services.AddDaprClient();
builder.Services.AddControllers();

// 添加MiniProfiler服务
builder.Services.AddMiniProfiler(options =>
{
    options.RouteBasePath = "/profiler"; // 设置MiniProfiler的路径
    (options.Storage as MemoryCacheStorage).CacheDuration = TimeSpan.FromMinutes(10);
}); // 如果你使用的是EF Core
builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

builder.Services.AddSingleton<IAuthorizationHandler, PermissionHandler>();
builder.Services.AddSingleton<IAuthorizationPolicyProvider, PermissionPolicyProvider>();
builder.Services.AddHttpContextAccessor();

//var corsOption = builder.Services.GetOptions<CorsOptions>();
//services.AddCors(c =>
//{
//    c.AddPolicy(corsOption.PolicyName, policy =>
//    {
//        policy.AllowAnyHeader()
//              .AllowAnyMethod();

//        if (corsOption.IsUse == true)
//        {
//            policy.WithOrigins(corsOption.WithOrigins).AllowCredentials();

//        }
//        else
//        {
//            policy.AllowAnyOrigin();

//        }
//    });
//});
var app = builder.Build();
// 配置顺序很重要
app.UseDeveloperExceptionPage();

//// 使用Swagger中间件
//app.UseSwagger();

//app.UseSwaggerUI(c =>
//{
//    c.IndexStream = () => GetType().GetTypeInfo().Assembly.GetManifestResourceStream("SSIC.Infrastructure.Swagger.UI.index-mini-profiler.html");
//    c.SwaggerEndpoint("/swagger/v1/swagger.json", "SSIC.WebApi v1");
//});

//app.UseAuthentication();
//app.UseContextMiddleware(); // 引入自定义的 HttpContextMiddleware 中间件
                            // app.UseHttpsRedirection(); // 取消 https 验证
app.UseRouting();
// Configure the HTTP request pipeline.
//app.UseCloudEvents();
//app.MapSubscribeHandler();
// 使用MiniProfiler中间件
app.UseMiniProfiler();
app.UseEndpoints(endpoints =>
{
    endpoints.MapControllers();
    endpoints.MapSubscribeHandler();
});
app.MapScalarApiReference(opt =>
{
    opt.Title = "Scalar Example2";
    opt.Theme = ScalarTheme.Kepler;
    opt.Servers = [];
    opt.DefaultHttpClient = new(ScalarTarget.Http, ScalarClient.Http11);
});


app.MapOpenApi();//映射OpenApi文档路径
app.MapDefaultEndpoints();

app.Run();

internal sealed record WeatherForecastMessage(string Message);

internal sealed record CachedWeatherForecast(WeatherForecast[] Forecasts, DateTimeOffset CachedAt);

internal sealed record WeatherForecast(DateOnly Date, int TemperatureC, string? Summary)
{
    public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);
}
