﻿using SSIC.Infrastructure.ConfigurableOptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.OptionsEntity
{
    /// <summary>
    /// 动态服务配置  
    /// </summary>
    public class ServerOptions : IConfigurableOptions
    {
        /// <summary>
        /// Aspire资源名称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string slnpath { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string slnname { get; set; }

        /// <summary>
        /// 项目类型
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// port端口
        /// </summary>
        public int port { get; set; }
        /// <summary>
        /// gRPC端口
        /// </summary>
        public int grpcprot { get; set; }

    }
}
