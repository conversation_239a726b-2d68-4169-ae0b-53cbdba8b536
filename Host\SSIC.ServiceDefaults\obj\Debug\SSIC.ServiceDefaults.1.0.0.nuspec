﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>SSIC.ServiceDefaults</id>
    <version>1.0.0</version>
    <authors>SSIC.ServiceDefaults</authors>
    <description>Package Description</description>
    <repository type="git" />
    <dependencies>
      <group targetFramework="net10.0">
        <dependency id="Microsoft.Extensions.Http.Resilience" version="9.4.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.ServiceDiscovery" version="9.1.0" exclude="Build,Analyzers" />
        <dependency id="OpenTelemetry.Exporter.OpenTelemetryProtocol" version="1.11.2" exclude="Build,Analyzers" />
        <dependency id="OpenTelemetry.Exporter.Zipkin" version="1.11.2" exclude="Build,Analyzers" />
        <dependency id="OpenTelemetry.Extensions.Hosting" version="1.11.2" exclude="Build,Analyzers" />
        <dependency id="OpenTelemetry.Instrumentation.AspNetCore" version="1.11.1" exclude="Build,Analyzers" />
        <dependency id="OpenTelemetry.Instrumentation.Http" version="1.11.1" exclude="Build,Analyzers" />
        <dependency id="OpenTelemetry.Instrumentation.Runtime" version="1.11.1" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkReferences>
      <group targetFramework="net10.0">
        <frameworkReference name="Microsoft.AspNetCore.App" />
      </group>
    </frameworkReferences>
  </metadata>
  <files>
    <file src="D:\Saisi\SSIC_Core\SSIC.ServiceDefaults\bin\Debug\net10.0\SSIC.ServiceDefaults.dll" target="lib\net10.0\SSIC.ServiceDefaults.dll" />
  </files>
</package>