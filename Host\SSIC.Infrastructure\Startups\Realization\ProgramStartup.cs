﻿using Google.Api;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using OpenTelemetry;
using OpenTelemetry.Trace;
using Serilog;
using Serilog.Sinks.OpenTelemetry;
using SSIC.Infrastructure.ConfigurableOptions.Realization;
using SSIC.Infrastructure.Dapr;
using SSIC.Infrastructure.Logging;
using SSIC.Infrastructure.OptionsEntity;
using SSIC.Infrastructure.Startups.HotReload;
using System;
using System.Linq;
using System.Reflection;

namespace SSIC.Infrastructure.Startups
{
    public static class ProgramStartup
    {
        private static readonly Lazy<IOrderedEnumerable<Type>> startupTypes = new Lazy<IOrderedEnumerable<Type>>(() =>
        {
            return Assembly.GetExecutingAssembly()
                .GetTypes()
                .Where(t => t.GetCustomAttributes<StartupAttribute>().Any())
                .OrderBy(t => t.GetCustomAttribute<StartupAttribute>()?.Sort);
        });

        /// <summary>
        /// 添加应用程序启动配置
        /// </summary>
        /// <param name="builder">Web应用程序构建器</param>
        /// <param name="UseServiceDefault">是否使用服务默认配置</param>
        /// <returns>构建的Web应用程序</returns>
        public static WebApplication AddStartup(this WebApplicationBuilder builder, bool UseServiceDefault = true)
        {
            IConfiguration _Configuration = builder.Configuration;

            // 这里可以添加其他服务
            builder.Services.AddAllConfigurableOptions(ref _Configuration);

            // 🚀 提前接管 Host 日志
            builder.Host.UseSerilogLogging(_Configuration);

            // 🚀 配置 Services 日志
            builder.Services.AddSerilogLogging(_Configuration);
      
            if (UseServiceDefault)
            {
                //builder.Services.AddOpenTelemetry(builder.Configuration);
                // 👇 注册 OpenTelemetry
                builder.AddServiceDefaults();
            }
         

            // 创建临时的服务提供程序来调用 ConfigureServices
            using (var tempProvider = builder.Services.BuildServiceProvider())
            {
                var serviceProvider = tempProvider;

                foreach (var type in startupTypes.Value)
                {
                    var startupInstance = ActivatorUtilities.CreateInstance(serviceProvider, type) as IStartups;
                    startupInstance?.ConfigureServices(builder.Services);
                }
            }
           
            var app = builder.Build();
            app.UseHotReload();

            using (var scope = app.Services.CreateScope())
            {
                var serviceProvider = scope.ServiceProvider;

                foreach (var type in startupTypes.Value)
                {
                    var startupInstance = ActivatorUtilities.CreateInstance(serviceProvider, type) as IStartups;
                    startupInstance?.Configure(app);
                }
            }
            
            if (UseServiceDefault)
            {
                app.MapDefaultEndpoints();
            }

            // 返回模块应用构建器
            return app;
        }
    }
}
