using SSIC.Infrastructure.Startups;
using Aspire.Seq;
var builder = WebApplication.CreateBuilder(args);
//builder.AddSeqEndpoint("seq", static settings =>
//{
//    settings.DisableHealthChecks = true;
//    settings.ServerUrl = "http://localhost:5341";
//});
// Add service defaults & Aspire components.
var app = builder.AddStartup();
//var app = builder.Build();
//app.AddConfigure(); // Ensure this is called before app.Run()

app.Run();
