@SSIC.Modules.Sys_HostAddress = http://localhost:5129
@token =

### ========== 天气预报接口测试 ==========

### 获取默认天气预报
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/weatherforecast/hello
Accept: application/json

### 获取指定天数的天气预报 - 成功案例
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/weatherforecast/forecast/7
Accept: application/json

### 获取指定天数的天气预报 - 失败案例（超出范围）
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/weatherforecast/forecast/50
Accept: application/json

### 获取指定城市的天气
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/weatherforecast/city/北京
Accept: application/json

### 获取天气统计信息
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/weatherforecast/statistics
Accept: application/json

### ========== 系统配置接口测试 ==========

### 获取所有系统配置
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemconfig
Authorization: Bearer {{token}}
Accept: application/json

### 获取指定分类的配置
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemconfig?category=基础设置
Authorization: Bearer {{token}}
Accept: application/json

### 根据键获取配置
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemconfig/system.name
Authorization: Bearer {{token}}
Accept: application/json

### 创建新配置
POST {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemconfig
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "key": "system.timeout",
  "value": "30",
  "description": "系统超时时间（秒）",
  "category": "性能设置"
}

### 更新配置
PUT {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemconfig/system.name
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "value": "SSIC管理系统 v2.0",
  "description": "系统名称（已更新）"
}

### 获取配置分类
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemconfig/categories
Authorization: Bearer {{token}}
Accept: application/json

### 批量更新配置
PUT {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemconfig/batch
Authorization: Bearer {{token}}
Content-Type: application/json

[
  {
    "key": "system.name",
    "value": "SSIC管理系统 v3.0"
  },
  {
    "key": "system.version",
    "value": "3.0.0"
  }
]

### 删除配置
DELETE {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemconfig/system.timeout
Authorization: Bearer {{token}}

### ========== 系统日志接口测试 ==========

### 获取系统日志（分页）
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemlog?page=1&pageSize=5
Authorization: Bearer {{token}}
Accept: application/json

### 获取指定级别的日志
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemlog?level=Error&page=1&pageSize=10
Authorization: Bearer {{token}}
Accept: application/json

### 获取指定来源的日志
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemlog?source=Auth&page=1&pageSize=10
Authorization: Bearer {{token}}
Accept: application/json

### 根据ID获取日志详情
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemlog/1
Authorization: Bearer {{token}}
Accept: application/json

### 创建日志记录
POST {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemlog
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "level": "Info",
  "message": "手动创建的测试日志",
  "source": "Manual",
  "details": "这是一个通过API创建的测试日志记录"
}

### 获取日志统计信息
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemlog/statistics
Authorization: Bearer {{token}}
Accept: application/json

### 导出日志（JSON格式）
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemlog/export?format=json&level=Error
Authorization: Bearer {{token}}
Accept: application/json

### 导出日志（CSV格式）
GET {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemlog/export?format=csv
Authorization: Bearer {{token}}
Accept: text/csv

### 清理日志
DELETE {{SSIC.Modules.Sys_HostAddress}}/api/sys/systemlog/cleanup?beforeDate=2024-01-01
Authorization: Bearer {{token}}

###
# 测试说明：
# 1. 所有接口都会自动路由到 /api/sys/ 前缀下
# 2. 系统配置和日志接口需要认证，请先在Auth模块登录获取token
# 3. 天气预报接口不需要认证，可以直接访问
# 4. 可以使用不同的参数测试各种场景
###
