# SSIC 模块化API接口测试说明

## 概述

本文档说明了为SSIC项目的Auth和Sys模块创建的API接口及其测试方法。所有接口都会自动路由到 `/api/{模块名}/` 前缀下。

## 路由规则

- Auth模块：`/api/auth/`
- Sys模块：`/api/sys/`

## Auth模块接口

### 1. 用户认证控制器 (AuthController)

#### 接口列表：
- `POST /api/auth/auth/login` - 用户登录
- `POST /api/auth/auth/register` - 用户注册  
- `GET /api/auth/auth/profile` - 获取用户信息（需认证）
- `POST /api/auth/auth/logout` - 用户登出（需认证）
- `POST /api/auth/auth/change-password` - 修改密码（需认证）

#### 测试账号：
- 用户名：`admin`
- 密码：`123456`

### 2. 角色管理控制器 (RoleController)

#### 接口列表：
- `GET /api/auth/role` - 获取所有角色（需认证）
- `GET /api/auth/role/{id}` - 根据ID获取角色（需认证）
- `POST /api/auth/role` - 创建新角色（需认证）
- `PUT /api/auth/role/{id}` - 更新角色（需认证）
- `DELETE /api/auth/role/{id}` - 删除角色（需认证）
- `GET /api/auth/role/{id}/permissions` - 获取角色权限（需认证）

## Sys模块接口

### 1. 天气预报控制器 (WeatherForecastController)

#### 接口列表：
- `GET /api/sys/weatherforecast/hello` - 获取默认天气预报
- `GET /api/sys/weatherforecast/forecast/{days}` - 获取指定天数的天气预报
- `GET /api/sys/weatherforecast/city/{city}` - 获取指定城市的天气
- `GET /api/sys/weatherforecast/statistics` - 获取天气统计信息

### 2. 系统配置控制器 (SystemConfigController)

#### 接口列表：
- `GET /api/sys/systemconfig` - 获取所有系统配置（需认证）
- `GET /api/sys/systemconfig/{key}` - 根据键获取配置（需认证）
- `POST /api/sys/systemconfig` - 创建新配置（需认证）
- `PUT /api/sys/systemconfig/{key}` - 更新配置（需认证）
- `DELETE /api/sys/systemconfig/{key}` - 删除配置（需认证）
- `GET /api/sys/systemconfig/categories` - 获取配置分类（需认证）
- `PUT /api/sys/systemconfig/batch` - 批量更新配置（需认证）

### 3. 系统日志控制器 (SystemLogController)

#### 接口列表：
- `GET /api/sys/systemlog` - 获取系统日志（分页，需认证）
- `GET /api/sys/systemlog/{id}` - 根据ID获取日志详情（需认证）
- `POST /api/sys/systemlog` - 创建日志记录（需认证）
- `GET /api/sys/systemlog/statistics` - 获取日志统计信息（需认证）
- `GET /api/sys/systemlog/export` - 导出日志（需认证）
- `DELETE /api/sys/systemlog/cleanup` - 清理日志（需认证）

## 测试文件

### Auth模块测试文件
- 文件位置：`SSIC.Modules/SSIC.Modules.Auth/SSIC.Modules.Auth.http`
- 包含所有Auth模块接口的测试用例

### Sys模块测试文件
- 文件位置：`SSIC.Modules/SSIC.Modules.Sys/SSIC.Modules.Sys.http`
- 包含所有Sys模块接口的测试用例

## 使用说明

1. **启动服务**：确保SSIC.HostServer正在运行
2. **获取Token**：先调用Auth模块的登录接口获取认证Token
3. **测试接口**：使用获取的Token测试需要认证的接口
4. **查看结果**：所有接口都返回JSON格式的响应

## 注意事项

1. 所有需要认证的接口都需要在请求头中添加：`Authorization: Bearer {token}`
2. 系统会自动为模块添加路由前缀，无需手动配置
3. 模块DLL文件会自动输出到`SSIC.HostServer/Modules/`目录
4. 测试数据都是模拟数据，用于演示接口功能

## 构建状态

- ✅ Auth模块构建成功
- ✅ Sys模块构建成功
- ✅ 路由配置正常
- ✅ 接口测试文件已创建

## 下一步

建议运行SSIC.HostServer并使用HTTP测试文件验证所有接口是否正常工作。
