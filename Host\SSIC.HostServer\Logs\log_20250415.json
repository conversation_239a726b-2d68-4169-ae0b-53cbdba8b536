{"@t":"2025-04-15T01:24:16.5713069Z","@mt":"加载模块失败: {Path}, 错误: {Message}","@l":"Error","@x":"System.InvalidOperationException: The service collection cannot be modified because it is read-only.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceCollection.ThrowReadOnlyException()\r\n   at Microsoft.Extensions.DependencyInjection.ServiceCollection.CheckReadOnly()\r\n   at Microsoft.Extensions.DependencyInjection.ServiceCollection.System.Collections.Generic.ICollection<Microsoft.Extensions.DependencyInjection.ServiceDescriptor>.Add(ServiceDescriptor item)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddSingleton(IServiceCollection services, Type serviceType, Object implementationInstance)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddSingleton[TService](IServiceCollection services, TService implementationInstance)\r\n   at Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure[TOptions](IServiceCollection services, String name, Action`1 configureOptions)\r\n   at Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure[TOptions](IServiceCollection services, Action`1 configureOptions)\r\n   at SSIC.Infrastructure.Startups.ServiceCollectionExtensions.AddModuleServices(IServiceCollection services, Assembly moduleAssembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\Extensions\\ServiceCollectionExtensions.cs:line 40\r\n   at SSIC.Infrastructure.HotReload.DynamicModuleLoader.LoadModule(String path) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\HotReload\\DynamicModuleLoader.cs:line 131","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","Message":"The service collection cannot be modified because it is read-only.","SourceContext":"SSIC.Infrastructure.HotReload.DynamicModuleLoader","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:24:18.2342742Z","@mt":"加载模块失败: {Path}, 错误: {Message}","@l":"Error","@x":"System.InvalidOperationException: The service collection cannot be modified because it is read-only.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceCollection.ThrowReadOnlyException()\r\n   at Microsoft.Extensions.DependencyInjection.ServiceCollection.CheckReadOnly()\r\n   at Microsoft.Extensions.DependencyInjection.ServiceCollection.System.Collections.Generic.ICollection<Microsoft.Extensions.DependencyInjection.ServiceDescriptor>.Add(ServiceDescriptor item)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddSingleton(IServiceCollection services, Type serviceType, Object implementationInstance)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddSingleton[TService](IServiceCollection services, TService implementationInstance)\r\n   at Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure[TOptions](IServiceCollection services, String name, Action`1 configureOptions)\r\n   at Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure[TOptions](IServiceCollection services, Action`1 configureOptions)\r\n   at SSIC.Infrastructure.Startups.ServiceCollectionExtensions.AddModuleServices(IServiceCollection services, Assembly moduleAssembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Startups\\Extensions\\ServiceCollectionExtensions.cs:line 40\r\n   at SSIC.Infrastructure.HotReload.DynamicModuleLoader.LoadModule(String path) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\HotReload\\DynamicModuleLoader.cs:line 131","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","Message":"The service collection cannot be modified because it is read-only.","SourceContext":"SSIC.Infrastructure.HotReload.DynamicModuleLoader","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:24:18.6473855Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:24:18.6519252Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:24:18.7101778Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:24:18.7124413Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:24:18.7150130Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:30:36.3912261Z","@mt":"模块加载失败: {Path}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.HotReload.DynamicModuleLoader.RegisterModuleServices(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\HotReload\\DynamicModuleLoader.cs:line 152\r\n   at SSIC.Infrastructure.HotReload.DynamicModuleLoader.LoadModule(String path) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\HotReload\\DynamicModuleLoader.cs:line 103","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.HotReload.DynamicModuleLoader","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:30:36.4888215Z","@mt":"模块加载失败: {Path}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.HotReload.DynamicModuleLoader.RegisterModuleServices(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\HotReload\\DynamicModuleLoader.cs:line 152\r\n   at SSIC.Infrastructure.HotReload.DynamicModuleLoader.LoadModule(String path) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\HotReload\\DynamicModuleLoader.cs:line 103","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.HotReload.DynamicModuleLoader","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:30:36.8950872Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:30:36.8997447Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:30:36.9434309Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:30:36.9467659Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:30:36.9495863Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:31:52.7769777Z","@mt":"模块加载失败: {Path}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.HotReload.DynamicModuleLoader.RegisterModuleServices(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\HotReload\\DynamicModuleLoader.cs:line 152\r\n   at SSIC.Infrastructure.HotReload.DynamicModuleLoader.LoadModule(String path) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\HotReload\\DynamicModuleLoader.cs:line 103","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Auth\\SSIC.Modules.Auth.dll","SourceContext":"SSIC.Infrastructure.HotReload.DynamicModuleLoader","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:31:52.8555080Z","@mt":"模块加载失败: {Path}","@l":"Error","@x":"System.InvalidOperationException: No service for type 'Microsoft.Extensions.DependencyInjection.IServiceCollection' has been registered.\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)\r\n   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)\r\n   at SSIC.Infrastructure.HotReload.DynamicModuleLoader.RegisterModuleServices(Assembly assembly) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\HotReload\\DynamicModuleLoader.cs:line 152\r\n   at SSIC.Infrastructure.HotReload.DynamicModuleLoader.LoadModule(String path) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\HotReload\\DynamicModuleLoader.cs:line 103","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\Modules\\SSIC.Modules.Sys\\SSIC.Modules.Sys.dll","SourceContext":"SSIC.Infrastructure.HotReload.DynamicModuleLoader","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:31:53.1914442Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:31:53.1956546Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:31:53.2451613Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:31:53.2476433Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T01:31:53.2504080Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:00:42.6543257Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:00:42.7035947Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:00:42.8035264Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:00:42.8073038Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:00:42.8102768Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:02:05.3898027Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:02:05.4344162Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:02:05.5283089Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:02:05.5315250Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:02:05.5343899Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:06:29.8532303Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:06:29.9019908Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:06:29.9636571Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:06:29.9666422Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:06:29.9690136Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:09:45.0251714Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:09:45.0878193Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:09:45.1692075Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:09:45.1717377Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:09:45.1741500Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:10:21.8168057Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:10:21.8754051Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:10:22.0017004Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:10:22.0047339Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T09:10:22.0081661Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:01:16.4829105Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:01:16.5355180Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:01:16.6561363Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:01:16.6587703Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:01:16.6621343Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:14:39.8729587Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:14:39.9237862Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:14:40.0202792Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:14:40.0229993Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:14:40.0255559Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:15:26.7796012Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Dapr.Client.InvocationException: An exception occurred while invoking method: 'WeatherForecast/Get123/123' on app-id: 'ssic-devtools'\r\n ---> System.Net.Http.HttpRequestException: 由于目标计算机积极拒绝，无法连接。 (localhost:3500)\r\n ---> System.Net.Sockets.SocketException (10061): 由于目标计算机积极拒绝，无法连接。\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)\r\n   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)\r\n   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|289_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)\r\n   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)\r\n   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)\r\n   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)\r\n   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)\r\n   at Dapr.Client.DaprClientGrpc.InvokeMethodWithResponseAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Dapr.Client.DaprClientGrpc.InvokeMethodWithResponseAsync(HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at Dapr.Client.DaprClientGrpc.InvokeMethodAsync[TResponse](HttpRequestMessage request, CancellationToken cancellationToken)\r\n   at SSIC.Infrastructure.Dapr.DaprClientProvider.InvokeServiceWithLoadBalancingAsync[TRequest,TResponse](HttpMethod httpMethod, String appId, String methodName, TRequest data) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Dapr\\DaprClientProvider.cs:line 34\r\n   at SSIC.HostServer.Controllers.WeatherForecast2Controller.Get() in D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\Controllers\\WeatherForecastController.cs:line 46\r\n   at lambda_method5(Closure, Object)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 94\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at SSIC.Infrastructure.Orm.ContextMiddleware.Invoke(HttpContext context) in D:\\Saisi\\SSIC_Core\\SSIC.Infrastructure\\Orm\\ContextMiddleware\\ContextMiddleware.cs:line 30\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"8691610965dd05521e461a6a0f85fc3f","@sp":"c1f99e6ec691d753","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNBS0PALE0K1:00000019","RequestPath":"/WeatherForecast2","ConnectionId":"0HNBS0PALE0K1","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:49:38.5012853Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:49:38.5532962Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:49:38.6837640Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:49:38.6870429Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:49:38.6898531Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:50:47.8175442Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:50:47.8870118Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:50:48.0633672Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:50:48.0681855Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:50:48.0709881Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:53:32.6177015Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:53:32.6702856Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:53:32.7877031Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:53:32.7919614Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:53:32.7952390Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:55:39.2569434Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:55:39.3044302Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:55:39.3934601Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:55:39.3963829Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:55:39.3986367Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:56:17.2098220Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:56:17.2635265Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:56:17.3580409Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:56:17.3618737Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:56:17.3646118Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:57:42.7986700Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:57:42.8684117Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:57:42.9834871Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:57:42.9874306Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T10:57:42.9909544Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T11:01:00.5350249Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T11:01:00.5903779Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T11:01:00.7120266Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T11:01:00.7195621Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T11:01:00.7229937Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T11:04:08.2551503Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T11:04:08.3062960Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T11:04:08.3992195Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T11:04:08.4021302Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-15T11:04:08.4052010Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
