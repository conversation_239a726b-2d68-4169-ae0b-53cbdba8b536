﻿using Microsoft.AspNetCore.Mvc;

namespace SSIC.Modules.Sys.Controllers
{
    public class WeatherForecastController : ControllerBase
    {
        private static readonly string[] Summaries = new[]
        {
            "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "<PERSON><PERSON><PERSON>", "Hot", "Sweltering", "Scorching"
        };

        private readonly ILogger<WeatherForecastController> _logger;

        public WeatherForecastController(ILogger<WeatherForecastController> logger)
        {
            _logger = logger;
        }

        [HttpGet("hello")]
        public IEnumerable<WeatherForecast> Get()
        {
            return Enumerable.Range(1, 5).Select(index => new WeatherForecast
            {
                Date = DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
                TemperatureC = Random.Shared.Next(-20, 55),
                Summary = Summaries[Random.Shared.Next(Summaries.Length)]
            })
            .ToArray();
        }

        /// <summary>
        /// 获取指定天数的天气预报
        /// </summary>
        /// <param name="days">天数</param>
        /// <returns>天气预报列表</returns>
        [HttpGet("forecast/{days:int}")]
        public IActionResult GetForecast(int days)
        {
            if (days <= 0 || days > 30)
            {
                return BadRequest(new { message = "天数必须在1-30之间" });
            }

            var forecasts = Enumerable.Range(1, days).Select(index => new WeatherForecast
            {
                Date = DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
                TemperatureC = Random.Shared.Next(-20, 55),
                Summary = Summaries[Random.Shared.Next(Summaries.Length)]
            }).ToArray();

            return Ok(new { success = true, data = forecasts, days = days });
        }

        /// <summary>
        /// 获取指定城市的天气
        /// </summary>
        /// <param name="city">城市名称</param>
        /// <returns>天气信息</returns>
        [HttpGet("city/{city}")]
        public IActionResult GetWeatherByCity(string city)
        {
            _logger.LogInformation("获取城市天气: {City}", city);

            var weather = new WeatherForecast
            {
                Date = DateOnly.FromDateTime(DateTime.Now),
                TemperatureC = Random.Shared.Next(-20, 55),
                Summary = Summaries[Random.Shared.Next(Summaries.Length)]
            };

            return Ok(new {
                success = true,
                data = weather,
                city = city,
                message = $"{city}的当前天气"
            });
        }

        /// <summary>
        /// 获取天气统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        [HttpGet("statistics")]
        public IActionResult GetStatistics()
        {
            var stats = new
            {
                totalCities = 100,
                averageTemperature = 22.5,
                maxTemperature = 45,
                minTemperature = -15,
                mostCommonSummary = "Mild",
                lastUpdated = DateTime.Now
            };

            return Ok(new { success = true, data = stats });
        }
    }
}
