using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace SSIC.Modules.Sys.Controllers
{
    /// <summary>
    /// 系统日志控制器
    /// </summary>
    [Authorize]
    public class SystemLogController : ControllerBase
    {
        private readonly ILogger<SystemLogController> _logger;

        // 模拟日志数据
        private static readonly List<SystemLog> _logs = new()
        {
            new SystemLog { Id = 1, Level = "Info", Message = "系统启动", Source = "System", CreatedAt = DateTime.Now.AddHours(-2), UserId = "admin" },
            new SystemLog { Id = 2, Level = "Warning", Message = "内存使用率较高", Source = "Monitor", CreatedAt = DateTime.Now.AddHours(-1), UserId = "system" },
            new SystemLog { Id = 3, Level = "Error", Message = "数据库连接失败", Source = "Database", CreatedAt = DateTime.Now.AddMinutes(-30), UserId = "system" },
            new SystemLog { Id = 4, Level = "Info", Message = "用户登录", Source = "Auth", CreatedAt = DateTime.Now.AddMinutes(-15), UserId = "user1" },
            new SystemLog { Id = 5, Level = "Debug", Message = "API调用", Source = "API", CreatedAt = DateTime.Now.AddMinutes(-5), UserId = "user2" }
        };

        public SystemLogController(ILogger<SystemLogController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 获取系统日志（分页）
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="level">日志级别</param>
        /// <param name="source">日志来源</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>日志列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetLogs(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? level = null,
            [FromQuery] string? source = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            _logger.LogInformation("获取系统日志，页码: {Page}, 页大小: {PageSize}", page, pageSize);
            
            var query = _logs.AsEnumerable();

            // 过滤条件
            if (!string.IsNullOrEmpty(level))
            {
                query = query.Where(l => l.Level.Equals(level, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrEmpty(source))
            {
                query = query.Where(l => l.Source.Contains(source, StringComparison.OrdinalIgnoreCase));
            }

            if (startDate.HasValue)
            {
                query = query.Where(l => l.CreatedAt >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(l => l.CreatedAt <= endDate.Value);
            }

            // 排序
            query = query.OrderByDescending(l => l.CreatedAt);

            var total = query.Count();
            var logs = query.Skip((page - 1) * pageSize).Take(pageSize).ToList();

            return Ok(new { 
                success = true, 
                data = logs, 
                total = total,
                page = page,
                pageSize = pageSize,
                totalPages = (int)Math.Ceiling((double)total / pageSize)
            });
        }

        /// <summary>
        /// 根据ID获取日志详情
        /// </summary>
        /// <param name="id">日志ID</param>
        /// <returns>日志详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetLog(int id)
        {
            _logger.LogInformation("获取日志详情: {LogId}", id);
            
            var log = _logs.FirstOrDefault(l => l.Id == id);
            if (log == null)
            {
                return NotFound(new { message = "日志不存在" });
            }

            return Ok(new { success = true, data = log });
        }

        /// <summary>
        /// 创建日志记录
        /// </summary>
        /// <param name="request">创建日志请求</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> CreateLog([FromBody] CreateLogRequest request)
        {
            _logger.LogInformation("创建日志记录: {Level} - {Message}", request.Level, request.Message);
            
            if (string.IsNullOrEmpty(request.Message))
            {
                return BadRequest(new { message = "日志消息不能为空" });
            }

            var newLog = new SystemLog
            {
                Id = _logs.Max(l => l.Id) + 1,
                Level = request.Level,
                Message = request.Message,
                Source = request.Source,
                UserId = User.Identity?.Name ?? "anonymous",
                CreatedAt = DateTime.Now,
                Details = request.Details
            };

            _logs.Add(newLog);

            return CreatedAtAction(nameof(GetLog), new { id = newLog.Id }, 
                new { success = true, data = newLog, message = "日志创建成功" });
        }

        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetStatistics()
        {
            _logger.LogInformation("获取日志统计信息");
            
            var stats = new
            {
                total = _logs.Count,
                today = _logs.Count(l => l.CreatedAt.Date == DateTime.Today),
                byLevel = _logs.GroupBy(l => l.Level)
                    .Select(g => new { level = g.Key, count = g.Count() })
                    .ToList(),
                bySource = _logs.GroupBy(l => l.Source)
                    .Select(g => new { source = g.Key, count = g.Count() })
                    .ToList(),
                recent = _logs.OrderByDescending(l => l.CreatedAt).Take(5).ToList()
            };

            return Ok(new { success = true, data = stats });
        }

        /// <summary>
        /// 清理日志
        /// </summary>
        /// <param name="beforeDate">清理此日期之前的日志</param>
        /// <returns>清理结果</returns>
        [HttpDelete("cleanup")]
        public async Task<IActionResult> CleanupLogs([FromQuery] DateTime beforeDate)
        {
            _logger.LogInformation("清理日志，清理日期: {BeforeDate}", beforeDate);
            
            var logsToRemove = _logs.Where(l => l.CreatedAt < beforeDate).ToList();
            var removedCount = logsToRemove.Count;

            foreach (var log in logsToRemove)
            {
                _logs.Remove(log);
            }

            return Ok(new { 
                success = true, 
                message = $"成功清理 {removedCount} 条日志记录",
                removedCount = removedCount
            });
        }

        /// <summary>
        /// 导出日志
        /// </summary>
        /// <param name="format">导出格式（json/csv）</param>
        /// <param name="level">日志级别</param>
        /// <param name="source">日志来源</param>
        /// <returns>导出文件</returns>
        [HttpGet("export")]
        public async Task<IActionResult> ExportLogs(
            [FromQuery] string format = "json",
            [FromQuery] string? level = null,
            [FromQuery] string? source = null)
        {
            _logger.LogInformation("导出日志，格式: {Format}", format);
            
            var query = _logs.AsEnumerable();

            if (!string.IsNullOrEmpty(level))
            {
                query = query.Where(l => l.Level.Equals(level, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrEmpty(source))
            {
                query = query.Where(l => l.Source.Contains(source, StringComparison.OrdinalIgnoreCase));
            }

            var logs = query.OrderByDescending(l => l.CreatedAt).ToList();

            if (format.ToLower() == "csv")
            {
                var csv = "Id,Level,Message,Source,UserId,CreatedAt\n" +
                         string.Join("\n", logs.Select(l => 
                             $"{l.Id},{l.Level},{l.Message},{l.Source},{l.UserId},{l.CreatedAt:yyyy-MM-dd HH:mm:ss}"));
                
                return File(System.Text.Encoding.UTF8.GetBytes(csv), "text/csv", $"logs_{DateTime.Now:yyyyMMdd}.csv");
            }

            return Ok(new { success = true, data = logs, format = "json" });
        }
    }

    #region 模型定义

    /// <summary>
    /// 系统日志模型
    /// </summary>
    public class SystemLog
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        public string Level { get; set; } = string.Empty;

        /// <summary>
        /// 日志消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 日志来源
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 详细信息
        /// </summary>
        public string? Details { get; set; }
    }

    /// <summary>
    /// 创建日志请求模型
    /// </summary>
    public class CreateLogRequest
    {
        /// <summary>
        /// 日志级别
        /// </summary>
        public string Level { get; set; } = "Info";

        /// <summary>
        /// 日志消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 日志来源
        /// </summary>
        public string Source { get; set; } = "Manual";

        /// <summary>
        /// 详细信息
        /// </summary>
        public string? Details { get; set; }
    }

    #endregion
}
