{"@t":"2025-07-10T03:02:36.7586777Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:36.8189835Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:36.8272044Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:36.8739953Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:36.8742499Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:36.9653980Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":4,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:36.9861929Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:36.9863837Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:36.9907351Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:36.9960105Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:36.9986382Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:36.9990753Z","@mt":"开始监控目录: {Path}","Path":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules\\SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:36.9992331Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:36.9993521Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:37.6124415Z","@mt":"Now listening on: {address}","address":"https://localhost:57476","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:37.6142802Z","@mt":"Now listening on: {address}","address":"http://localhost:57479","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:37.7491139Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:37.7494673Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:02:37.7496278Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:03:14.2080430Z","@mt":"首次请求触发端点刷新","@tr":"9082fffb8c61eb92b1010cb23a08fb57","@sp":"2042719f8d1845b5","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNDVBP7QL4VA:00000001","RequestPath":"/dapr/config","ConnectionId":"0HNDVBP7QL4VA","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-10T03:03:14.2121423Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"9082fffb8c61eb92b1010cb23a08fb57","@sp":"2042719f8d1845b5","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNDVBP7QL4VA:00000001","RequestPath":"/dapr/config","ConnectionId":"0HNDVBP7QL4VA","MachineName":"DESKTOP-O211UJ1","ThreadId":14,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
