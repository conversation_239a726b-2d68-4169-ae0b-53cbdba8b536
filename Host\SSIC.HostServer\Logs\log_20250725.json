{"@t":"2025-07-24T16:38:07.2378780Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:07.3165540Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:07.3192903Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:07.3500691Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:07.3542908Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:07.4427473Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:07.4595766Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:07.4617047Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:07.4670406Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:07.7762820Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:07.7809231Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:07.8348279Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:07.8378568Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:07.8937098Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:08.9922138Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:08.9945349Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:08.9964768Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:08.9978922Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:08.9998201Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:09.5110892Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:09.5182686Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:09.5223557Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:09.5243630Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:09.5259855Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":20,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:13.2216132Z","@mt":"首次请求触发端点刷新","@tr":"872b369a6c218d2974e4fa3b0e7988b3","@sp":"9bae39f2c496bef1","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQ1S62U7A:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQ1S62U7A","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:13.2277498Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"872b369a6c218d2974e4fa3b0e7988b3","@sp":"9bae39f2c496bef1","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQ1S62U7A:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQ1S62U7A","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:13.6294510Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"506dd5633784bd6844dca272abe10c77","@sp":"982becd6874ab878","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ1S62U7A:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ1S62U7A","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:13.6363334Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"506dd5633784bd6844dca272abe10c77","@sp":"982becd6874ab878","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ1S62U7A:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ1S62U7A","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:38:13.6498177Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"506dd5633784bd6844dca272abe10c77","@sp":"982becd6874ab878","PathCount":26,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ1S62U7A:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ1S62U7A","MachineName":"DESKTOP-O211UJ1","ThreadId":23,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.1355546Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.1632823Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.1969036Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.2629124Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.2654987Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.3403664Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.4238175Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.4261841Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.4302005Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.6955456Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.7003916Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.7409873Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.7436242Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:40.7455547Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:41.9536635Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:41.9579285Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:41.9608781Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:41.9626188Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:41.9646306Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:42.4759205Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:42.4820971Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:42.4866194Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:42.4887325Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:42.4909646Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:43.6708192Z","@mt":"首次请求触发端点刷新","@tr":"70fc665d4c896893fc49633cdbba34a8","@sp":"c48747bc2f625d12","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQ4DKS26M:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQ4DKS26M","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:43.6796774Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"70fc665d4c896893fc49633cdbba34a8","@sp":"c48747bc2f625d12","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQ4DKS26M:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQ4DKS26M","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:44.1991868Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"335ac46cc93ba153af3b600cdee2de37","@sp":"3c40f8533011ab48","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ4DKS26M:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ4DKS26M","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:44.2074724Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"335ac46cc93ba153af3b600cdee2de37","@sp":"3c40f8533011ab48","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ4DKS26M:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ4DKS26M","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:42:44.2287024Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"335ac46cc93ba153af3b600cdee2de37","@sp":"3c40f8533011ab48","PathCount":26,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ4DKS26M:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ4DKS26M","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:11.4454340Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:11.4211530Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:11.4877922Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:11.5850959Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:11.5880182Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:11.6524630Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:11.6690912Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:11.6802618Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:11.6894576Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:11.9891545Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:11.9942701Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:12.0396859Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:12.0422947Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:12.0452780Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:13.2469647Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:13.2509675Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:13.2532706Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:13.2564350Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:13.2590303Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:13.7635389Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:13.7691770Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:13.7749176Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:13.7775764Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:13.7802759Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:13.9654821Z","@mt":"首次请求触发端点刷新","@tr":"acf4c92193e713a12983aa0ba415fdb4","@sp":"e2dd187ecb45712e","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQ4MSJDG5:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQ4MSJDG5","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:13.9714068Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"acf4c92193e713a12983aa0ba415fdb4","@sp":"e2dd187ecb45712e","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQ4MSJDG5:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQ4MSJDG5","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:14.4407368Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"ca0877cf996063e3ab088b29aa5a174c","@sp":"6c1ab233df41dd64","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ4MSJDG5:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ4MSJDG5","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:14.4443971Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"ca0877cf996063e3ab088b29aa5a174c","@sp":"6c1ab233df41dd64","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ4MSJDG5:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ4MSJDG5","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:43:14.4568168Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"ca0877cf996063e3ab088b29aa5a174c","@sp":"6c1ab233df41dd64","PathCount":26,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ4MSJDG5:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ4MSJDG5","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:21.5334075Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:21.5293194Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:21.5554464Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:21.5667348Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:21.5679599Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:21.5907903Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:21.5943251Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:21.5961198Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:21.5987716Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:21.6979341Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:21.6997426Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:21.7008393Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:21.7018802Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:23.1230043Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:23.1246274Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:23.1259183Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:23.1266702Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:23.1279676Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:23.6287332Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:23.6311678Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:23.6329389Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:23.6337845Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:45:23.6345330Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:18.7443344Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:18.7443345Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:18.7757685Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:18.7886385Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:18.7912677Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:18.8260787Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:18.8301105Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:18.8316641Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:18.8350389Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:18.9445446Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:18.9469767Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:18.9479488Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:18.9490011Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:20.3614413Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:20.3631123Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:20.3642599Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:20.3649314Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:20.3655292Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:20.8695401Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:20.8745896Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:20.8789196Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:20.8805729Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:48:20.8827763Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:50.6749916Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:50.6806467Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:50.7162982Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:50.7271439Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:50.7288587Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:50.7595899Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:50.7653981Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:50.7664484Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:50.7705162Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:50.8939074Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:50.8963386Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:50.8974378Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:50.8985972Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:52.2923906Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:52.2953761Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:52.2968467Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:52.2979017Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:52.2987952Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:52.8030321Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:52.8071048Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:52.8092994Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:52.8101045Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:49:52.8108544Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:04.4469435Z","@mt":"Application is shutting down...","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:04.4560101Z","@mt":"文件监控已停止","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:04.4586476Z","@mt":"已删除临时目录: {Path}","Path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\SSIC_Modules_6168c70559744fb2a647cbe737f1704a","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:17.9682136Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:17.9875939Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:18.0184148Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:18.0689849Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:18.0741904Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:18.1862112Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:18.2022154Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:18.2062958Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:18.2148536Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:18.4773908Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:18.4807289Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:18.5192012Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:18.5213672Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:18.5228131Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:19.7399037Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:19.7438986Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:19.7465974Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:19.7482658Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:19.7498491Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:20.2638104Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:20.2706016Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:20.2762706Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:20.2792188Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:20.2822395Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:20.6929822Z","@mt":"首次请求触发端点刷新","@tr":"17d2fc7d5b066719b9314ce1b3ab7c81","@sp":"f60c08bd0b29b362","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQ8M0FTD8:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:20.7006704Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"17d2fc7d5b066719b9314ce1b3ab7c81","@sp":"f60c08bd0b29b362","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQ8M0FTD8:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:21.2714795Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"a9d1707115ebe68cf7d7bbe22003634a","@sp":"757f0923cb6335e5","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ8M0FTD8:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:21.2757491Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"a9d1707115ebe68cf7d7bbe22003634a","@sp":"757f0923cb6335e5","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ8M0FTD8:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:50:21.2874872Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"a9d1707115ebe68cf7d7bbe22003634a","@sp":"757f0923cb6335e5","PathCount":29,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ8M0FTD8:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:51:31.6862147Z","@mt":"收到OpenAPI文档刷新请求","@tr":"c6e28d299c6bd1e33131275bdc642e23","@sp":"3c2330116658006b","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNEAQ8M0FTD8:00000015","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:51:31.6937191Z","@mt":"开始刷新OpenAPI相关组件...","@tr":"c6e28d299c6bd1e33131275bdc642e23","@sp":"3c2330116658006b","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNEAQ8M0FTD8:00000015","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:51:31.6969340Z","@mt":"ActionDescriptor刷新完成","@tr":"c6e28d299c6bd1e33131275bdc642e23","@sp":"3c2330116658006b","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNEAQ8M0FTD8:00000015","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:51:31.6991990Z","@mt":"已通知控制器变更","@tr":"c6e28d299c6bd1e33131275bdc642e23","@sp":"3c2330116658006b","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNEAQ8M0FTD8:00000015","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:51:31.7018422Z","@mt":"OpenAPI组件刷新完成","@tr":"c6e28d299c6bd1e33131275bdc642e23","@sp":"3c2330116658006b","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","RequestId":"0HNEAQ8M0FTD8:00000015","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:51:31.7037658Z","@mt":"已使用简化服务刷新OpenAPI组件","@tr":"c6e28d299c6bd1e33131275bdc642e23","@sp":"3c2330116658006b","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","RequestId":"0HNEAQ8M0FTD8:00000015","RequestPath":"/api/openapi/refresh","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:51:35.4143776Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"af3e1a603f56dde4196cb96ad3e1fb1f","@sp":"10d9c61e382f4cf4","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ8M0FTD8:00000021","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:51:35.4193613Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"af3e1a603f56dde4196cb96ad3e1fb1f","@sp":"10d9c61e382f4cf4","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ8M0FTD8:00000021","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:51:35.4249983Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"af3e1a603f56dde4196cb96ad3e1fb1f","@sp":"10d9c61e382f4cf4","PathCount":29,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQ8M0FTD8:00000021","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQ8M0FTD8","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.0041659Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.0258144Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.0523092Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.1289155Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.1321073Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.1842579Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.1974953Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.2316493Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.2396289Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.4874563Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.4918679Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.5291696Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.5321363Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:23.5347618Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:24.7826873Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:24.7864584Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:24.7892890Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:24.7916195Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:24.7936449Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:25.3058997Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:25.3114843Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:25.3166694Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:25.3197967Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:25.3242934Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:25.9724490Z","@mt":"首次请求触发端点刷新","@tr":"1cffa76f83ca3017f277f97a39860518","@sp":"3873b686461daa5b","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQDOG0RK3:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQDOG0RK3","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:25.9792943Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"1cffa76f83ca3017f277f97a39860518","@sp":"3873b686461daa5b","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQDOG0RK3:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQDOG0RK3","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:26.4478574Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"b54a4bc03b6879c3c381caad2e8c8340","@sp":"a3c63df441d17bc6","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQDOG0RK3:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQDOG0RK3","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:26.4530713Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"b54a4bc03b6879c3c381caad2e8c8340","@sp":"a3c63df441d17bc6","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQDOG0RK3:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQDOG0RK3","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T16:59:26.4631442Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"b54a4bc03b6879c3c381caad2e8c8340","@sp":"a3c63df441d17bc6","PathCount":26,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQDOG0RK3:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQDOG0RK3","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:58.8362516Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:58.8593369Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:58.9708327Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:59.0215785Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:59.0243622Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:59.0762364Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:59.1264833Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:59.1286150Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:59.1323913Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":19,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:59.5489639Z","@mt":"Now listening on: {address}","address":"https://localhost:7090","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:59.5533793Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:59.5870847Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:59.5910550Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:08:59.5933978Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:00.6606334Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:00.6649581Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:00.6682293Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:00.6701735Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:00.6726283Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":10,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:01.1795892Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:01.1852566Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:01.1898804Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:01.1920447Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:01.1952977Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:01.3879296Z","@mt":"首次请求触发端点刷新","@tr":"ace64db53c709f00b5444de6bf3fea2c","@sp":"9a136c515e69bbce","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQJ3VQR1S:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQJ3VQR1S","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:01.3945368Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"ace64db53c709f00b5444de6bf3fea2c","@sp":"9a136c515e69bbce","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQJ3VQR1S:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQJ3VQR1S","MachineName":"DESKTOP-O211UJ1","ThreadId":5,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:01.7996666Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"af1b54beef243c4928f37c1d504570e7","@sp":"c4f7ef1b2a337d35","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQJ3VQR1S:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQJ3VQR1S","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:01.8039631Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"af1b54beef243c4928f37c1d504570e7","@sp":"c4f7ef1b2a337d35","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQJ3VQR1S:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQJ3VQR1S","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:09:01.8152203Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"af1b54beef243c4928f37c1d504570e7","@sp":"c4f7ef1b2a337d35","PathCount":24,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQJ3VQR1S:0000000B","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQJ3VQR1S","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:11.4838140Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:11.4837997Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:11.5260109Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:11.5412301Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:11.5429636Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:11.5742722Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:11.5792190Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:11.5803258Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:11.5830503Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:11.7109452Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:11.7140372Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:11.7150626Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:11.7165813Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:13.1137120Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:13.1160287Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:13.1181348Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:13.1190860Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:13.1199789Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:13.6276062Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:13.6312329Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:13.6329178Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:13.6336197Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:13.6346167Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:55.8593095Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:55.8642730Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:55.8881016Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:55.8974034Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:55.8989527Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:55.9227851Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:55.9262297Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:55.9270897Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:55.9294647Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:56.0236943Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:56.0256537Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:56.0264644Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:56.0272685Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:57.4484578Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:57.4504094Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:57.4517138Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:57.4528042Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:57.4536058Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:57.9554450Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:57.9584558Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:57.9604016Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:57.9620494Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:11:57.9629441Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:12.8284099Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:12.8231000Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:12.8492667Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:12.8598306Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:12.8611431Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:12.8840272Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:12.8869557Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:12.8878217Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:12.8908297Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:12.9855691Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:12.9877120Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:12.9889927Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:12.9903022Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:14.4137637Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:14.4150394Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:14.4160811Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:14.4167235Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:14.4174280Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:14.9235099Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:14.9259811Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:14.9278230Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:14.9285289Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:14:14.9293502Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:02.6985475Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:02.6960685Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:02.7233607Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:02.7346041Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:02.7360956Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:02.7590856Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:02.7636084Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:02.7646265Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:02.7669660Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:02.8696228Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:02.8721045Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:02.8729651Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:02.8741825Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:04.2833880Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:04.2851282Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:04.2862660Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:04.2870375Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:04.2876960Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:04.7919939Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:04.7944157Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:04.7963218Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:04.7971735Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:15:04.7979541Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":18,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:18.5934190Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:18.5933410Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:18.6217264Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:18.6349296Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:18.6360845Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:18.6644628Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:18.6680573Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:18.6690603Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:18.6716616Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:18.7684301Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:18.7705497Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:18.7715498Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:18.7724456Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:20.1875480Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:20.1889387Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:20.1900097Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:20.1906173Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:20.1912419Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:20.7031580Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:20.7080892Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:20.7131122Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:20.7161098Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:16:20.7175964Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:09.6903284Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:09.6840822Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:09.7114983Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:09.7239302Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:09.7252158Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:09.7515017Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:09.7549882Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:09.7561086Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:09.7597588Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:09.8531385Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:09.8548899Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:09.8563169Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:09.8574971Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:11.2841491Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:11.2859278Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:11.2878447Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:11.2885691Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:11.2891982Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:11.7900462Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:11.7929159Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:11.7949228Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:11.7958907Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:18:11.7968995Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:53.4704446Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:53.4795633Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:53.5064463Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:53.5179530Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:53.5191143Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:53.5539450Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:53.5597359Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:53.5613481Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:53.5690293Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:53.6892109Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:53.6916839Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:53.6928558Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:53.6935913Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:55.0790788Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:55.0810709Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:55.0822558Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:55.0830331Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:55.0839014Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:55.5895292Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:55.5922333Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:55.5942774Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:55.5951398Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:19:55.5959077Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:20:58.4175257Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:20:58.4185932Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:20:58.4484706Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:20:58.4598616Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:20:58.4621729Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:20:58.4921353Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:20:58.4974340Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:20:58.4996592Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:20:58.5025715Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:20:58.5983851Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:20:58.6007349Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:20:58.6021875Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:20:58.6031042Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:00.0319460Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:00.0335463Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:00.0349225Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:00.0359054Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:00.0367417Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:00.5372241Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:00.5395810Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:00.5414152Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:00.5421371Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:00.5429296Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:51.6763525Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:51.6793764Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:51.7073019Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:51.7165141Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:51.7177660Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:51.7423867Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:51.7468290Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:51.7478036Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:51.7505491Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:51.8560097Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:51.8580514Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:51.8590234Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:51.8598698Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:53.2684656Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:53.2713492Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:53.2723426Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:53.2729747Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:53.2736286Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:53.7816831Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:53.7845024Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:53.7868697Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:53.7874954Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:21:53.7887230Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:22:30.7121672Z","@mt":"首次请求触发端点刷新","@tr":"9b63c760857f87e56963bb84da59f709","@sp":"371c8b3bccf1ae7f","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQQLONBIV:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQQLONBIV","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:22:30.7228643Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"9b63c760857f87e56963bb84da59f709","@sp":"371c8b3bccf1ae7f","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQQLONBIV:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQQLONBIV","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:22:30.7296055Z","@mt":"Failed to determine the https port for redirect.","@l":"Warning","@tr":"9b63c760857f87e56963bb84da59f709","@sp":"371c8b3bccf1ae7f","EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEAQQLONBIV:00000001","RequestPath":"/scalar/v1","ConnectionId":"0HNEAQQLONBIV","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:22:31.2054707Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"9170a9190287224dba59699962700bde","@sp":"7a072da92d27828a","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQQLONBJ0:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQQLONBJ0","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:22:31.2103074Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"9170a9190287224dba59699962700bde","@sp":"7a072da92d27828a","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQQLONBJ0:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQQLONBJ0","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:22:31.2232405Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"9170a9190287224dba59699962700bde","@sp":"7a072da92d27828a","PathCount":24,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQQLONBJ0:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQQLONBJ0","MachineName":"DESKTOP-O211UJ1","ThreadId":22,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:16.0089541Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:16.0159778Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:16.0416715Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:16.0502972Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:16.0523479Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:16.0798389Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:16.0833442Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:16.0851342Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:16.0883749Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:16.1885435Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:16.1905473Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:16.1913677Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:16.1921539Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:17.6052547Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:17.6066345Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:17.6076920Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:17.6083167Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:17.6089473Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:18.1130013Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:18.1176897Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:18.1209073Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:18.1219776Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:24:18.1230276Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:00.9182119Z","@mt":"首次请求触发端点刷新","@tr":"2644e1f7ece76bad6d4a394be3878663","@sp":"fd5d3f77f2915edc","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQS2HH8N2:00000001","RequestPath":"/api/auth/role/123","ConnectionId":"0HNEAQS2HH8N2","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:00.9240247Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"2644e1f7ece76bad6d4a394be3878663","@sp":"fd5d3f77f2915edc","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQS2HH8N2:00000001","RequestPath":"/api/auth/role/123","ConnectionId":"0HNEAQS2HH8N2","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:00.9293734Z","@mt":"Failed to determine the https port for redirect.","@l":"Warning","@tr":"2644e1f7ece76bad6d4a394be3878663","@sp":"fd5d3f77f2915edc","EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEAQS2HH8N2:00000001","RequestPath":"/api/auth/role/123","ConnectionId":"0HNEAQS2HH8N2","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:19.7121103Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:19.7084749Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:19.7350162Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:19.7446755Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:19.7456291Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:19.7639130Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:19.7678687Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:19.7694875Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:19.7716131Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:19.8170977Z","@mt":"Now listening on: {address}","address":"http://localhost:5000","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:19.8199111Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:19.8209582Z","@mt":"Hosting environment: {EnvName}","EnvName":"Production","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:19.8220578Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:21.3006226Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:21.3036501Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:21.3051261Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:21.3059571Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:21.3071380Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:21.8087634Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:21.8120224Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:21.8144896Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\bin\\Debug\\net10.0\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:21.8152350Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:25:21.8159852Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":12,"EnvironmentName":"Production","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:48.3557710Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:48.3557709Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:48.3843566Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:48.3930369Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:48.3947248Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:48.4218715Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:48.4263884Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:48.4275374Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:48.4303499Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:48.5304816Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:48.5329430Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:48.5336684Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:48.5343641Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:49.9471720Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:49.9489560Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:49.9501671Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:49.9509841Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:49.9518175Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:50.4578413Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:50.4607595Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:50.4630106Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:50.4644066Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:26:50.4652640Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:54.8009472Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:54.7968740Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:54.8264470Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:54.8369771Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:54.8379381Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:54.8637147Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:54.8690272Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:54.8699770Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:54.8726133Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:54.9668032Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:54.9696471Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:54.9705315Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:54.9714668Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:56.4039512Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:56.4078377Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:56.4113879Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:56.4134276Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:56.4155070Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:56.9259831Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:56.9283138Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:56.9306417Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:56.9313694Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:27:56.9321988Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:18.5595215Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:18.5561385Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:18.5884340Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:18.6012282Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:18.6031723Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:18.6322064Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:18.6381223Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:18.6393802Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:18.6428107Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:18.7397148Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:18.7426479Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:18.7437050Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:18.7445894Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:20.1614498Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:20.1629473Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:20.1641454Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:20.1648157Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:20.1655579Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:20.6689718Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:20.6719190Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:20.6740139Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:20.6753369Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:29:20.6762393Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:30:09.2413770Z","@mt":"首次请求触发端点刷新","@tr":"c3adaa389f6f19d9a89fd72ea3b59df4","@sp":"698a86dbbd9d86e0","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQUUE1K6J:00000001","RequestPath":"/api/test-module-route","ConnectionId":"0HNEAQUUE1K6J","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:30:09.2458087Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"c3adaa389f6f19d9a89fd72ea3b59df4","@sp":"698a86dbbd9d86e0","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQUUE1K6J:00000001","RequestPath":"/api/test-module-route","ConnectionId":"0HNEAQUUE1K6J","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:30:09.2487706Z","@mt":"Failed to determine the https port for redirect.","@l":"Warning","@tr":"c3adaa389f6f19d9a89fd72ea3b59df4","@sp":"698a86dbbd9d86e0","EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEAQUUE1K6J:00000001","RequestPath":"/api/test-module-route","ConnectionId":"0HNEAQUUE1K6J","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:30:28.7576982Z","@mt":"开始转换OpenAPI文档以包含动态模块...","@tr":"3e5fdfbb66bb3a604ef7385c60a6df48","@sp":"b6b6f5a20a24ca88","SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQUUE1K6M:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQUUE1K6M","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:30:28.7605940Z","@mt":"发现 {Count} 个活跃模块程序集","@tr":"3e5fdfbb66bb3a604ef7385c60a6df48","@sp":"b6b6f5a20a24ca88","Count":2,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQUUE1K6M:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQUUE1K6M","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:30:28.7662401Z","@mt":"OpenAPI文档转换完成，当前包含 {PathCount} 个路径","@tr":"3e5fdfbb66bb3a604ef7385c60a6df48","@sp":"b6b6f5a20a24ca88","PathCount":24,"SourceContext":"SSIC.Infrastructure.OpenApi.DynamicModuleDocumentTransformer","RequestId":"0HNEAQUUE1K6M:00000002","RequestPath":"/openapi/v1.json","ConnectionId":"0HNEAQUUE1K6M","MachineName":"DESKTOP-O211UJ1","ThreadId":21,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:04.7798738Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:04.7843759Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:04.8082793Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:04.8179194Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:04.8190825Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:04.8451551Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:04.8499807Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:04.8508793Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:04.8534326Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:04.9482713Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:04.9510148Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:04.9518039Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:04.9525868Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:06.3783727Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:06.3811306Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:06.3826174Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:06.3836575Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:06.3846545Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:06.8876583Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:06.8905529Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:06.8924586Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:06.8946247Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:06.8955014Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:15.6738744Z","@mt":"首次请求触发端点刷新","@tr":"12cb7635c1ccacf66b5a9e440d5c6c37","@sp":"01986d0fe23167d6","SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQVH2SAFB:00000001","RequestPath":"/api/auth/role","ConnectionId":"0HNEAQVH2SAFB","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:15.6792705Z","@mt":"请求触发的端点刷新完成，发现 {Count} 个模块","@tr":"12cb7635c1ccacf66b5a9e440d5c6c37","@sp":"01986d0fe23167d6","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.Endpoints.EndpointRefreshMiddleware","RequestId":"0HNEAQVH2SAFB:00000001","RequestPath":"/api/auth/role","ConnectionId":"0HNEAQVH2SAFB","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:31:15.6841544Z","@mt":"Failed to determine the https port for redirect.","@l":"Warning","@tr":"12cb7635c1ccacf66b5a9e440d5c6c37","@sp":"01986d0fe23167d6","EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNEAQVH2SAFB:00000001","RequestPath":"/api/auth/role","ConnectionId":"0HNEAQVH2SAFB","MachineName":"DESKTOP-O211UJ1","ThreadId":9,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:12.8881215Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:12.8881706Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:12.9173455Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:12.9297243Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:12.9314521Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:12.9576153Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:12.9628172Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:12.9643747Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:12.9675328Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:13.0624448Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:13.0649349Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:13.0657486Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:13.0666776Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:14.4887022Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:14.4906642Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:14.4922545Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:14.4954923Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:14.4976983Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:15.0115852Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:15.0147144Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:15.0170273Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:15.0186593Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:32:15.0200765Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:31.1284583Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:31.1341452Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:31.1580197Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:31.1672905Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:31.1685820Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:31.1926929Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:31.1982028Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:31.1995351Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:31.2024831Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:31.2984764Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:31.3004900Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:31.3013705Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:31.3022205Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:32.7202648Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:32.7219964Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:32.7232198Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:32.7239780Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:32.7249203Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:33.2279161Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:33.2348418Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:33.2409373Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:33.2442219Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:33:33.2479933Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:13.5699267Z","@mt":"应用启动时已手动刷新路由端点，共 {Count} 个模块","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:13.5699267Z","@mt":"发现 {Count} 个模块文件","Count":2,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":13,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:13.5956514Z","@mt":"已强制刷新FixedEndpointDataSource","SourceContext":"SSIC.Infrastructure.Startups.HostBuilder","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:13.6048187Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:13.6057218Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:13.6314986Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Auth","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:13.6356435Z","@mt":"已配置模块服务: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:13.6366682Z","@mt":"成功加载插件: {Name}","Name":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:13.6422560Z","@mt":"已重建路由端点，模块 {AssemblyName} 已加载","AssemblyName":"SSIC.Modules.Sys","SourceContext":"SSIC.Infrastructure.Startups.HotReload.PluginManager","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:13.7357284Z","@mt":"Now listening on: {address}","address":"http://localhost:5246","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:13.7382557Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:13.7390765Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:13.7401886Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"F:\\SSIC\\Host\\SSIC.HostServer","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:15.1589155Z","@mt":"开始刷新OpenAPI相关组件...","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:15.1608141Z","@mt":"ActionDescriptor刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:15.1619694Z","@mt":"已通知控制器变更","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:15.1627563Z","@mt":"OpenAPI组件刷新完成","SourceContext":"SSIC.Infrastructure.Startups.HotReload.SimpleOpenApiRefreshService","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:15.1635042Z","@mt":"初始化完成，已刷新OpenAPI组件","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":16,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:15.6670654Z","@mt":"模块热重载初始化完成，请刷新Scalar页面查看API文档","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:15.6709580Z","@mt":"加载了 {Count} 个动态端点定义","Count":0,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:15.6727530Z","@mt":"开始监控目录: {Path}","Path":"F:\\SSIC\\Host\\SSIC.HostServer\\Modules","SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:15.6735883Z","@mt":"文件监控已启动，共监控 {Count} 个目录","Count":1,"SourceContext":"SSIC.Infrastructure.Startups.HotReload.FileWatcher","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-07-24T17:34:15.6745631Z","@mt":"热插拔服务已启动","SourceContext":"SSIC.Infrastructure.Startups.HotReload.HotReloadExtensions.HotReloadLogger","MachineName":"DESKTOP-O211UJ1","ThreadId":6,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
