﻿using SSIC.Infrastructure.Enums;

namespace SSIC.Infrastructure.ViewModel
{
    /// <summary>
    /// 权限输出表
    /// </summary>
    public class OutPermission
    {
        /// <summary>
        /// 权限名称
        /// </summary>
        public string name { get; set; }

        /// <summary>
        /// 权限代号
        /// </summary>
        public ActionPermissionOption code { get; set; }

        /// <summary>
        /// 按钮图标
        /// </summary>
        public string icon { get; set; }

        /// <summary>
        /// 按钮颜色
        /// </summary>
        public string color { get; set; }

        /// <summary>
        /// 多语言标签t
        /// </summary>
        public string titlekey { get; set; }
        /// <summary>
        /// 按钮类型0.弹窗,1.接口
        /// </summary>
        public int type { get; set; }
    }
}