using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.Endpoints
{
    /// <summary>
    /// 动态端点源接口，用于获取动态端点定义
    /// </summary>
    public interface IDynamicEndpointSource
    {
        /// <summary>
        /// 异步获取所有端点
        /// </summary>
        /// <returns>端点集合</returns>
        Task<IEnumerable<Endpoint>> GetEndpointsAsync();
        
        /// <summary>
        /// 监控端点定义变化
        /// </summary>
        /// <param name="callback">端点变化回调方法</param>
        void Watch(Action<IEnumerable<Endpoint>> callback);
    }
} 