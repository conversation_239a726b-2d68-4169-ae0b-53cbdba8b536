﻿using FreeSql;
using Microsoft.Extensions.Configuration;
using SSIC.Infrastructure.ConfigurableOptions;
using System.Collections.Generic;
using SSIC.Infrastructure.OptionsEntity;
namespace SSIC.Infrastructure.OptionsEntity
{
    /// <summary>
    /// 数据库配置类
    /// </summary>
    public class DbInfoOptions : IConfigurableOptions
    {
        /// <summary>
        /// 数据库地址
        /// </summary>
        public string dataservice { get; set; }

        /// <summary>
        /// 数据库端口号
        /// </summary>
        public string dataport { get; set; }

        /// <summary>
        /// 数据库类型
        /// SqlServer = 1,
        /// PostgreSQL = 2,
        /// Oracle = 3,
        /// Sqlite = 4,
        /// OdbcOracle = 5,
        /// OdbcSqlServer = 6,
        /// OdbcMySql = 7,
        /// OdbcPostgreSQL = 8,
        /// Odbc = 9,
        /// OdbcDamning = 10,
        /// MsAccess = 11,
        /// Dameng = 12,
        /// OdbcKingbaseES = 13,
        /// ShenTong = 14,
        /// KingbaseES = 15,
        /// Firebird = 16
        /// </summary>
        public DataType datatype { get; set; }

        /// <summary>
        /// 数据库名称
        /// </summary>
        public string dataname { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string datauid { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string datapwd { get; set; }

        //是否启用saas模式,该项针对客户端
        public bool saasmodel { get; set; } = false;

        /// <summary>
        /// 租户ID
        /// </summary>
        public string tenantid { get; set; }

        /// <summary>
        /// 数据库地址
        /// </summary>
        public string ComposeUrl
        {
            get
            {
                var _sqlurl = "";
                switch (datatype)
                {
                    case DataType.MySql:
                        _sqlurl = $"Data Source={dataservice};Port={dataport};User ID={datauid};Password={datapwd}; Initial Catalog={dataname};Charset=utf8; SslMode=none;Min pool size=1";
                        break;

                    case DataType.SqlServer:
                        _sqlurl = $"Data Source={dataservice},{dataport};User Id={datauid};Password={datapwd};Initial Catalog={dataname};Pooling=true;Min Pool Size=1";
                        break;

                    case DataType.PostgreSQL:
                        _sqlurl = $"Host={dataservice};Port={dataport};Username={datauid};Password={datapwd}; Database={dataname};Pooling=true;Minimum Pool Size=1";
                        break;

                    case DataType.Oracle:
                        _sqlurl = $"user id={datauid};password={datapwd}; data source={dataservice}:{dataport}/{dataname};Pooling=true;Min Pool Size=1";
                        break;

                    case DataType.Sqlite://单文件数据库
                        _sqlurl = $"Data Source={dataservice}; Attachs={dataservice}; Pooling=true;Min Pool Size=1";
                        break;

                    case DataType.OdbcOracle:
                        _sqlurl = "Driver={Oracle in XE};" + $"Server={dataservice}:{dataport}/{dataname}; Persist Security Info=False; Trusted_Connection=Yes;UID={datauid};PWD={datapwd}; Min Pool Size=1";
                        break;

                    case DataType.OdbcSqlServer:
                        _sqlurl = "Driver={SQL Server};" + $"Server={dataservice},{dataport};Persist Security Info=False;UID={datauid};PWD={datapwd}; Trusted_Connection=Yes;Integrated Security=True; DATABASE={dataname}; Pooling=true;Min Pool Size=1";
                        break;

                    case DataType.OdbcMySql:
                        _sqlurl = "Driver={MySQL ODBC 8.0 Unicode Driver};" + $" Server={dataservice};Port={dataport};Persist Security Info=False; Trusted_Connection=Yes;UID={datauid};PWD={datapwd}; DATABASE={dataname};Charset=utf8; SslMode=none;Min Pool Size=1";
                        break;

                    case DataType.OdbcPostgreSQL:
                        _sqlurl = "Driver={PostgreSQL Unicode(x64)};" + $"Server={dataservice}; Port={dataport};UID={datauid};PWD={datapwd}; Database={dataname};Pooling=true;Min Pool Size=1";
                        break;

                    case DataType.Odbc:
                        _sqlurl = "Driver={SQL Server};" + $"Server={dataservice},{dataport};Persist Security Info=False; UID={datauid};PWD={datapwd}; Trusted_Connection=Yes;Integrated Security=True; DATABASE={dataname}; Pooling=true;Min pool size=1";
                        break;
                     
                    //case DataType.OdbcDameng:
                    //    _sqlurl = "Driver={DM8 ODBC DRIVER};" + $"Server={dataservice};TCP_PORT={dataport}, Persist Security Info=False; Trusted_Connection=Yes; DSN={dataname}; UID={datauid};PWD={datapwd}";
                    //    break;

                    case DataType.MsAccess://本地数据库,单文件
                        _sqlurl = $"Provider=Microsoft.Jet.OleDb.4.0;Data Source={dataservice}";
                        break;

                    case DataType.Dameng:
                        _sqlurl = $"server={dataservice};port={dataport};user id={datauid};password={datapwd};database={dataname};poolsize=5";
                        break;

                    //case DataType.OdbcKingbaseES:
                    //    _sqlurl = "Driver={KingbaseES 8.2 ODBC Driver ANSI};" + $"Server={dataservice};Port={dataport};UID={datauid};PWD={datapwd};database={dataname}";
                    //    break;

                    case DataType.ShenTong:
                        _sqlurl = $"HOST={dataservice};PORT={dataport};DATABASE={dataname};USERNAME={datauid};PASSWORD={datapwd};MAXPOOLSIZE=2";
                        break;

                    case DataType.KingbaseES:
                        _sqlurl = "Driver={KingbaseES 8.2 ODBC Driver ANSI};" + $"Server={dataservice};Port={dataport};UID={datauid};PWD={datapwd};database={dataname}";
                        break;

                    case DataType.Firebird:
                        _sqlurl = $"User={datauid}; Password={datapwd}; Database={dataname}.fdb; DataSource={dataservice}; Port={dataport}; Dialect=3; Charset=NONE; Role=; Connection lifetime=15; Pooling=true; MinPoolSize=0; MaxPoolSize=50; Packet Size=8192; ServerType=0";
                        break;
                }

                return _sqlurl;
            }
        }

        /// <summary>
        ///租户详细
        /// </summary>
        public List<TenantList> tenantLists { get; set; } = new List<TenantList>();

        public void OnListener(DbInfoOptions options, IConfiguration configuration)
        {
            tenantLists = options.tenantLists;
        }

        public void PostConfigure(DbInfoOptions options, IConfiguration configuration)
        {
            tenantLists = options.tenantLists;
        }
    }
}