﻿using System;

namespace SSIC.Infrastructure.Entity.EntityBase.Composition
{
    /// <summary>
    /// 汇率，税率
    /// </summary>
    public class RateBasic : EntityBasic, IEntityRate
    {
        /// <summary>
        /// 币别
        /// </summary>
        public Guid? currencyid { get; set; }

        /// <summary>
        /// 汇率
        /// </summary>
        public decimal? exchangerate { get; set; }

        /// <summary>
        /// 税别代号
        /// </summary>
        public string taxcode { get; set; }

        /// <summary>
        /// 税别类型
        /// </summary>
        public int? taxtype { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? taxrate { get; set; }
    }
}