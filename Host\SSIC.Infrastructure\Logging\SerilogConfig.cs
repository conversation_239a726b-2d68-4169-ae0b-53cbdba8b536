﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Sinks.OpenTelemetry;
using System;
using System.Collections.Generic;

namespace SSIC.Infrastructure.Logging
{
    public static class SerilogConfig
    {
        // 1. Host 阶段接管日志,系统级日志
        public static void UseSerilogLogging(this ConfigureHostBuilder hostBuilder, IConfiguration configuration)
        {
            hostBuilder.UseSerilog((hostingContext, services, loggerConfiguration) =>
            {
                loggerConfiguration
                    .ReadFrom.Configuration(configuration)
                    .Enrich.FromLogContext()
                    .WriteTo.Console()
                    .WriteTo.OpenTelemetry(opt =>
                    {
                        opt.Endpoint = "http://localhost:4317";
                        opt.Protocol = OtlpProtocol.Grpc;
                        opt.ResourceAttributes = new Dictionary<string, object>
                        {
                            ["service.name"] = configuration["ServiceName"] ?? "default-service",
                            ["deployment.environment"] = hostingContext.HostingEnvironment.EnvironmentName
                        };
                        opt.BatchingOptions.BatchSizeLimit = 10;
                        opt.BatchingOptions.BufferingTimeLimit = TimeSpan.FromSeconds(2);
                    });
            });
        }

        // 2. Service 层注册日志，应用内部依赖注入能用日志
        public static void AddSerilogLogging(this IServiceCollection services, IConfiguration configuration)
        {
            var logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .Enrich.FromLogContext()
                .CreateLogger();

            services.AddLogging(loggingBuilder =>
            {
                loggingBuilder.ClearProviders();
                loggingBuilder.AddSerilog(logger, dispose: true);
            });

            services.AddSingleton<Serilog.ILogger>(logger); // （可选）直接注入 Serilog 的 Logger
        }
    }
}
