﻿using FreeSql.Internal.Model;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.BaseProvider
{
    public interface IServiceBase<TEntity> where TEntity : class
    {
        /// <summary>
        /// 根据条件表达式删除,返回条数
        /// </summary>
        /// <param name="predicate">表达式</param>
        /// <returns></returns>
        Task<int> DeleteAsync(Expression<Func<TEntity, bool>> predicate);

        /// <summary>
        /// 根据实体进行删除
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task<int> DeleteAsync(TEntity entity);

        /// <summary>
        /// 根据实体进行增加数据并返回实体
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task<TEntity> InsertAsync(TEntity entity);

        /// <summary>
        /// 批量插入数据,并返回实体集合
        /// </summary>
        /// <param name="entitys"></param>
        /// <returns></returns>
        Task<List<TEntity>> InsertAsyncList(List<TEntity> entitys);

        /// <summary>
        /// 根据实体ID进行数据更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task<int> UpdateAsync(TEntity entity);

        /// <summary>
        /// 根据实体集合进行数据更新并返回影响条数
        /// </summary>
        /// <param name="entitys"></param>
        /// <returns></returns>
        Task<int> UpdateAsync(List<TEntity> entitys);

        /// <summary>
        /// 动态更新
        /// </summary>
        /// <param name="columns">要更新的字段</param>
        /// <param name="whereExpression">条件表达式</param>
        /// <returns></returns>
        Task<int> UpdateAsync(Expression<Func<TEntity, TEntity>> columns, Expression<Func<TEntity, bool>> whereExpression);

        /// <summary>
        /// 返回指定字段数组
        /// </summary>
        /// <param name="columns">指定字段</param>
        /// <param name="whereExpression">条件表达式</param>
        /// <param name="strOrderByFileds">排序字段</param>
        /// <param name="IsDesc">排序方式true为desc,false为asc</param>
        /// <returns></returns>
        Task<List<Expression<Func<TEntity, TEntity>>>> FindOneColumns(Expression<Func<TEntity, TEntity>> columns, Expression<Func<TEntity, bool>> whereExpression, Expression<Func<TEntity, object>> strOrderByFileds = null, bool IsDesc = false);

        /// <summary>
        /// 根据条件返回整表数据
        /// </summary>
        /// <param name="whereExpression">查询条件Lambda</param>
        /// <param name="strOrderByFileds">排序字段</param>
        /// <param name="IsDesc">排序方式true为desc,false为asc</param>
        /// <returns></returns>
        Task<(long, List<TEntity>)> FindAllList(Expression<Func<TEntity, bool>> whereExpression, Expression<Func<TEntity, object>> strOrderByFileds = null, bool IsDesc = false);

        /// <summary>
        /// 查询第一条数据
        /// </summary>
        /// <param name="whereExpression"></param>
        /// <returns></returns>
        Task<TEntity> FirstAsync(Expression<Func<TEntity, bool>> whereExpression = null);

        /// <summary>
        /// 分页查询数据
        /// </summary>
        /// <param name="whereExpression">查询条件Lambda</param>
        /// <param name="intPageIndex">起始页</param>
        /// <param name="intPageSize">条数</param>
        /// <param name="strOrderByFileds">排序字段</param>
        /// <param name="IsDesc">排序方式true为desc,false为asc</param>
        /// <returns></returns>

        Task<(long, List<TEntity>)> QueryPage(Expression<Func<TEntity, bool>> whereExpression, int intPageIndex, int intPageSize, Expression<Func<TEntity, object>> strOrderByFileds = null, bool IsDesc = false);

        /// <summary>
        /// 分页查询数据
        /// </summary>
        /// <param name="whereExpression">查询条件Lambda</param>
        /// <param name="intPageIndex">起始页</param>
        /// <param name="intPageSize">条数</param>
        /// <param name="strOrderByFileds">排序字段</param>
        /// <param name="IsDesc">排序方式true为desc,false为asc</param>
        /// <returns></returns>
        Task<(long, List<TEntity>)> QueryPageStr(Expression<Func<TEntity, bool>> whereExpression, int intPageIndex,
            int intPageSize, string strOrderByFileds = null, bool IsDesc = false);

        /// <summary>
        /// 分页查询数据
        /// </summary>
        /// <param name="dynamicFilterInfo">动态过滤条件</param>
        /// <param name="intPageIndex">起始页</param>
        /// <param name="intPageSize">条数</param>
        /// <param name="strOrderByFileds">排序字段</param>
        /// <param name="IsDesc">排序方式true为desc,false为asc</param>

        Task<(long, List<TEntity>)> QueryPageDyn(DynamicFilterInfo dynamicFilterInfo, int intPageIndex,
            int intPageSize, Expression<Func<TEntity, object>> strOrderByFileds = null, bool IsDesc = false);

        /// <summary>
        /// 分页查询数据
        /// </summary>
        /// <param name="dynamicFilterInfo">动态过滤条件</param>
        /// <param name="intPageIndex">起始页</param>
        /// <param name="intPageSize">条数</param>
        /// <param name="strOrderByFileds">排序字段</param>
        /// <param name="IsDesc">排序方式true为desc,false为asc</param>
        Task<(long, List<TEntity>)> QueryPageDynStr(DynamicFilterInfo dynamicFilterInfo, int intPageIndex,
            int intPageSize, string strOrderByFileds = null, bool IsDesc = false);
    }
}