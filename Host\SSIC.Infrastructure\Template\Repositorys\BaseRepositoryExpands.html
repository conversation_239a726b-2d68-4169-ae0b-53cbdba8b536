﻿/*
*代码由框架生成,任何更改都可能导致被代码生成器覆盖
*Repository提供数据库操作，如果要增加数据库操作请在当前目录下Expands文件夹{EntityName}Repository编写代码
*/
using SSFB.Business.{BusinessName}.IRepository;
using SSFB.Core.BaseProvider;
using SSFB.Core.DependencyInjection.Dependencies;
using SSFB.Entity.{BusinessName};

namespace SSFB.Business.{BusinessName}.Repository
{
  public partial class {EntityName}Repository: RepositoryBase<{EntityName}>
    ,I{EntityName}Repository
    {
    public {EntityName}Repository(IFreeSql fsql) : base(fsql)
    {
    }
    }
    }