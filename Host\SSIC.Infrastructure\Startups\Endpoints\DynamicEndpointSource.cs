using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Routing.Patterns;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.Startups.Endpoints
{
    /// <summary>
    /// 动态端点源实现，从JSON文件读取端点定义并监控文件变化
    /// </summary>
    public class DynamicEndpointSource : IDynamicEndpointSource
    {
        private readonly IFileProvider _fileProvider;
        private readonly string _endpointsPath;
        private readonly ILogger<DynamicEndpointSource> _logger;
        private IEnumerable<Endpoint> _cachedEndpoints;
        private List<Action<IEnumerable<Endpoint>>> _watchers = new List<Action<IEnumerable<Endpoint>>>();

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="fileProvider">文件提供程序</param>
        /// <param name="endpointsPath">端点定义JSON文件所在目录</param>
        public DynamicEndpointSource(IFileProvider fileProvider, string endpointsPath)
        {
            _fileProvider = fileProvider ?? throw new ArgumentNullException(nameof(fileProvider));
            _endpointsPath = endpointsPath ?? "Endpoints";
            
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            _logger = loggerFactory.CreateLogger<DynamicEndpointSource>();
            
            // 初始化文件监控
            StartFileWatching();
        }

        /// <summary>
        /// 异步获取所有端点
        /// </summary>
        /// <returns>端点集合</returns>
        public async Task<IEnumerable<Endpoint>> GetEndpointsAsync()
        {
            if (_cachedEndpoints != null)
            {
                return _cachedEndpoints;
            }
            
            var endpoints = new List<Endpoint>();
            var directoryContents = _fileProvider.GetDirectoryContents(_endpointsPath);
            
            if (!directoryContents.Exists)
            {
                _logger.LogWarning("端点定义目录不存在: {Path}", _endpointsPath);
                return endpoints;
            }
            
            foreach (var file in directoryContents.Where(f => f.Name.EndsWith(".json")))
            {
                try
                {
                    await using var stream = file.CreateReadStream();
                    using var reader = new StreamReader(stream);
                    var json = await reader.ReadToEndAsync();
                    
                    var endpointDefinitions = JsonSerializer.Deserialize<EndpointDefinition[]>(json, 
                        new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    
                    if (endpointDefinitions != null)
                    {
                        foreach (var definition in endpointDefinitions)
                        {
                            try
                            {
                                if (string.IsNullOrEmpty(definition.RoutePattern))
                                {
                                    _logger.LogWarning("端点定义缺少路由模式: {File}", file.Name);
                                    continue;
                                }
                                
                                var routePattern = RoutePatternFactory.Parse(definition.RoutePattern);
                                var displayName = definition.DisplayName ?? definition.RoutePattern;
                                
                                // 创建空的元数据集合
                                var metadata = new EndpointMetadataCollection();
                                
                                // 设置请求委托
                                RequestDelegate handler = async context =>
                                {
                                    context.Response.StatusCode = 200;
                                    await context.Response.WriteAsync($"动态端点: {displayName}");
                                };
                                
                                // 创建RouteEndpoint
                                var endpoint = new RouteEndpoint(
                                    handler,
                                    routePattern,
                                    definition.Order ?? 0,
                                    metadata,
                                    displayName
                                );
                                
                                endpoints.Add(endpoint);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "创建端点时出错: {RoutePattern}", definition.RoutePattern);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "解析端点定义文件时出错: {File}", file.Name);
                }
            }
            
            _cachedEndpoints = endpoints;
            return endpoints;
        }

        /// <summary>
        /// 监控端点定义变化
        /// </summary>
        /// <param name="callback">端点变化回调方法</param>
        public void Watch(Action<IEnumerable<Endpoint>> callback)
        {
            if (callback != null)
            {
                _watchers.Add(callback);
            }
        }

        private void StartFileWatching()
        {
            // 监控目录变化
            ChangeToken.OnChange(
                () => _fileProvider.Watch($"{_endpointsPath}/**/*.json"),
                async () =>
                {
                    _logger.LogInformation("检测到端点定义文件变化");
                    
                    // 清除缓存并重新加载
                    _cachedEndpoints = null;
                    var endpoints = await GetEndpointsAsync();
                    
                    // 通知所有观察者
                    foreach (var watcher in _watchers)
                    {
                        try
                        {
                            watcher(endpoints);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "通知端点变化观察者时出错");
                        }
                    }
                }
            );
            
            _logger.LogInformation("已启动端点定义文件监控: {Path}", _endpointsPath);
        }
    }

    /// <summary>
    /// 端点定义类
    /// </summary>
    public class EndpointDefinition
    {
        /// <summary>
        /// 路由模式
        /// </summary>
        public string RoutePattern { get; set; }
        
        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName { get; set; }
        
        /// <summary>
        /// 顺序（优先级）
        /// </summary>
        public int? Order { get; set; }
    }
} 