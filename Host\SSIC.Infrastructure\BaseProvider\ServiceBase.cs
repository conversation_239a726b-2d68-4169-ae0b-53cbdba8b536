﻿using FreeSql;
using FreeSql.Internal.Model;
using SSIC.Infrastructure.Entity;
using SSIC.Infrastructure.DependencyInjection.Interface;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace SSIC.Infrastructure.BaseProvider
{
    public class ServiceBase<TEntity> : IScoped, IServiceBase<TEntity> where TEntity : EntityBasic, new()
    {
        protected IBaseRepository<TEntity> _fsqlRepository;

        /// <summary>
        /// 是否禁用全局过滤器
        /// </summary>
        protected bool DisGlobalFilter = false;

        //全局过滤器参数
        protected string[] DisGlobalFilterName = { };

        private List<TEntity> data;
        private long total;

        public ServiceBase(IFreeSql fsql)
        {
            if (fsql == null) throw new NullReferenceException("fsql 参数不可为空");
            _fsqlRepository = fsql.GetRepository<TEntity>();
        }

        /// <summary>
        /// 根据条件表达式删除,返回条数
        /// </summary>
        /// <param name="predicate">表达式</param>
        /// <returns></returns>
        public Task<int> DeleteAsync(Expression<Func<TEntity, bool>> predicate)
        {
            return _fsqlRepository.DeleteAsync(predicate);
        }

        /// <summary>
        /// 根据实体进行删除
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public Task<int> DeleteAsync(TEntity entity)
        {
            return _fsqlRepository.DeleteAsync(entity);
        }

        /// <summary>
        /// 根据条件返回整表数据
        /// </summary>
        /// <param name="whereExpression">查询条件Lambda</param>
        /// <param name="strOrderByFileds">排序字段</param>
        /// <param name="IsDesc">排序方式true为desc,false为asc</param>
        /// <returns></returns>
        public async Task<(long, List<TEntity>)> FindAllList(Expression<Func<TEntity, bool>> whereExpression, Expression<Func<TEntity, object>> strOrderByFileds = null, bool IsDesc = false)
        {
            if (DisGlobalFilter)
            {
                data = await _fsqlRepository.Select.DisableGlobalFilter(DisGlobalFilterName).Where(n => n.isdelete == false || n.isdelete == null)
                    .WhereIf(whereExpression != null, whereExpression).OrderByIf(strOrderByFileds != null,
                        strOrderByFileds,
                        IsDesc).Count(out total).ToListAsync();
                return (total, data);
            }
            data = await _fsqlRepository.Select.Where(n => n.isdelete == false || n.isdelete == null)
                .WhereIf(whereExpression != null, whereExpression).OrderByIf(strOrderByFileds != null,
                    strOrderByFileds,
                    IsDesc).Count(out total).ToListAsync();
            return (total, data);
        }

        /// <summary>
        /// 查询第一条数据
        /// </summary>
        /// <param name="whereExpression"></param>
        /// <returns></returns>
        public Task<TEntity> FirstAsync(Expression<Func<TEntity, bool>> whereExpression = null)
        {
            if (DisGlobalFilter)
            {
                return _fsqlRepository.Select.DisableGlobalFilter(DisGlobalFilterName).Where(n => n.isdelete == false || n.isdelete == null).WhereIf(whereExpression != null, whereExpression).FirstAsync();
            }

            return _fsqlRepository.Select.Where(n => n.isdelete == false || n.isdelete == null).WhereIf(whereExpression != null, whereExpression).FirstAsync();
        }

        /// <summary>
        /// 返回指定字段数组
        /// </summary>
        /// <param name="columns">指定字段</param>
        /// <param name="whereExpression">条件表达式</param>
        /// <param name="strOrderByFileds">排序字段</param>
        /// <param name="IsDesc">排序方式true为desc,false为asc</param>
        /// <returns></returns>
        public Task<List<Expression<Func<TEntity, TEntity>>>> FindOneColumns(Expression<Func<TEntity, TEntity>> columns,
            Expression<Func<TEntity, bool>> whereExpression,
            Expression<Func<TEntity, object>> strOrderByFileds = null, bool IsDesc = false)
        {
            if (DisGlobalFilter)
            {
                return _fsqlRepository.Select.DisableGlobalFilter(DisGlobalFilterName).WhereIf(whereExpression != null, whereExpression).OrderByIf(
                    strOrderByFileds != null,
                    strOrderByFileds, IsDesc).ToListAsync(a => columns);
            }

            return _fsqlRepository.Select.WhereIf(whereExpression != null, whereExpression).OrderByIf(strOrderByFileds != null,
                strOrderByFileds, IsDesc).ToListAsync(a => columns);
        }

        /// <summary>
        /// 根据实体进行增加数据并返回实体
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public Task<TEntity> InsertAsync(TEntity entity)
        {
            return _fsqlRepository.InsertAsync(entity);
        }

        /// <summary>
        /// 批量插入数据,并返回实体集合
        /// </summary>
        /// <param name="entitys"></param>
        /// <returns></returns>
        public Task<List<TEntity>> InsertAsyncList(List<TEntity> entitys) { return _fsqlRepository.InsertAsync(entitys); }

        /// <summary>
        /// 分页查询数据
        /// </summary>
        /// <param name="whereExpression">查询条件Lambda</param>
        /// <param name="intPageIndex">起始页</param>
        /// <param name="intPageSize">条数</param>
        /// <param name="strOrderByFileds">排序字段</param>
        /// <param name="IsDesc">排序方式true为desc,false为asc</param>
        /// <returns></returns>
        public async Task<(long, List<TEntity>)> QueryPage(Expression<Func<TEntity, bool>> whereExpression, int intPageIndex, int intPageSize, Expression<Func<TEntity, object>> strOrderByFileds = null, bool IsDesc = false)
        {
            if (DisGlobalFilter)
            {
                data = await _fsqlRepository.Select.DisableGlobalFilter(DisGlobalFilterName).Where(n => n.isdelete == false || n.isdelete == null).WhereIf(whereExpression != null, whereExpression).OrderByIf(strOrderByFileds != null, strOrderByFileds, IsDesc).Count(out total).Page(intPageIndex, intPageSize).ToListAsync();
                return (total, data);
            }
            data = await _fsqlRepository.Select.Where(n => n.isdelete == false || n.isdelete == null).WhereIf(whereExpression != null, whereExpression).OrderByIf(strOrderByFileds != null, strOrderByFileds, IsDesc).Count(out total).Page(intPageIndex, intPageSize).ToListAsync();
            return (total, data);
        }

        /// <summary>
        /// 分页查询数据
        /// </summary>
        /// <param name="whereExpression">查询条件Lambda</param>
        /// <param name="intPageIndex">起始页</param>
        /// <param name="intPageSize">条数</param>
        /// <param name="strOrderByFileds">排序字段</param>
        /// <param name="IsDesc">排序方式true为desc,false为asc</param>
        /// <returns></returns>
        public async Task<(long, List<TEntity>)> QueryPageStr(Expression<Func<TEntity, bool>> whereExpression, int intPageIndex, int intPageSize, string strOrderByFileds = null, bool IsDesc = false)
        {
            if (DisGlobalFilter)
            {
                data = await _fsqlRepository.Select.DisableGlobalFilter(DisGlobalFilterName).Where(n => n.isdelete == false || n.isdelete == null)
                    .WhereIf(whereExpression != null, whereExpression)
                    .OrderByPropertyNameIf(strOrderByFileds != null, strOrderByFileds, IsDesc).Count(out total)
                    .Page(intPageIndex, intPageSize).ToListAsync();
                return (total, data);
            }
            data = await _fsqlRepository.Select.Where(n => n.isdelete == false || n.isdelete == null)
                .WhereIf(whereExpression != null, whereExpression)
                .OrderByPropertyNameIf(strOrderByFileds != null, strOrderByFileds, IsDesc).Count(out total)
                .Page(intPageIndex, intPageSize).ToListAsync();
            return (total, data);
        }

        /// <summary>
        /// 分页查询数据
        /// </summary>
        /// <param name="dynamicFilterInfo">动态过滤条件</param>
        /// <param name="intPageIndex">起始页</param>
        /// <param name="intPageSize">条数</param>
        /// <param name="strOrderByFileds">排序字段</param>
        /// <param name="IsDesc">排序方式true为desc,false为asc</param>

        public async Task<(long, List<TEntity>)> QueryPageDyn(DynamicFilterInfo dynamicFilterInfo, int intPageIndex,
            int intPageSize, Expression<Func<TEntity, object>> strOrderByFileds = null, bool IsDesc = false)
        {
            if (DisGlobalFilter)
            {
                data = await _fsqlRepository.Select.DisableGlobalFilter(DisGlobalFilterName).Where(n => n.isdelete == false || n.isdelete == null)
                    .WhereDynamicFilter(dynamicFilterInfo).OrderByIf(strOrderByFileds != null, strOrderByFileds, IsDesc)
                    .Count(out total).Page(intPageIndex, intPageSize).ToListAsync();
                return (total, data);
            }
            data = await _fsqlRepository.Select.Where(n => n.isdelete == false || n.isdelete == null)
                .WhereDynamicFilter(dynamicFilterInfo).OrderByIf(strOrderByFileds != null, strOrderByFileds, IsDesc)
                .Count(out total).Page(intPageIndex, intPageSize).ToListAsync();
            return (total, data);
        }

        /// <summary>
        /// 分页查询数据
        /// </summary>
        /// <param name="dynamicFilterInfo">动态过滤条件</param>
        /// <param name="intPageIndex">起始页</param>
        /// <param name="intPageSize">条数</param>
        /// <param name="strOrderByFileds">排序字段</param>
        /// <param name="IsDesc">排序方式true为desc,false为asc</param>
        public async Task<(long, List<TEntity>)> QueryPageDynStr(DynamicFilterInfo dynamicFilterInfo, int intPageIndex,
            int intPageSize, string strOrderByFileds = null, bool IsDesc = false)
        {
            if (DisGlobalFilter)
            {
                data = await _fsqlRepository.Select.DisableGlobalFilter(DisGlobalFilterName).Where(n => n.isdelete == false || n.isdelete == null).WhereDynamicFilter(dynamicFilterInfo).OrderByPropertyNameIf(strOrderByFileds != null, strOrderByFileds, IsDesc).Count(out total).Page(intPageIndex, intPageSize).ToListAsync();
                return (total, data);
            }

            data = await _fsqlRepository.Select.Where(n => n.isdelete == false || n.isdelete == null).WhereDynamicFilter(dynamicFilterInfo).OrderByPropertyNameIf(strOrderByFileds != null, strOrderByFileds, IsDesc).Count(out total).Page(intPageIndex, intPageSize).ToListAsync();
            return (total, data);
        }

        /// <summary>
        /// 根据实体ID进行数据更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public Task<int> UpdateAsync(TEntity entity)
        {
            return _fsqlRepository.UpdateAsync(entity);
        }

        /// <summary>
        /// 根据实体集合进行数据更新并返回影响条数
        /// </summary>
        /// <param name="entitys"></param>
        /// <returns></returns>
        public Task<int> UpdateAsync(List<TEntity> entitys)
        { return _fsqlRepository.UpdateAsync(entitys); }

        /// <summary>
        /// 动态更新
        /// </summary>
        /// <param name="columns">要更新的字段</param>
        /// <param name="whereExpression">条件表达式</param>
        /// <returns></returns>
        public Task<int> UpdateAsync(Expression<Func<TEntity, TEntity>> columns, Expression<Func<TEntity, bool>> whereExpression)
        {
            return _fsqlRepository.UpdateDiy.Set(columns).WhereIf(whereExpression != null, whereExpression).ExecuteAffrowsAsync();
        }
    }
}