{"@t":"2025-04-09T10:30:25.0777309Z","@mt":"Now listening on: {address}","address":"https://localhost:63091","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:25.1249430Z","@mt":"Now listening on: {address}","address":"http://localhost:63092","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:25.2374882Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:25.2411694Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:25.2414034Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\Saisi\\SSIC\\Host\\SSIC.DevTools","SourceContext":"Microsoft.Hosting.Lifetime","MachineName":"DESKTOP-O211UJ1","ThreadId":1,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:29.9256084Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":47.8962,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
{"@t":"2025-04-09T10:30:50.0056848Z","@mt":"Execution attempt. Source: '{PipelineName}/{PipelineInstance}/{StrategyName}', Operation Key: '{OperationKey}', Result: '{Result}', Handled: '{Handled}', Attempt: '{Attempt}', Execution Time: {ExecutionTimeMs}ms","PipelineName":"-standard","PipelineInstance":"","StrategyName":"Standard-Retry","OperationKey":null,"Result":202,"Handled":false,"Attempt":0,"ExecutionTimeMs":8.1587,"EventId":{"Id":3,"Name":"ExecutionAttempt"},"SourceContext":"Polly","HttpMethod":"POST","Uri":"http://localhost:9411/api/v2/spans","Scope":["HTTP POST http://localhost:9411/api/v2/spans"],"MachineName":"DESKTOP-O211UJ1","ThreadId":15,"EnvironmentName":"Development","Application":"SSIC","Environment":"Development"}
